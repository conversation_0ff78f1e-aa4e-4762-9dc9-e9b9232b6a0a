/**
 * 批量修复剩余的交互组件
 */

const fs = require('fs');
const path = require('path');

const componentsToFix = [
  'PhysicsGrabComponent.ts',
  'ThrowableComponent.ts', 
  'XRGrabComponent.ts'
];

const componentsDir = 'src/interaction/components';

function addCreateInstanceMethod(filePath, className) {
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 检查是否已经有 createInstance 方法
  if (content.includes('createInstance')) {
    console.log(`${className} already has createInstance method`);
    return;
  }
  
  // 查找文件结尾的 }
  const lastBraceIndex = content.lastIndexOf('}');
  if (lastBraceIndex === -1) {
    console.log(`Could not find closing brace in ${className}`);
    return;
  }
  
  // 添加 createInstance 方法
  const methodToAdd = `
  /**
   * 创建组件实例
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new ${className}();
  }

  /**
   * 序列化自定义数据
   * @returns 自定义数据
   */
  protected serializeData(): any {
    return {};
  }

  /**
   * 反序列化自定义数据
   * @param data 自定义数据
   */
  protected deserializeData(data: any): void {
    // 子类可以重写此方法
  }
`;

  // 插入方法
  const newContent = content.slice(0, lastBraceIndex) + methodToAdd + '\n' + content.slice(lastBraceIndex);
  
  fs.writeFileSync(filePath, newContent, 'utf8');
  console.log(`Added createInstance method to ${className}`);
}

// 修复每个组件
componentsToFix.forEach(fileName => {
  const filePath = path.join(componentsDir, fileName);
  const className = fileName.replace('.ts', '');
  
  if (fs.existsSync(filePath)) {
    addCreateInstanceMethod(filePath, className);
  } else {
    console.log(`File not found: ${filePath}`);
  }
});

console.log('Batch fix completed!');

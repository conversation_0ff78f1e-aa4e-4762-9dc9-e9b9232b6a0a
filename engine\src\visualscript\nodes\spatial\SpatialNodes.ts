/**
 * 空间信息系统视觉脚本节点
 * 为视觉脚本系统提供空间信息相关的节点
 */
import { FlowNode } from '../FlowNode';
import { NodeCategory, SocketDirection, SocketType } from '../Node';
import { ExecutionContext } from '../../execution/ExecutionContext';
import { 
  GeographicCoordinate, 
  CoordinateSystemType, 
  CoordinateTransformer,
  CoordinateSystemManager 
} from '../../../spatial/coordinate/CoordinateSystem';
import { GeospatialComponent } from '../../../spatial/components/GeospatialComponent';
import { SpatialAnalysisEngine, BufferAnalysisParams } from '../../../spatial/analysis/SpatialAnalysisEngine';
import { Entity } from '../../../core/Entity';

/**
 * 创建地理坐标节点
 */
export class CreateGeographicCoordinateNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('longitude', SocketDirection.INPUT, SocketType.NUMBER, '经度', 0);
    this.addSocket('latitude', SocketDirection.INPUT, SocketType.NUMBER, '纬度', 0);
    this.addSocket('altitude', SocketDirection.INPUT, SocketType.NUMBER, '海拔', 0);
    
    this.addSocket('coordinate', SocketDirection.OUTPUT, SocketType.OBJECT, '坐标');
  }
  
  protected executeImpl(): any {
    const longitude = this.getInputValue('longitude') as number;
    const latitude = this.getInputValue('latitude') as number;
    const altitude = this.getInputValue('altitude') as number;
    
    const coordinate: GeographicCoordinate = {
      longitude,
      latitude,
      altitude: altitude || undefined
    };
    
    this.setOutputValue('coordinate', coordinate);
    return coordinate;
  }
}

/**
 * 坐标转换节点
 */
export class CoordinateTransformNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('coordinate', SocketDirection.INPUT, SocketType.OBJECT, '输入坐标');
    this.addSocket('sourceSystem', SocketDirection.INPUT, SocketType.STRING, '源坐标系', 'WGS84');
    this.addSocket('targetSystem', SocketDirection.INPUT, SocketType.STRING, '目标坐标系', 'GCJ02');
    
    this.addSocket('transformedCoordinate', SocketDirection.OUTPUT, SocketType.OBJECT, '转换后坐标');
    this.addSocket('success', SocketDirection.OUTPUT, SocketType.BOOLEAN, '转换成功');
  }
  
  protected executeImpl(): any {
    const coordinate = this.getInputValue('coordinate') as GeographicCoordinate;
    const sourceSystem = this.getInputValue('sourceSystem') as string;
    const targetSystem = this.getInputValue('targetSystem') as string;
    
    try {
      const manager = CoordinateSystemManager.getInstance();
      const result = manager.transform(
        coordinate,
        sourceSystem as CoordinateSystemType,
        targetSystem as CoordinateSystemType
      );
      
      this.setOutputValue('transformedCoordinate', result);
      this.setOutputValue('success', true);
      return result;
    } catch (error) {
      console.error('坐标转换失败:', error);
      this.setOutputValue('transformedCoordinate', coordinate);
      this.setOutputValue('success', false);
      return coordinate;
    }
  }
}

/**
 * 创建地理空间组件节点
 */
export class CreateGeospatialComponentNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('coordinate', SocketDirection.INPUT, SocketType.OBJECT, '地理坐标');
    this.addSocket('geometryType', SocketDirection.INPUT, SocketType.STRING, '几何类型', 'Point');
    this.addSocket('coordinateSystem', SocketDirection.INPUT, SocketType.STRING, '坐标系', 'WGS84');
    this.addSocket('properties', SocketDirection.INPUT, SocketType.OBJECT, '属性', {});
    
    this.addSocket('component', SocketDirection.OUTPUT, SocketType.OBJECT, '地理空间组件');
  }
  
  protected executeImpl(): any {
    const coordinate = this.getInputValue('coordinate') as GeographicCoordinate;
    const geometryType = this.getInputValue('geometryType') as 'Point' | 'LineString' | 'Polygon';
    const coordinateSystem = this.getInputValue('coordinateSystem') as CoordinateSystemType;
    const properties = this.getInputValue('properties') as any;
    
    const component = new GeospatialComponent(coordinate, coordinateSystem, geometryType);
    component.setProperties(properties);
    
    this.setOutputValue('component', component);
    return component;
  }
}

/**
 * 添加地理空间组件到实体节点
 */
export class AddGeospatialComponentNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('entity', SocketDirection.INPUT, SocketType.OBJECT, '实体');
    this.addSocket('component', SocketDirection.INPUT, SocketType.OBJECT, '地理空间组件');
    this.addSocket('exec', SocketDirection.INPUT, SocketType.EXEC, '执行');
    
    this.addSocket('onComplete', SocketDirection.OUTPUT, SocketType.EXEC, '完成');
    this.addSocket('success', SocketDirection.OUTPUT, SocketType.BOOLEAN, '成功');
  }
  
  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const component = this.getInputValue('component') as GeospatialComponent;
    
    try {
      entity.addComponent(component);
      this.setOutputValue('success', true);
    } catch (error) {
      console.error('添加地理空间组件失败:', error);
      this.setOutputValue('success', false);
    }
    
    this.executeOutput('onComplete');
    return true;
  }
}

/**
 * 获取地理坐标节点
 */
export class GetGeographicCoordinateNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('component', SocketDirection.INPUT, SocketType.OBJECT, '地理空间组件');
    
    this.addSocket('coordinate', SocketDirection.OUTPUT, SocketType.OBJECT, '地理坐标');
    this.addSocket('longitude', SocketDirection.OUTPUT, SocketType.NUMBER, '经度');
    this.addSocket('latitude', SocketDirection.OUTPUT, SocketType.NUMBER, '纬度');
    this.addSocket('altitude', SocketDirection.OUTPUT, SocketType.NUMBER, '海拔');
  }
  
  protected executeImpl(): any {
    const component = this.getInputValue('component') as GeospatialComponent;
    
    if (component) {
      const coordinate = component.getGeographicCoordinate();
      
      this.setOutputValue('coordinate', coordinate);
      this.setOutputValue('longitude', coordinate.longitude);
      this.setOutputValue('latitude', coordinate.latitude);
      this.setOutputValue('altitude', coordinate.altitude || 0);
      
      return coordinate;
    }
    
    return null;
  }
}

/**
 * 设置地理坐标节点
 */
export class SetGeographicCoordinateNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('component', SocketDirection.INPUT, SocketType.OBJECT, '地理空间组件');
    this.addSocket('coordinate', SocketDirection.INPUT, SocketType.OBJECT, '新坐标');
    this.addSocket('exec', SocketDirection.INPUT, SocketType.EXEC, '执行');
    
    this.addSocket('onComplete', SocketDirection.OUTPUT, SocketType.EXEC, '完成');
    this.addSocket('success', SocketDirection.OUTPUT, SocketType.BOOLEAN, '成功');
  }
  
  protected executeImpl(): any {
    const component = this.getInputValue('component') as GeospatialComponent;
    const coordinate = this.getInputValue('coordinate') as GeographicCoordinate;
    
    try {
      component.setGeographicCoordinate(coordinate);
      this.setOutputValue('success', true);
    } catch (error) {
      console.error('设置地理坐标失败:', error);
      this.setOutputValue('success', false);
    }
    
    this.executeOutput('onComplete');
    return true;
  }
}

/**
 * 计算距离节点
 */
export class CalculateDistanceNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('coordinate1', SocketDirection.INPUT, SocketType.OBJECT, '坐标1');
    this.addSocket('coordinate2', SocketDirection.INPUT, SocketType.OBJECT, '坐标2');
    
    this.addSocket('distance', SocketDirection.OUTPUT, SocketType.NUMBER, '距离(米)');
    this.addSocket('distanceKm', SocketDirection.OUTPUT, SocketType.NUMBER, '距离(公里)');
  }
  
  protected executeImpl(): any {
    const coord1 = this.getInputValue('coordinate1') as GeographicCoordinate;
    const coord2 = this.getInputValue('coordinate2') as GeographicCoordinate;
    
    if (coord1 && coord2) {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const distance = analysisEngine.haversineDistance(coord1, coord2);
      
      this.setOutputValue('distance', distance);
      this.setOutputValue('distanceKm', distance / 1000);
      
      return distance;
    } else {
      this.setOutputValue('distance', 0);
      this.setOutputValue('distanceKm', 0);
      return 0;
    }
  }
}

/**
 * 缓冲区分析节点
 */
export class BufferAnalysisNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('geometry', SocketDirection.INPUT, SocketType.OBJECT, '输入几何');
    this.addSocket('distance', SocketDirection.INPUT, SocketType.NUMBER, '缓冲距离', 100);
    this.addSocket('unit', SocketDirection.INPUT, SocketType.STRING, '距离单位', 'meters');
    this.addSocket('segments', SocketDirection.INPUT, SocketType.NUMBER, '分段数', 32);
    this.addSocket('exec', SocketDirection.INPUT, SocketType.EXEC, '执行');
    
    this.addSocket('onComplete', SocketDirection.OUTPUT, SocketType.EXEC, '完成');
    this.addSocket('result', SocketDirection.OUTPUT, SocketType.OBJECT, '缓冲区几何');
    this.addSocket('success', SocketDirection.OUTPUT, SocketType.BOOLEAN, '成功');
    this.addSocket('error', SocketDirection.OUTPUT, SocketType.STRING, '错误信息');
  }
  
  protected executeImpl(): any {
    const geometry = this.getInputValue('geometry') as any;
    const distance = this.getInputValue('distance') as number;
    const unit = this.getInputValue('unit') as 'meters' | 'kilometers' | 'degrees';
    const segments = this.getInputValue('segments') as number;
    
    const params: BufferAnalysisParams = {
      distance,
      unit,
      segments
    };
    
    try {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const result = analysisEngine.buffer(geometry, params);
      
      this.setOutputValue('result', result.result);
      this.setOutputValue('success', result.success);
      this.setOutputValue('error', result.error || '');
    } catch (error) {
      this.setOutputValue('result', null);
      this.setOutputValue('success', false);
      this.setOutputValue('error', error instanceof Error ? error.message : '分析失败');
    }
    
    this.executeOutput('onComplete');
    return true;
  }
}

/**
 * 点在多边形内判断节点
 */
export class PointInPolygonNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;

    this.addSocket('point', SocketDirection.INPUT, SocketType.OBJECT, '点坐标');
    this.addSocket('polygon', SocketDirection.INPUT, SocketType.ARRAY, '多边形坐标');

    this.addSocket('inside', SocketDirection.OUTPUT, SocketType.BOOLEAN, '在内部');
  }

  protected executeImpl(): any {
    const point = this.getInputValue('point') as GeographicCoordinate;
    const polygon = this.getInputValue('polygon') as GeographicCoordinate[];

    if (point && polygon && polygon.length >= 3) {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const pointArray = [point.longitude, point.latitude];
      const polygonArray = polygon.map(coord => [coord.longitude, coord.latitude]);

      const inside = analysisEngine.pointInPolygon(pointArray, polygonArray);
      this.setOutputValue('inside', inside);
      return inside;
    } else {
      this.setOutputValue('inside', false);
      return false;
    }
  }
}

/**
 * 创建GeoJSON节点
 */
export class CreateGeoJSONNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;

    this.addSocket('component', SocketDirection.INPUT, SocketType.OBJECT, '地理空间组件');

    this.addSocket('geoJSON', SocketDirection.OUTPUT, SocketType.OBJECT, 'GeoJSON对象');
    this.addSocket('geoJSONString', SocketDirection.OUTPUT, SocketType.STRING, 'GeoJSON字符串');
  }

  protected executeImpl(): any {
    const component = this.getInputValue('component') as GeospatialComponent;

    if (component) {
      const geoJSON = component.toGeoJSON();

      this.setOutputValue('geoJSON', geoJSON);
      this.setOutputValue('geoJSONString', JSON.stringify(geoJSON, null, 2));
      return geoJSON;
    } else {
      this.setOutputValue('geoJSON', null);
      this.setOutputValue('geoJSONString', '');
      return null;
    }
  }
}

/**
 * 从GeoJSON创建组件节点
 */
export class CreateFromGeoJSONNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;

    this.addSocket('geoJSON', SocketDirection.INPUT, SocketType.OBJECT, 'GeoJSON对象');
    this.addSocket('coordinateSystem', SocketDirection.INPUT, SocketType.STRING, '坐标系', 'WGS84');

    this.addSocket('component', SocketDirection.OUTPUT, SocketType.OBJECT, '地理空间组件');
    this.addSocket('success', SocketDirection.OUTPUT, SocketType.BOOLEAN, '成功');
  }

  protected executeImpl(): any {
    const geoJSON = this.getInputValue('geoJSON') as any;
    const coordinateSystem = this.getInputValue('coordinateSystem') as CoordinateSystemType;

    try {
      const component = GeospatialComponent.fromGeoJSON(geoJSON, coordinateSystem);

      this.setOutputValue('component', component);
      this.setOutputValue('success', true);
      return component;
    } catch (error) {
      console.error('从GeoJSON创建组件失败:', error);
      this.setOutputValue('component', null);
      this.setOutputValue('success', false);
      return null;
    }
  }
}

/**
 * 设置地图视图节点
 */
export class SetMapViewNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;

    this.addSocket('spatialSystem', SocketDirection.INPUT, SocketType.OBJECT, '空间系统');
    this.addSocket('center', SocketDirection.INPUT, SocketType.OBJECT, '中心坐标');
    this.addSocket('zoom', SocketDirection.INPUT, SocketType.NUMBER, '缩放级别', 10);
    this.addSocket('exec', SocketDirection.INPUT, SocketType.EXEC, '执行');

    this.addSocket('onComplete', SocketDirection.OUTPUT, SocketType.EXEC, '完成');
    this.addSocket('success', SocketDirection.OUTPUT, SocketType.BOOLEAN, '成功');
  }

  protected executeImpl(): any {
    const spatialSystem = this.getInputValue('spatialSystem') as any;
    const center = this.getInputValue('center') as GeographicCoordinate;
    const zoom = this.getInputValue('zoom') as number;

    try {
      spatialSystem.setMapView(center, zoom);
      this.setOutputValue('success', true);
    } catch (error) {
      console.error('设置地图视图失败:', error);
      this.setOutputValue('success', false);
    }

    this.executeOutput('onComplete');
    return true;
  }
}

/**
 * 获取地图视图节点
 */
export class GetMapViewNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;

    this.addSocket('spatialSystem', SocketDirection.INPUT, SocketType.OBJECT, '空间系统');

    this.addSocket('center', SocketDirection.OUTPUT, SocketType.OBJECT, '中心坐标');
    this.addSocket('zoom', SocketDirection.OUTPUT, SocketType.NUMBER, '缩放级别');
    this.addSocket('hasView', SocketDirection.OUTPUT, SocketType.BOOLEAN, '有视图');
  }

  protected executeImpl(): any {
    const spatialSystem = this.getInputValue('spatialSystem') as any;

    try {
      const view = spatialSystem.getMapView();

      if (view) {
        this.setOutputValue('center', view.center);
        this.setOutputValue('zoom', view.zoom);
        this.setOutputValue('hasView', true);
        return view;
      } else {
        this.setOutputValue('center', null);
        this.setOutputValue('zoom', 0);
        this.setOutputValue('hasView', false);
        return null;
      }
    } catch (error) {
      console.error('获取地图视图失败:', error);
      this.setOutputValue('center', null);
      this.setOutputValue('zoom', 0);
      this.setOutputValue('hasView', false);
      return null;
    }
  }
}

/**
 * 设置地图提供者节点
 */
export class SetMapProviderNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;

    this.addSocket('spatialSystem', SocketDirection.INPUT, SocketType.OBJECT, '空间系统');
    this.addSocket('provider', SocketDirection.INPUT, SocketType.STRING, '提供者', 'osm');
    this.addSocket('exec', SocketDirection.INPUT, SocketType.EXEC, '执行');

    this.addSocket('onComplete', SocketDirection.OUTPUT, SocketType.EXEC, '完成');
    this.addSocket('success', SocketDirection.OUTPUT, SocketType.BOOLEAN, '成功');
  }

  protected executeImpl(): any {
    const spatialSystem = this.getInputValue('spatialSystem') as any;
    const provider = this.getInputValue('provider') as string;

    try {
      spatialSystem.setTileProvider(provider);
      this.setOutputValue('success', true);
    } catch (error) {
      console.error('设置地图提供者失败:', error);
      this.setOutputValue('success', false);
    }

    this.executeOutput('onComplete');
    return true;
  }
}

/**
 * 相交分析节点
 */
export class IntersectionAnalysisNode extends FlowNode {
  constructor(options: any) {
    super(options);
    this.category = NodeCategory.SPATIAL;
    
    this.addSocket('geometry1', SocketDirection.INPUT, SocketType.OBJECT, '几何1');
    this.addSocket('geometry2', SocketDirection.INPUT, SocketType.OBJECT, '几何2');
    this.addSocket('exec', SocketDirection.INPUT, SocketType.EXEC, '执行');
    
    this.addSocket('onComplete', SocketDirection.OUTPUT, SocketType.EXEC, '完成');
    this.addSocket('result', SocketDirection.OUTPUT, SocketType.OBJECT, '相交几何');
    this.addSocket('hasIntersection', SocketDirection.OUTPUT, SocketType.BOOLEAN, '有相交');
    this.addSocket('success', SocketDirection.OUTPUT, SocketType.BOOLEAN, '成功');
  }
  
  protected executeImpl(): any {
    const geometry1 = this.getInputValue('geometry1') as any;
    const geometry2 = this.getInputValue('geometry2') as any;
    
    try {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const result = analysisEngine.intersection(geometry1, geometry2);
      
      this.setOutputValue('result', result.result);
      this.setOutputValue('hasIntersection', !!result.result);
      this.setOutputValue('success', result.success);
    } catch (error) {
      this.setOutputValue('result', null);
      this.setOutputValue('hasIntersection', false);
      this.setOutputValue('success', false);
    }
    
    this.executeOutput('onComplete');
    return true;
  }
}

/**
 * GLTF模型组件
 * 用于存储GLTF模型数据
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';

// GLTF类型定义
export interface GLTF {
  scene: THREE.Group;
  scenes: THREE.Group[];
  animations: THREE.AnimationClip[];
  cameras: THREE.Camera[];
  asset: any;
  parser: any;
  userData: any;
}

/**
 * GLTF模型组件
 */
export class GLTFModelComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'GLTFModelComponent';

  /** GLTF模型 */
  private gltf: GLTF | null = null;

  /** 模型URL */
  private url: string = '';

  /** 是否已加载 */
  private loaded: boolean = false;

  /** 加载错误 */
  private error: Error | null = null;

  /**
   * 创建GLTF模型组件
   * @param gltf GLTF模型
   */
  constructor(gltf?: GLTF) {
    super(GLTFModelComponent.type);

    if (gltf) {
      this.gltf = gltf;
      this.loaded = true;
    }
  }

  /**
   * 获取GLTF模型
   * @returns GLTF模型
   */
  public getGLTF(): GLTF | null {
    return this.gltf;
  }

  /**
   * 设置GLTF模型
   * @param gltf GLTF模型
   */
  public setGLTF(gltf: GLTF): void {
    this.gltf = gltf;
    this.loaded = true;
    this.error = null;
  }

  /**
   * 获取模型URL
   * @returns 模型URL
   */
  public getURL(): string {
    return this.url;
  }

  /**
   * 设置模型URL
   * @param url 模型URL
   */
  public setURL(url: string): void {
    this.url = url;
  }

  /**
   * 是否已加载
   * @returns 是否已加载
   */
  public isLoaded(): boolean {
    return this.loaded;
  }

  /**
   * 设置加载状态
   * @param loaded 是否已加载
   */
  public setLoaded(loaded: boolean): void {
    this.loaded = loaded;
  }

  /**
   * 获取加载错误
   * @returns 加载错误
   */
  public getError(): Error | null {
    return this.error;
  }

  /**
   * 设置加载错误
   * @param error 加载错误
   */
  public setError(error: Error | null): void {
    this.error = error;
  }

  /**
   * 获取场景
   * @returns 场景
   */
  public getScene(): THREE.Group | null {
    return this.gltf?.scene || null;
  }

  /**
   * 获取动画
   * @returns 动画数组
   */
  public getAnimations(): THREE.AnimationClip[] {
    return this.gltf?.animations || [];
  }

  /**
   * 获取相机
   * @returns 相机数组
   */
  public getCameras(): THREE.Camera[] {
    return this.gltf?.cameras || [];
  }

  /**
   * 获取场景
   * @returns 场景数组
   */
  public getScenes(): THREE.Group[] {
    return this.gltf?.scenes || [];
  }

  /**
   * 获取资产
   * @returns 资产
   */
  public getAsset(): any {
    return this.gltf?.asset;
  }

  /**
   * 克隆组件
   * @returns 克隆的组件
   */
  public clone(): GLTFModelComponent {
    const component = new GLTFModelComponent(this.gltf || undefined);
    component.url = this.url;
    component.loaded = this.loaded;
    component.error = this.error;
    return component;
  }

  /**
   * 创建组件实例
   * @returns 新的组件实例
   */
  protected createInstance(): GLTFModelComponent {
    return new GLTFModelComponent();
  }

  /**
   * 销毁组件
   */
  public dispose(): void {
    // 销毁GLTF模型中的资源
    if (this.gltf) {
      // 销毁几何体
      this.gltf.scene.traverse((node: THREE.Object3D) => {
        if (node instanceof THREE.Mesh) {
          if (node.geometry) {
            (node.geometry as any).dispose();
          }

          if (node.material) {
            if (Array.isArray(node.material)) {
              for (const material of node.material) {
                this.disposeMaterial(material);
              }
            } else {
              this.disposeMaterial(node.material);
            }
          }
        }
      });
    }

    // 调用基类的dispose方法
    super.dispose();
  }

  /**
   * 销毁材质
   * @param material 材质
   */
  private disposeMaterial(material: THREE.Material): void {
    // 销毁材质中的纹理
    for (const key in material) {
      const value = (material as any)[key];
      if (value instanceof THREE.Texture) {
        (value as any).dispose();
      }
    }

    (material as any).dispose();
  }
}

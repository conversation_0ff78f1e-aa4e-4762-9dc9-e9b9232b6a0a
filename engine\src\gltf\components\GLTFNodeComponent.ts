/**
 * GLTF节点组件
 * 用于存储GLTF节点数据
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';

/**
 * GLTF节点组件
 */
export class GLTFNodeComponent extends Component {
  /** 组件类型 */
  public static readonly type: string = 'GLTFNodeComponent';
  
  /** Three.js对象 */
  private object: THREE.Object3D;
  
  /** 节点索引 */
  private nodeIndex: number = -1;
  
  /** 节点名称 */
  private nodeName: string = '';
  
  /** 节点类型 */
  private nodeType: string = '';
  
  /** 节点额外数据 */
  private userData: Record<string, any> = {};

  /**
   * 创建GLTF节点组件
   * @param object Three.js对象
   */
  constructor(object: THREE.Object3D) {
    super(GLTFNodeComponent.type);
    
    this.object = object;
    this.nodeName = object.name || '';
    this.nodeType = this.determineNodeType(object);
    this.userData = { ...object.userData };
    
    // 尝试从userData中获取节点索引
    if (object.userData && typeof object.userData.nodeIndex === 'number') {
      this.nodeIndex = object.userData.nodeIndex;
    }
  }

  /**
   * 确定节点类型
   * @param object Three.js对象
   * @returns 节点类型
   */
  private determineNodeType(object: THREE.Object3D): string {
    if (object instanceof THREE.Mesh) {
      return 'Mesh';
    } else if (object instanceof THREE.Camera) {
      return 'Camera';
    } else if (object instanceof THREE.Light) {
      return 'Light';
    } else if (object instanceof THREE.Bone) {
      return 'Bone';
    } else if (object instanceof THREE.SkinnedMesh) {
      return 'SkinnedMesh';
    } else if (object instanceof THREE.Group) {
      return 'Group';
    } else {
      return 'Object3D';
    }
  }

  /**
   * 获取Three.js对象
   * @returns Three.js对象
   */
  public getObject(): THREE.Object3D {
    return this.object;
  }

  /**
   * 设置Three.js对象
   * @param object Three.js对象
   */
  public setObject(object: THREE.Object3D): void {
    this.object = object;
    this.nodeName = object.name || '';
    this.nodeType = this.determineNodeType(object);
    this.userData = { ...object.userData };
    
    // 尝试从userData中获取节点索引
    if (object.userData && typeof object.userData.nodeIndex === 'number') {
      this.nodeIndex = object.userData.nodeIndex;
    }
  }

  /**
   * 获取节点索引
   * @returns 节点索引
   */
  public getNodeIndex(): number {
    return this.nodeIndex;
  }

  /**
   * 设置节点索引
   * @param index 节点索引
   */
  public setNodeIndex(index: number): void {
    this.nodeIndex = index;
    
    // 同时更新对象的userData
    if (this.object) {
      this.object.userData.nodeIndex = index;
    }
  }

  /**
   * 获取节点名称
   * @returns 节点名称
   */
  public getNodeName(): string {
    return this.nodeName;
  }

  /**
   * 设置节点名称
   * @param name 节点名称
   */
  public setNodeName(name: string): void {
    this.nodeName = name;
    
    // 同时更新对象的名称
    if (this.object) {
      this.object.name = name;
    }
  }

  /**
   * 获取节点类型
   * @returns 节点类型
   */
  public getNodeType(): string {
    return this.nodeType;
  }

  /**
   * 获取节点额外数据
   * @returns 节点额外数据
   */
  public getUserData(): Record<string, any> {
    return { ...this.userData };
  }

  /**
   * 设置节点额外数据
   * @param userData 节点额外数据
   */
  public setUserData(userData: Record<string, any>): void {
    this.userData = { ...userData };
    
    // 同时更新对象的userData
    if (this.object) {
      this.object.userData = { ...this.object.userData, ...userData };
    }
  }

  /**
   * 克隆组件
   * @returns 克隆的组件
   */
  public clone(): GLTFNodeComponent {
    const component = new GLTFNodeComponent(this.object.clone());
    component.nodeIndex = this.nodeIndex;
    component.nodeName = this.nodeName;
    component.nodeType = this.nodeType;
    component.userData = { ...this.userData };
    return component;
  }

  /**
   * 创建组件实例
   * @returns 新的组件实例
   */
  protected createInstance(): GLTFNodeComponent {
    return new GLTFNodeComponent(this.object.clone());
  }
}

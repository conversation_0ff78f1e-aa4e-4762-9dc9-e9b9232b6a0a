import { Entity } from '../../core/Entity';
import { Vector3 } from '../../math/Vector3';
import { IndustrialDeviceComponent } from './IndustrialDeviceComponent';
import { DeviceType, DeviceStatus, DeviceConfig, DataQuality } from '../types';
import { Debug } from '../../utils/Debug';

/**
 * 传送带段接口
 */
interface ConveyorSegment {
  id: string;
  startPosition: Vector3;
  endPosition: Vector3;
  length: number;
  width: number;
  height: number;
  speed: number;
  direction: Vector3;
}

/**
 * 物料项接口
 */
interface MaterialItem {
  id: string;
  name: string;
  position: Vector3;
  size: Vector3;
  weight: number;
  barcode?: string;
  timestamp: Date;
  destination?: string;
}

/**
 * 传感器数据接口
 */
interface ConveyorSensor {
  id: string;
  name: string;
  type: 'photoelectric' | 'proximity' | 'weight' | 'barcode';
  position: Vector3;
  status: boolean;
  value: any;
  lastTriggered?: Date;
}

/**
 * 传送带组件
 * 实现工业传送带的数字孪生功能
 */
export class ConveyorComponent extends IndustrialDeviceComponent {
  public readonly deviceType = DeviceType.CONVEYOR;
  
  // 传送带特有属性
  public segments: ConveyorSegment[] = [];
  public totalLength: number = 0;
  public beltWidth: number = 500;        // 带宽 (mm)
  public maxSpeed: number = 2000;        // 最大速度 (mm/min)
  public currentSpeed: number = 0;       // 当前速度 (mm/min)
  public direction: 'forward' | 'reverse' | 'stop' = 'stop';
  
  // 物料管理
  public materialsOnBelt: MaterialItem[] = [];
  public materialCount: number = 0;
  public totalWeight: number = 0;
  public maxWeight: number = 1000;       // 最大承重 (kg)
  
  // 传感器系统
  public sensors: ConveyorSensor[] = [];
  public entryCount: number = 0;         // 进料计数
  public exitCount: number = 0;          // 出料计数
  public jamDetected: boolean = false;   // 堵料检测
  
  // 驱动系统
  public motorSpeed: number = 0;         // 电机转速 (RPM)
  public motorCurrent: number = 0;       // 电机电流 (A)
  public motorTemperature: number = 25;  // 电机温度 (°C)
  public motorVibration: number = 0;     // 电机振动 (mm/s)
  
  // 维护数据
  public beltTension: number = 100;      // 皮带张力 (%)
  public beltWear: number = 0;           // 皮带磨损 (%)
  public operatingHours: number = 0;     // 运行小时数
  public lastMaintenance: Date = new Date();

  constructor(entity: Entity, config?: DeviceConfig) {
    super(entity, 'Conveyor', config);
    
    // 初始化传送带段
    this.initializeSegments();
    
    // 初始化传感器
    this.initializeSensors();
    
    // 初始化传送带特有的数据点
    this.initializeConveyorDataPoints();
    
    Debug.log('Conveyor', `传送带组件已创建: ${this.deviceName}`);
  }

  /**
   * 初始化传送带段
   */
  private initializeSegments(): void {
    // 创建默认的直线传送带段
    const segment: ConveyorSegment = {
      id: 'segment_001',
      startPosition: new Vector3(0, 0, 0),
      endPosition: new Vector3(5000, 0, 0), // 5米长
      length: 5000,
      width: this.beltWidth,
      height: 100,
      speed: 0,
      direction: new Vector3(1, 0, 0)
    };
    
    this.segments.push(segment);
    this.totalLength = segment.length;
  }

  /**
   * 初始化传感器
   */
  private initializeSensors(): void {
    this.sensors = [
      {
        id: 'entry_sensor',
        name: '进料传感器',
        type: 'photoelectric',
        position: new Vector3(100, 0, 50),
        status: false,
        value: false
      },
      {
        id: 'exit_sensor',
        name: '出料传感器',
        type: 'photoelectric',
        position: new Vector3(4900, 0, 50),
        status: false,
        value: false
      },
      {
        id: 'weight_sensor',
        name: '重量传感器',
        type: 'weight',
        position: new Vector3(2500, 0, 0),
        status: true,
        value: 0
      },
      {
        id: 'jam_sensor',
        name: '堵料传感器',
        type: 'proximity',
        position: new Vector3(3000, 0, 50),
        status: false,
        value: false
      }
    ];
  }

  /**
   * 初始化传送带数据点
   */
  private initializeConveyorDataPoints(): void {
    const dataPoints = [
      // 运行状态
      { tagId: 'belt_speed', name: '皮带速度', unit: 'mm/min' },
      { tagId: 'belt_direction', name: '运行方向', unit: '' },
      { tagId: 'material_count', name: '物料数量', unit: '个' },
      { tagId: 'total_weight', name: '总重量', unit: 'kg' },
      { tagId: 'entry_count', name: '进料计数', unit: '个' },
      { tagId: 'exit_count', name: '出料计数', unit: '个' },
      
      // 驱动系统
      { tagId: 'motor_speed', name: '电机转速', unit: 'RPM' },
      { tagId: 'motor_current', name: '电机电流', unit: 'A' },
      { tagId: 'motor_temperature', name: '电机温度', unit: '°C' },
      { tagId: 'motor_vibration', name: '电机振动', unit: 'mm/s' },
      
      // 维护数据
      { tagId: 'belt_tension', name: '皮带张力', unit: '%' },
      { tagId: 'belt_wear', name: '皮带磨损', unit: '%' },
      { tagId: 'operating_hours', name: '运行时间', unit: 'h' },
      
      // 传感器状态
      { tagId: 'entry_sensor', name: '进料传感器', unit: '' },
      { tagId: 'exit_sensor', name: '出料传感器', unit: '' },
      { tagId: 'weight_sensor', name: '重量传感器', unit: 'kg' },
      { tagId: 'jam_detected', name: '堵料检测', unit: '' }
    ];
    
    dataPoints.forEach(dp => {
      this.updateDataPoint({
        tagId: dp.tagId,
        deviceId: this.deviceId,
        timestamp: new Date(),
        value: 0,
        quality: DataQuality.GOOD,
        metadata: { name: dp.name, unit: dp.unit }
      });
    });
  }

  /**
   * 启动传送带
   * @param speed 速度 (mm/min)
   * @param direction 方向
   */
  public start(speed: number = 1000, direction: 'forward' | 'reverse' = 'forward'): void {
    if (this.status !== DeviceStatus.ONLINE && this.status !== DeviceStatus.RUNNING) {
      Debug.warn('Conveyor', `传送带无法启动: ${this.deviceName} - 设备状态异常`);
      return;
    }

    this.currentSpeed = Math.min(Math.abs(speed), this.maxSpeed);
    this.direction = direction;
    this.motorSpeed = this.currentSpeed / 10; // 简化的转速计算
    
    this.updateStatus(DeviceStatus.RUNNING);
    
    Debug.log('Conveyor', `传送带启动: ${this.deviceName} - 速度: ${this.currentSpeed}mm/min, 方向: ${direction}`);
    
    // 更新数据点
    this.updateSpeedDataPoints();
  }

  /**
   * 停止传送带
   */
  public stop(): void {
    this.currentSpeed = 0;
    this.direction = 'stop';
    this.motorSpeed = 0;
    
    this.updateStatus(DeviceStatus.ONLINE);
    
    Debug.log('Conveyor', `传送带停止: ${this.deviceName}`);
    
    // 更新数据点
    this.updateSpeedDataPoints();
  }

  /**
   * 设置传送带速度
   * @param speed 速度 (mm/min)
   */
  public setSpeed(speed: number): void {
    if (this.direction === 'stop') {
      Debug.warn('Conveyor', `传送带未运行，无法设置速度: ${this.deviceName}`);
      return;
    }

    this.currentSpeed = Math.min(Math.abs(speed), this.maxSpeed);
    this.motorSpeed = this.currentSpeed / 10;
    
    Debug.log('Conveyor', `传送带速度调整: ${this.deviceName} - ${this.currentSpeed}mm/min`);
    
    // 更新数据点
    this.updateSpeedDataPoints();
  }

  /**
   * 添加物料到传送带
   * @param material 物料信息
   */
  public addMaterial(material: Omit<MaterialItem, 'id' | 'timestamp'>): string {
    const materialItem: MaterialItem = {
      id: `material_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      timestamp: new Date(),
      ...material
    };

    this.materialsOnBelt.push(materialItem);
    this.materialCount = this.materialsOnBelt.length;
    this.totalWeight = this.materialsOnBelt.reduce((sum, item) => sum + item.weight, 0);
    this.entryCount++;

    // 触发进料传感器
    this.triggerSensor('entry_sensor', true);

    Debug.log('Conveyor', `物料上料: ${this.deviceName} - ${materialItem.name} (${materialItem.id})`);

    // 更新数据点
    this.updateMaterialDataPoints();

    return materialItem.id;
  }

  /**
   * 移除物料从传送带
   * @param materialId 物料ID
   */
  public removeMaterial(materialId: string): boolean {
    const index = this.materialsOnBelt.findIndex(item => item.id === materialId);
    if (index === -1) {
      return false;
    }

    const material = this.materialsOnBelt[index];
    this.materialsOnBelt.splice(index, 1);
    this.materialCount = this.materialsOnBelt.length;
    this.totalWeight = this.materialsOnBelt.reduce((sum, item) => sum + item.weight, 0);
    this.exitCount++;

    // 触发出料传感器
    this.triggerSensor('exit_sensor', true);

    Debug.log('Conveyor', `物料下料: ${this.deviceName} - ${material.name} (${materialId})`);

    // 更新数据点
    this.updateMaterialDataPoints();

    return true;
  }

  /**
   * 触发传感器
   * @param sensorId 传感器ID
   * @param value 传感器值
   */
  private triggerSensor(sensorId: string, value: any): void {
    const sensor = this.sensors.find(s => s.id === sensorId);
    if (sensor) {
      sensor.value = value;
      sensor.status = true;
      sensor.lastTriggered = new Date();

      // 更新传感器数据点
      this.updateDataPoint({
        tagId: sensorId,
        deviceId: this.deviceId,
        timestamp: new Date(),
        value: value,
        quality: DataQuality.GOOD
      });

      // 自动复位传感器（模拟）
      setTimeout(() => {
        sensor.status = false;
        sensor.value = false;
        this.updateDataPoint({
          tagId: sensorId,
          deviceId: this.deviceId,
          timestamp: new Date(),
          value: false,
          quality: DataQuality.GOOD
        });
      }, 1000);
    }
  }

  /**
   * 检测堵料
   */
  private detectJam(): void {
    // 简化的堵料检测逻辑
    const jamThreshold = 5; // 5个物料以上可能堵料
    const weightThreshold = this.maxWeight * 0.8; // 重量超过80%可能堵料

    const previousJamStatus = this.jamDetected;
    this.jamDetected = this.materialCount > jamThreshold || this.totalWeight > weightThreshold;

    if (this.jamDetected && !previousJamStatus) {
      Debug.warn('Conveyor', `检测到堵料: ${this.deviceName}`);
      this.triggerAlarm('conveyor_jam');
      this.triggerSensor('jam_sensor', true);
    } else if (!this.jamDetected && previousJamStatus) {
      Debug.log('Conveyor', `堵料解除: ${this.deviceName}`);
      this.clearAlarm('conveyor_jam');
    }

    this.updateDataPoint({
      tagId: 'jam_detected',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.jamDetected,
      quality: DataQuality.GOOD
    });
  }

  /**
   * 更新速度相关数据点
   */
  private updateSpeedDataPoints(): void {
    const now = new Date();

    this.updateDataPoint({
      tagId: 'belt_speed',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.currentSpeed,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'belt_direction',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.direction,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'motor_speed',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.motorSpeed,
      quality: DataQuality.GOOD
    });
  }

  /**
   * 更新物料相关数据点
   */
  private updateMaterialDataPoints(): void {
    const now = new Date();

    this.updateDataPoint({
      tagId: 'material_count',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.materialCount,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'total_weight',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.totalWeight,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'entry_count',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.entryCount,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'exit_count',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.exitCount,
      quality: DataQuality.GOOD
    });

    // 更新重量传感器
    this.updateDataPoint({
      tagId: 'weight_sensor',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.totalWeight,
      quality: DataQuality.GOOD
    });
  }

  /**
   * 模拟物料移动
   */
  private simulateMaterialMovement(): void {
    if (this.direction === 'stop' || this.currentSpeed === 0) {
      return;
    }

    const deltaTime = 1; // 1秒
    const distance = (this.currentSpeed / 60) * deltaTime; // mm/s * s = mm

    this.materialsOnBelt.forEach(material => {
      if (this.direction === 'forward') {
        material.position.x += distance;
      } else {
        material.position.x -= distance;
      }

      // 检查物料是否到达出口
      if (material.position.x >= this.totalLength) {
        // 自动移除到达出口的物料
        setTimeout(() => {
          this.removeMaterial(material.id);
        }, 100);
      }
    });
  }

  /**
   * 子类更新方法
   */
  protected onUpdate(): void {
    // 模拟物料移动
    this.simulateMaterialMovement();

    // 检测堵料
    this.detectJam();

    // 更新电机数据
    if (this.direction !== 'stop') {
      this.motorCurrent = 5 + Math.random() * 3; // 5-8A
      this.motorTemperature = 25 + this.motorCurrent * 2 + Math.random() * 5;
      this.motorVibration = Math.random() * 2;
      this.operatingHours += 1 / 3600; // 增加运行时间
    } else {
      this.motorCurrent = 0;
      this.motorTemperature = Math.max(25, this.motorTemperature - 0.1);
      this.motorVibration = 0;
    }

    // 更新皮带磨损
    if (this.direction !== 'stop') {
      this.beltWear += 0.0001; // 缓慢磨损
    }

    // 更新驱动系统数据点
    const now = new Date();
    this.updateDataPoint({
      tagId: 'motor_current',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.motorCurrent,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'motor_temperature',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.motorTemperature,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'motor_vibration',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.motorVibration,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'belt_tension',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.beltTension,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'belt_wear',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.beltWear,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'operating_hours',
      deviceId: this.deviceId,
      timestamp: now,
      value: this.operatingHours,
      quality: DataQuality.GOOD
    });

    // 检查报警条件
    if (this.motorTemperature > 80) {
      this.triggerAlarm('motor_overheat');
    }

    if (this.beltWear > 80) {
      this.triggerAlarm('belt_wear_high');
    }

    if (this.totalWeight > this.maxWeight) {
      this.triggerAlarm('overweight');
    }
  }

  /**
   * 获取传送带特有信息
   * @returns 传送带信息
   */
  public getConveyorInfo(): any {
    return {
      ...this.getDeviceInfo(),
      segments: this.segments,
      totalLength: this.totalLength,
      beltWidth: this.beltWidth,
      currentSpeed: this.currentSpeed,
      maxSpeed: this.maxSpeed,
      direction: this.direction,
      materialsOnBelt: this.materialsOnBelt,
      materialCount: this.materialCount,
      totalWeight: this.totalWeight,
      maxWeight: this.maxWeight,
      sensors: this.sensors,
      entryCount: this.entryCount,
      exitCount: this.exitCount,
      jamDetected: this.jamDetected,
      motorSpeed: this.motorSpeed,
      motorCurrent: this.motorCurrent,
      motorTemperature: this.motorTemperature,
      beltWear: this.beltWear,
      operatingHours: this.operatingHours
    };
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): ConveyorComponent {
    return new ConveyorComponent(this.entity!);
  }
}

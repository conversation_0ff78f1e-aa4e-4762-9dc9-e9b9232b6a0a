/**
 * 数据保护
 * 提供数据加密和保护功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { SecurityConfig, SecurityLevel, DataClassification } from './types';

export class DataProtection extends EventEmitter {
  private config: SecurityConfig;
  private encryptionKeys: Map<SecurityLevel, string> = new Map();
  private classifications: Map<string, DataClassification> = new Map();
  private isDestroyed: boolean = false;

  constructor(config: SecurityConfig) {
    super();
    this.config = config;
    this.initializeEncryptionKeys();
    this.initializeDataClassifications();
  }

  /**
   * 加密数据
   * @param data 原始数据
   * @param level 安全级别
   * @returns 加密后的数据
   */
  public async encrypt(data: any, level: SecurityLevel = SecurityLevel.INTERNAL): Promise<string> {
    if (this.isDestroyed) {
      throw new Error('DataProtection has been destroyed');
    }

    try {
      if (!this.config.enableDataEncryption) {
        return JSON.stringify(data);
      }

      const key = this.encryptionKeys.get(level);
      if (!key) {
        throw new Error(`No encryption key found for security level: ${level}`);
      }

      const serializedData = JSON.stringify(data);
      const encryptedData = await this.performEncryption(serializedData, key);

      this.emit('data-encrypted', { level, size: serializedData.length });
      return encryptedData;

    } catch (error) {
      this.emit('encryption-error', { error, level });
      throw error;
    }
  }

  /**
   * 解密数据
   * @param encryptedData 加密的数据
   * @param level 安全级别
   * @returns 解密后的数据
   */
  public async decrypt(encryptedData: string, level: SecurityLevel = SecurityLevel.INTERNAL): Promise<any> {
    if (this.isDestroyed) {
      throw new Error('DataProtection has been destroyed');
    }

    try {
      if (!this.config.enableDataEncryption) {
        return JSON.parse(encryptedData);
      }

      const key = this.encryptionKeys.get(level);
      if (!key) {
        throw new Error(`No encryption key found for security level: ${level}`);
      }

      const decryptedData = await this.performDecryption(encryptedData, key);
      const data = JSON.parse(decryptedData);

      this.emit('data-decrypted', { level, size: decryptedData.length });
      return data;

    } catch (error) {
      this.emit('decryption-error', { error, level });
      throw error;
    }
  }

  /**
   * 哈希数据
   * @param data 原始数据
   * @param algorithm 哈希算法
   * @returns 哈希值
   */
  public async hash(data: string, algorithm: string = 'SHA-256'): Promise<string> {
    if (this.isDestroyed) {
      throw new Error('DataProtection has been destroyed');
    }

    try {
      // 在浏览器环境中使用Web Crypto API
      if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(data);
        const hashBuffer = await window.crypto.subtle.digest(algorithm, dataBuffer);
        const hashArray = Array.from(new Uint8Array(hashBuffer));
        return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
      }

      // 简化实现（实际应该使用crypto库）
      return this.simpleHash(data);

    } catch (error) {
      this.emit('hash-error', { error, algorithm });
      throw error;
    }
  }

  /**
   * 验证哈希
   * @param data 原始数据
   * @param hash 哈希值
   * @param algorithm 哈希算法
   * @returns 是否匹配
   */
  public async verifyHash(data: string, hash: string, algorithm: string = 'SHA-256'): Promise<boolean> {
    try {
      const computedHash = await this.hash(data, algorithm);
      return computedHash === hash;
    } catch (error) {
      this.emit('hash-verification-error', { error });
      return false;
    }
  }

  /**
   * 分类数据
   * @param data 数据
   * @returns 数据分类
   */
  public classifyData(data: any): DataClassification | null {
    if (this.isDestroyed) {
      return null;
    }

    const dataString = JSON.stringify(data).toLowerCase();
    let bestMatch: DataClassification | null = null;
    let bestScore = 0;

    this.classifications.forEach(classification => {
      let score = 0;
      
      classification.rules.forEach(rule => {
        const regex = new RegExp(rule.pattern, 'i');
        if (regex.test(dataString)) {
          score += rule.weight;
        }
      });

      if (score > bestScore) {
        bestScore = score;
        bestMatch = classification;
      }
    });

    return bestMatch;
  }

  /**
   * 添加数据分类
   * @param classification 数据分类
   */
  public addDataClassification(classification: DataClassification): void {
    if (this.isDestroyed) {
      return;
    }

    this.classifications.set(classification.id, classification);
    this.emit('classification-added', classification);
  }

  /**
   * 移除数据分类
   * @param classificationId 分类ID
   */
  public removeDataClassification(classificationId: string): void {
    if (this.isDestroyed) {
      return;
    }

    const classification = this.classifications.get(classificationId);
    if (classification) {
      this.classifications.delete(classificationId);
      this.emit('classification-removed', classification);
    }
  }

  /**
   * 生成随机密钥
   * @param length 密钥长度
   * @returns 密钥
   */
  public generateKey(length: number = 32): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * 轮换加密密钥
   * @param level 安全级别
   */
  public rotateKey(level: SecurityLevel): void {
    if (this.isDestroyed) {
      return;
    }

    const newKey = this.generateKey();
    const oldKey = this.encryptionKeys.get(level);
    
    this.encryptionKeys.set(level, newKey);
    
    this.emit('key-rotated', { level, oldKey, newKey });
  }

  /**
   * 获取数据保护统计
   * @returns 统计信息
   */
  public getStats(): any {
    return {
      encryptionEnabled: this.config.enableDataEncryption,
      algorithm: this.config.encryptionAlgorithm,
      securityLevels: Array.from(this.encryptionKeys.keys()),
      classifications: this.classifications.size,
      isDestroyed: this.isDestroyed
    };
  }

  /**
   * 销毁数据保护
   */
  public async destroy(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    // 清除敏感数据
    this.encryptionKeys.clear();
    this.classifications.clear();
    
    this.removeAllListeners();
    this.isDestroyed = true;
  }

  /**
   * 初始化加密密钥
   */
  private initializeEncryptionKeys(): void {
    // 为每个安全级别生成密钥
    Object.values(SecurityLevel).forEach(level => {
      this.encryptionKeys.set(level, this.generateKey());
    });
  }

  /**
   * 初始化数据分类
   */
  private initializeDataClassifications(): void {
    // 个人信息分类
    this.addDataClassification({
      id: 'personal-info',
      name: 'Personal Information',
      level: SecurityLevel.CONFIDENTIAL,
      description: 'Contains personal identifiable information',
      rules: [
        { field: 'email', pattern: '\\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}\\b', weight: 10 },
        { field: 'phone', pattern: '\\b\\d{3}-\\d{3}-\\d{4}\\b', weight: 8 },
        { field: 'ssn', pattern: '\\b\\d{3}-\\d{2}-\\d{4}\\b', weight: 15 }
      ],
      retention: {
        retentionPeriod: 2555, // 7 years
        archiveAfter: 1825, // 5 years
        deleteAfter: 3650, // 10 years
        backupRequired: true
      },
      encryption: {
        required: true,
        algorithm: 'AES-256-GCM',
        keySize: 256,
        rotationPeriod: 90
      }
    });

    // 财务信息分类
    this.addDataClassification({
      id: 'financial-info',
      name: 'Financial Information',
      level: SecurityLevel.SECRET,
      description: 'Contains financial and payment information',
      rules: [
        { field: 'credit_card', pattern: '\\b\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}[\\s-]?\\d{4}\\b', weight: 20 },
        { field: 'bank_account', pattern: '\\b\\d{8,12}\\b', weight: 15 },
        { field: 'routing', pattern: '\\b\\d{9}\\b', weight: 10 }
      ],
      retention: {
        retentionPeriod: 2555, // 7 years
        archiveAfter: 1095, // 3 years
        deleteAfter: 2555, // 7 years
        backupRequired: true
      },
      encryption: {
        required: true,
        algorithm: 'AES-256-GCM',
        keySize: 256,
        rotationPeriod: 30
      }
    });
  }

  /**
   * 执行加密
   */
  private async performEncryption(data: string, key: string): Promise<string> {
    // 简化的加密实现（实际应该使用真正的加密算法）
    const encoded = btoa(data + '|' + key);
    return `encrypted:${encoded}`;
  }

  /**
   * 执行解密
   */
  private async performDecryption(encryptedData: string, key: string): Promise<string> {
    // 简化的解密实现
    if (!encryptedData.startsWith('encrypted:')) {
      throw new Error('Invalid encrypted data format');
    }

    const encoded = encryptedData.substring(10);
    const decoded = atob(encoded);
    const parts = decoded.split('|');
    
    if (parts.length !== 2 || parts[1] !== key) {
      throw new Error('Decryption failed: invalid key');
    }

    return parts[0];
  }

  /**
   * 简单哈希实现
   */
  private simpleHash(data: string): string {
    let hash = 0;
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash).toString(16);
  }
}

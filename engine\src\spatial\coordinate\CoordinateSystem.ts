/**
 * 地理坐标系统
 * 支持多种坐标系统和坐标转换
 */
import * as THREE from 'three';

/**
 * 坐标系统类型枚举
 */
export enum CoordinateSystemType {
  WGS84 = 'WGS84',           // 世界大地坐标系
  GCJ02 = 'GCJ02',           // 火星坐标系（中国偏移坐标系）
  BD09 = 'BD09',             // 百度坐标系
  UTM = 'UTM',               // 通用横轴墨卡托投影
  WebMercator = 'WebMercator' // Web墨卡托投影
}

/**
 * 地理坐标接口
 */
export interface GeographicCoordinate {
  longitude: number;  // 经度
  latitude: number;   // 纬度
  altitude?: number;  // 海拔高度
}

/**
 * 投影坐标接口
 */
export interface ProjectedCoordinate {
  x: number;
  y: number;
  z?: number;
}

/**
 * 坐标转换器
 */
export class CoordinateTransformer {
  // 地球半径常量
  private static readonly EARTH_RADIUS = 6378137.0;
  private static readonly PI = Math.PI;
  private static readonly X_PI = Math.PI * 3000.0 / 180.0;
  
  /**
   * WGS84 转 GCJ02 (火星坐标系)
   * @param coord WGS84坐标
   * @returns GCJ02坐标
   */
  static wgs84ToGcj02(coord: GeographicCoordinate): GeographicCoordinate {
    if (this.outOfChina(coord.latitude, coord.longitude)) {
      return { ...coord };
    }
    
    let dlat = this.transformLat(coord.longitude - 105.0, coord.latitude - 35.0);
    let dlng = this.transformLng(coord.longitude - 105.0, coord.latitude - 35.0);
    
    const radlat = coord.latitude / 180.0 * this.PI;
    let magic = Math.sin(radlat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / ((this.EARTH_RADIUS * (1 - 0.00669342162296594323)) / (magic * sqrtmagic) * this.PI);
    dlng = (dlng * 180.0) / (this.EARTH_RADIUS / sqrtmagic * Math.cos(radlat) * this.PI);
    
    return {
      latitude: coord.latitude + dlat,
      longitude: coord.longitude + dlng,
      altitude: coord.altitude
    };
  }
  
  /**
   * GCJ02 转 WGS84
   * @param coord GCJ02坐标
   * @returns WGS84坐标
   */
  static gcj02ToWgs84(coord: GeographicCoordinate): GeographicCoordinate {
    if (this.outOfChina(coord.latitude, coord.longitude)) {
      return { ...coord };
    }
    
    let dlat = this.transformLat(coord.longitude - 105.0, coord.latitude - 35.0);
    let dlng = this.transformLng(coord.longitude - 105.0, coord.latitude - 35.0);
    
    const radlat = coord.latitude / 180.0 * this.PI;
    let magic = Math.sin(radlat);
    magic = 1 - 0.00669342162296594323 * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / ((this.EARTH_RADIUS * (1 - 0.00669342162296594323)) / (magic * sqrtmagic) * this.PI);
    dlng = (dlng * 180.0) / (this.EARTH_RADIUS / sqrtmagic * Math.cos(radlat) * this.PI);
    
    return {
      latitude: coord.latitude - dlat,
      longitude: coord.longitude - dlng,
      altitude: coord.altitude
    };
  }
  
  /**
   * GCJ02 转 BD09 (百度坐标系)
   * @param coord GCJ02坐标
   * @returns BD09坐标
   */
  static gcj02ToBd09(coord: GeographicCoordinate): GeographicCoordinate {
    const z = Math.sqrt(coord.longitude * coord.longitude + coord.latitude * coord.latitude) + 0.00002 * Math.sin(coord.latitude * this.X_PI);
    const theta = Math.atan2(coord.latitude, coord.longitude) + 0.000003 * Math.cos(coord.longitude * this.X_PI);
    
    return {
      longitude: z * Math.cos(theta) + 0.0065,
      latitude: z * Math.sin(theta) + 0.006,
      altitude: coord.altitude
    };
  }
  
  /**
   * BD09 转 GCJ02
   * @param coord BD09坐标
   * @returns GCJ02坐标
   */
  static bd09ToGcj02(coord: GeographicCoordinate): GeographicCoordinate {
    const x = coord.longitude - 0.0065;
    const y = coord.latitude - 0.006;
    const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * this.X_PI);
    const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * this.X_PI);
    
    return {
      longitude: z * Math.cos(theta),
      latitude: z * Math.sin(theta),
      altitude: coord.altitude
    };
  }
  
  /**
   * 地理坐标转Web墨卡托投影
   * @param coord 地理坐标
   * @returns Web墨卡托坐标
   */
  static geographicToWebMercator(coord: GeographicCoordinate): ProjectedCoordinate {
    const x = coord.longitude * 20037508.34 / 180;
    let y = Math.log(Math.tan((90 + coord.latitude) * this.PI / 360)) / (this.PI / 180);
    y = y * 20037508.34 / 180;
    
    return {
      x,
      y,
      z: coord.altitude || 0
    };
  }
  
  /**
   * Web墨卡托投影转地理坐标
   * @param coord Web墨卡托坐标
   * @returns 地理坐标
   */
  static webMercatorToGeographic(coord: ProjectedCoordinate): GeographicCoordinate {
    const longitude = coord.x / 20037508.34 * 180;
    let latitude = coord.y / 20037508.34 * 180;
    latitude = 180 / this.PI * (2 * Math.atan(Math.exp(latitude * this.PI / 180)) - this.PI / 2);
    
    return {
      longitude,
      latitude,
      altitude: coord.z
    };
  }
  
  /**
   * 地理坐标转UTM投影
   * @param coord 地理坐标
   * @param zone UTM分带号
   * @returns UTM坐标
   */
  static geographicToUTM(coord: GeographicCoordinate, zone?: number): ProjectedCoordinate {
    if (!zone) {
      zone = Math.floor((coord.longitude + 180) / 6) + 1;
    }
    
    // UTM投影计算（简化版）
    const a = 6378137.0; // 长半轴
    const f = 1 / 298.257223563; // 扁率
    const k0 = 0.9996; // 比例因子
    
    const lon0 = (zone - 1) * 6 - 180 + 3; // 中央经线
    const lat = coord.latitude * this.PI / 180;
    const lon = coord.longitude * this.PI / 180;
    const lon0Rad = lon0 * this.PI / 180;
    
    const e2 = 2 * f - f * f;
    const e = Math.sqrt(e2);
    const n = f / (2 - f);
    
    const A = a / (1 + n) * (1 + n * n / 4 + n * n * n * n / 64);
    
    const t = Math.tan(lat);
    const C = e2 * Math.cos(lat) * Math.cos(lat) / (1 - e2);
    const A_coeff = Math.cos(lat) * (lon - lon0Rad);
    const nu = a / Math.sqrt(1 - e2 * Math.sin(lat) * Math.sin(lat));
    
    const x = k0 * nu * (A_coeff + (1 - t * t + C) * A_coeff * A_coeff * A_coeff / 6);
    const y = k0 * (this.meridionalArc(lat, a, e2) + nu * Math.tan(lat) * (A_coeff * A_coeff / 2));
    
    return {
      x: x + 500000, // 东偏移
      y: coord.latitude >= 0 ? y : y + 10000000, // 北半球/南半球
      z: coord.altitude || 0
    };
  }
  
  /**
   * 计算子午线弧长
   */
  private static meridionalArc(lat: number, a: number, e2: number): number {
    const e4 = e2 * e2;
    const e6 = e4 * e2;
    
    const A0 = a * (1 - e2 / 4 - 3 * e4 / 64 - 5 * e6 / 256);
    const A2 = 3 * a * (e2 + e4 / 4 + 15 * e6 / 128) / 8;
    const A4 = 15 * a * (e4 + 3 * e6 / 4) / 256;
    const A6 = 35 * a * e6 / 3072;
    
    return A0 * lat - A2 * Math.sin(2 * lat) + A4 * Math.sin(4 * lat) - A6 * Math.sin(6 * lat);
  }
  
  /**
   * 判断是否在中国境外
   */
  private static outOfChina(lat: number, lng: number): boolean {
    return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271;
  }
  
  /**
   * 纬度转换
   */
  private static transformLat(lng: number, lat: number): number {
    let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * this.PI) + 20.0 * Math.sin(2.0 * lng * this.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lat * this.PI) + 40.0 * Math.sin(lat / 3.0 * this.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(lat / 12.0 * this.PI) + 320 * Math.sin(lat * this.PI / 30.0)) * 2.0 / 3.0;
    return ret;
  }
  
  /**
   * 经度转换
   */
  private static transformLng(lng: number, lat: number): number {
    let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
    ret += (20.0 * Math.sin(6.0 * lng * this.PI) + 20.0 * Math.sin(2.0 * lng * this.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(lng * this.PI) + 40.0 * Math.sin(lng / 3.0 * this.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(lng / 12.0 * this.PI) + 300.0 * Math.sin(lng / 30.0 * this.PI)) * 2.0 / 3.0;
    return ret;
  }
}

/**
 * 坐标系统管理器
 */
export class CoordinateSystemManager {
  private static instance: CoordinateSystemManager;
  private currentSystem: CoordinateSystemType = CoordinateSystemType.WGS84;
  
  static getInstance(): CoordinateSystemManager {
    if (!this.instance) {
      this.instance = new CoordinateSystemManager();
    }
    return this.instance;
  }
  
  /**
   * 设置当前坐标系统
   */
  setCurrentSystem(system: CoordinateSystemType): void {
    this.currentSystem = system;
  }
  
  /**
   * 获取当前坐标系统
   */
  getCurrentSystem(): CoordinateSystemType {
    return this.currentSystem;
  }
  
  /**
   * 转换坐标到当前系统
   */
  transformToCurrentSystem(
    coord: GeographicCoordinate, 
    sourceSystem: CoordinateSystemType
  ): GeographicCoordinate {
    if (sourceSystem === this.currentSystem) {
      return { ...coord };
    }
    
    return this.transform(coord, sourceSystem, this.currentSystem);
  }
  
  /**
   * 坐标系统转换
   */
  transform(
    coord: GeographicCoordinate,
    sourceSystem: CoordinateSystemType,
    targetSystem: CoordinateSystemType
  ): GeographicCoordinate {
    if (sourceSystem === targetSystem) {
      return { ...coord };
    }
    
    // 转换路径：源坐标系 -> WGS84 -> 目标坐标系
    let wgs84Coord = coord;
    
    // 第一步：转换到WGS84
    switch (sourceSystem) {
      case CoordinateSystemType.GCJ02:
        wgs84Coord = CoordinateTransformer.gcj02ToWgs84(coord);
        break;
      case CoordinateSystemType.BD09:
        const gcj02Coord = CoordinateTransformer.bd09ToGcj02(coord);
        wgs84Coord = CoordinateTransformer.gcj02ToWgs84(gcj02Coord);
        break;
      case CoordinateSystemType.WGS84:
        wgs84Coord = coord;
        break;
    }
    
    // 第二步：从WGS84转换到目标坐标系
    switch (targetSystem) {
      case CoordinateSystemType.WGS84:
        return wgs84Coord;
      case CoordinateSystemType.GCJ02:
        return CoordinateTransformer.wgs84ToGcj02(wgs84Coord);
      case CoordinateSystemType.BD09:
        const gcj02Result = CoordinateTransformer.wgs84ToGcj02(wgs84Coord);
        return CoordinateTransformer.gcj02ToBd09(gcj02Result);
      default:
        return wgs84Coord;
    }
  }
}

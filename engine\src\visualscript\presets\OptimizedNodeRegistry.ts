/**
 * OptimizedNodeRegistry.ts
 * 
 * 优化的节点注册系统 - 统一管理所有节点类型的注册
 */

import { NodeRegistry } from '../nodes/NodeRegistry';

// 导入核心节点注册函数
import { registerCoreNodes } from './CoreNodes';
import { registerLogicNodes } from './LogicNodes';
import { registerEntityNodes } from './EntityNodes';
import { registerMathNodes } from './MathNodes';
import { registerPhysicsNodes } from './PhysicsNodes';
import { registerSoftBodyNodes } from './SoftBodyNodes';
import { registerAnimationNodes } from './AnimationNodes';
import { registerInputNodes } from './InputNodes';
import { registerNetworkNodes } from './NetworkNodes';
import { registerHTTPNodes } from './HTTPNodes';
import { registerJSONNodes } from './JSONNodes';
import { registerDateTimeNodes } from './DateTimeNodes';
import { registerAINodes } from './AINodes';
import { registerDatabaseNodes } from './DatabaseNodes';
import { registerCryptographyNodes } from './CryptographyNodes';

// 导入优化的节点注册函数
import { registerOptimizedUINodes } from './UINodes_Optimized';
import { registerOptimizedFileSystemNodes } from './FileSystemNodes_Optimized';

// 导入原有节点注册函数（需要逐步替换）
import { registerUINodes } from './UINodes';
import { registerFileSystemNodes } from './FileSystemNodes';
import { registerImageProcessingNodes } from './ImageProcessingNodes';

// 导入新增的高优先级节点
import { registerRenderingNodes } from './RenderingNodes';
import { registerSceneManagementNodes } from './SceneManagementNodes';
import { registerAssetManagementNodes } from './AssetManagementNodes';
import { registerPostProcessingNodes } from './PostProcessingNodes';
import { registerNLPNodes } from './NLPNodes';
import { registerSpatialNodes } from './SpatialNodes';

// 导入新增的中优先级节点
import { registerAdvancedAnimationNodes } from './AdvancedAnimationNodes';
import { registerAdvancedUILayoutNodes } from './AdvancedUILayoutNodes';
import { registerNetworkOptimizationNodes } from './NetworkOptimizationNodes';

// 导入新增的低优先级节点
import { registerTerrainSystemNodes } from './TerrainSystemNodes';
import { registerVegetationSystemNodes } from './VegetationSystemNodes';
import { registerBlockchainSystemNodes } from './BlockchainSystemNodes';
import { registerFluidSimulationNodes } from './FluidSimulationNodes';

// 导入虚拟化身定制节点
import { registerAvatarCustomizationNodes } from './AvatarCustomizationNodes';
import { registerAvatarPreviewNodes } from './AvatarPreviewNodes';
import { registerAvatarSaveNodes } from './AvatarSaveNodes';
import { registerAvatarSceneNodes } from './AvatarSceneNodes';
import { registerAvatarControlNodes } from './AvatarControlNodes';
import { registerAvatarUploadNodes } from './AvatarUploadNodes';

// 导入骨骼动画节点
import { registerSkeletonAnimationNodes } from './SkeletonAnimationNodes';

/**
 * 节点注册配置
 */
export interface NodeRegistrationConfig {
  /** 是否使用优化的节点 */
  useOptimizedNodes?: boolean;
  /** 是否启用调试模式 */
  debugMode?: boolean;
  /** 要排除的节点类型 */
  excludeNodeTypes?: string[];
  /** 要包含的节点类型 */
  includeNodeTypes?: string[];
}

/**
 * 注册所有节点类型到指定的注册表
 * @param registry 节点注册表
 * @param config 注册配置
 */
export function registerAllNodes(
  registry: NodeRegistry, 
  config: NodeRegistrationConfig = {}
): void {
  const {
    useOptimizedNodes = true,
    debugMode = false,
    excludeNodeTypes = [],
    includeNodeTypes = []
  } = config;

  // 记录注册开始时间
  const startTime = performance.now();
  let registeredCount = 0;

  /**
   * 安全注册节点类型
   */
  function safeRegister(
    name: string, 
    registerFunc: (registry: NodeRegistry) => void
  ): void {
    // 检查是否在排除列表中
    if (excludeNodeTypes.includes(name)) {
      if (debugMode) {
        console.log(`跳过注册节点类型: ${name} (在排除列表中)`);
      }
      return;
    }

    // 检查是否在包含列表中（如果指定了包含列表）
    if (includeNodeTypes.length > 0 && !includeNodeTypes.includes(name)) {
      if (debugMode) {
        console.log(`跳过注册节点类型: ${name} (不在包含列表中)`);
      }
      return;
    }

    try {
      const nodeStartTime = performance.now();
      registerFunc(registry);
      const nodeEndTime = performance.now();
      
      registeredCount++;
      
      if (debugMode) {
        console.log(`✅ 已注册节点类型: ${name} (耗时: ${(nodeEndTime - nodeStartTime).toFixed(2)}ms)`);
      }
    } catch (error) {
      console.error(`❌ 注册节点类型失败: ${name}`, error);
    }
  }

  // 注册核心节点类型
  if (debugMode) {
    console.log('开始注册视觉脚本节点...');
  }

  // 1. 核心节点（必须）
  safeRegister('CoreNodes', registerCoreNodes);
  safeRegister('LogicNodes', registerLogicNodes);
  safeRegister('MathNodes', registerMathNodes);

  // 2. 实体和物理节点
  safeRegister('EntityNodes', registerEntityNodes);
  safeRegister('PhysicsNodes', registerPhysicsNodes);
  safeRegister('SoftBodyNodes', registerSoftBodyNodes);

  // 3. 动画和输入节点
  safeRegister('AnimationNodes', registerAnimationNodes);
  safeRegister('InputNodes', registerInputNodes);

  // 3.1 空间信息系统节点（新增）
  safeRegister('SpatialNodes', registerSpatialNodes);

  // 4. 网络和数据节点
  safeRegister('NetworkNodes', registerNetworkNodes);
  safeRegister('HTTPNodes', registerHTTPNodes);
  safeRegister('JSONNodes', registerJSONNodes);
  safeRegister('DateTimeNodes', registerDateTimeNodes);

  // 5. AI和数据库节点
  safeRegister('AINodes', registerAINodes);
  safeRegister('NLPNodes', registerNLPNodes);
  safeRegister('DatabaseNodes', registerDatabaseNodes);
  safeRegister('CryptographyNodes', registerCryptographyNodes);

  // 6. UI节点（使用优化版本或原版本）
  if (useOptimizedNodes) {
    safeRegister('OptimizedUINodes', registerOptimizedUINodes);
  } else {
    safeRegister('UINodes', registerUINodes);
  }

  // 7. 文件系统节点（使用优化版本或原版本）
  if (useOptimizedNodes) {
    safeRegister('OptimizedFileSystemNodes', registerOptimizedFileSystemNodes);
  } else {
    safeRegister('FileSystemNodes', registerFileSystemNodes);
  }

  // 8. 图像处理节点（暂时使用原版本，待优化）
  safeRegister('ImageProcessingNodes', registerImageProcessingNodes);

  // 9. 新增的高优先级节点
  safeRegister('RenderingNodes', registerRenderingNodes);
  safeRegister('SceneManagementNodes', registerSceneManagementNodes);
  safeRegister('AssetManagementNodes', registerAssetManagementNodes);
  safeRegister('PostProcessingNodes', registerPostProcessingNodes);

  // 10. 新增的中优先级节点
  safeRegister('AdvancedAnimationNodes', registerAdvancedAnimationNodes);
  safeRegister('AdvancedUILayoutNodes', registerAdvancedUILayoutNodes);
  safeRegister('NetworkOptimizationNodes', registerNetworkOptimizationNodes);

  // 11. 新增的低优先级节点
  safeRegister('TerrainSystemNodes', registerTerrainSystemNodes);
  safeRegister('VegetationSystemNodes', registerVegetationSystemNodes);
  safeRegister('BlockchainSystemNodes', registerBlockchainSystemNodes);
  safeRegister('FluidSimulationNodes', registerFluidSimulationNodes);

  // 12. 虚拟化身定制节点
  safeRegister('AvatarCustomizationNodes', registerAvatarCustomizationNodes);
  safeRegister('AvatarPreviewNodes', registerAvatarPreviewNodes);
  safeRegister('AvatarSaveNodes', registerAvatarSaveNodes);
  safeRegister('AvatarSceneNodes', registerAvatarSceneNodes);
  safeRegister('AvatarControlNodes', registerAvatarControlNodes);
  safeRegister('AvatarUploadNodes', registerAvatarUploadNodes);

  // 13. 骨骼动画节点
  safeRegister('SkeletonAnimationNodes', registerSkeletonAnimationNodes);

  // 记录注册完成时间
  const endTime = performance.now();
  const totalTime = endTime - startTime;

  if (debugMode) {
    console.log(`🎉 节点注册完成！`);
    console.log(`📊 统计信息:`);
    console.log(`   - 已注册节点类型: ${registeredCount}`);
    console.log(`   - 总耗时: ${totalTime.toFixed(2)}ms`);
    console.log(`   - 平均耗时: ${(totalTime / registeredCount).toFixed(2)}ms/类型`);
    console.log(`   - 使用优化节点: ${useOptimizedNodes ? '是' : '否'}`);
  }
}

/**
 * 获取节点注册统计信息
 * @param registry 节点注册表
 * @returns 统计信息
 */
export function getNodeRegistrationStats(registry: NodeRegistry): {
  totalNodes: number;
  nodesByCategory: Record<string, number>;
  nodesByTags: Record<string, number>;
} {
  const allNodeTypes = registry.getAllNodeTypes();
  const totalNodes = allNodeTypes.length;
  
  const nodesByCategory: Record<string, number> = {};
  const nodesByTags: Record<string, number> = {};

  for (const nodeType of allNodeTypes) {
    const info = registry.getNodeTypeInfo(nodeType);
    if (info) {
      // 统计分类
      const category = info.category;
      nodesByCategory[category] = (nodesByCategory[category] || 0) + 1;

      // 统计标签
      if (info.tags) {
        for (const tag of info.tags) {
          nodesByTags[tag] = (nodesByTags[tag] || 0) + 1;
        }
      }
    }
  }

  return {
    totalNodes,
    nodesByCategory,
    nodesByTags
  };
}

/**
 * 验证节点注册完整性
 * @param registry 节点注册表
 * @returns 验证结果
 */
export function validateNodeRegistration(registry: NodeRegistry): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  stats: ReturnType<typeof getNodeRegistrationStats>;
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const stats = getNodeRegistrationStats(registry);

  // 检查必需的节点类型
  const requiredNodeTypes = [
    'core/events/onStart',
    'core/events/onUpdate',
    'core/flow/sequence',
    'math/basic/add',
    'logic/compare'
  ];

  for (const nodeType of requiredNodeTypes) {
    if (!registry.hasNodeType(nodeType)) {
      errors.push(`缺少必需的节点类型: ${nodeType}`);
    }
  }

  // 检查节点数量
  if (stats.totalNodes < 50) {
    warnings.push(`注册的节点数量较少: ${stats.totalNodes}，可能存在注册问题`);
  }

  // 检查分类覆盖
  const expectedCategories = ['EVENT', 'FLOW', 'MATH', 'LOGIC', 'UI', 'FILE'];
  for (const category of expectedCategories) {
    if (!stats.nodesByCategory[category]) {
      warnings.push(`缺少分类: ${category}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
    stats
  };
}

/**
 * 打印节点注册报告
 * @param registry 节点注册表
 */
export function printNodeRegistrationReport(registry: NodeRegistry): void {
  const validation = validateNodeRegistration(registry);
  
  console.log('\n📋 节点注册报告');
  console.log('='.repeat(50));
  
  console.log(`\n📊 统计信息:`);
  console.log(`   总节点数: ${validation.stats.totalNodes}`);
  
  console.log(`\n📂 按分类统计:`);
  for (const [category, count] of Object.entries(validation.stats.nodesByCategory)) {
    console.log(`   ${category}: ${count} 个节点`);
  }
  
  console.log(`\n🏷️ 按标签统计 (前10个):`);
  const sortedTags = Object.entries(validation.stats.nodesByTags)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10);
  for (const [tag, count] of sortedTags) {
    console.log(`   ${tag}: ${count} 个节点`);
  }
  
  if (validation.errors.length > 0) {
    console.log(`\n❌ 错误 (${validation.errors.length}):`);
    for (const error of validation.errors) {
      console.log(`   - ${error}`);
    }
  }
  
  if (validation.warnings.length > 0) {
    console.log(`\n⚠️ 警告 (${validation.warnings.length}):`);
    for (const warning of validation.warnings) {
      console.log(`   - ${warning}`);
    }
  }
  
  console.log(`\n✅ 注册状态: ${validation.isValid ? '正常' : '存在问题'}`);
  console.log('='.repeat(50));
}

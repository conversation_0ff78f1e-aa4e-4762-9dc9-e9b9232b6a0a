/**
 * 安全模块类型定义
 */

/**
 * 安全级别
 */
export enum SecurityLevel {
  PUBLIC = 'public',
  INTERNAL = 'internal',
  CONFIDENTIAL = 'confidential',
  SECRET = 'secret',
  TOP_SECRET = 'top_secret'
}

/**
 * 权限类型
 */
export enum Permission {
  READ = 'read',
  WRITE = 'write',
  EXECUTE = 'execute',
  DELETE = 'delete',
  ADMIN = 'admin'
}

/**
 * 用户角色
 */
export enum UserRole {
  GUEST = 'guest',
  USER = 'user',
  MODERATOR = 'moderator',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin'
}

/**
 * 审计事件类型
 */
export enum AuditEventType {
  LOGIN = 'login',
  LOGOUT = 'logout',
  ACCESS_GRANTED = 'access_granted',
  ACCESS_DENIED = 'access_denied',
  DATA_ACCESS = 'data_access',
  DATA_MODIFICATION = 'data_modification',
  SYSTEM_CHANGE = 'system_change',
  SECURITY_VIOLATION = 'security_violation',
  ERROR = 'error'
}

/**
 * 用户信息
 */
export interface User {
  id: string;
  username: string;
  email: string;
  roles: UserRole[];
  permissions: Permission[];
  securityLevel: SecurityLevel;
  isActive: boolean;
  lastLogin?: Date;
  metadata?: Record<string, any>;
}

/**
 * 访问控制规则
 */
export interface AccessRule {
  id: string;
  resource: string;
  action: Permission;
  roles: UserRole[];
  conditions?: AccessCondition[];
  priority: number;
  isActive: boolean;
}

/**
 * 访问条件
 */
export interface AccessCondition {
  type: 'time' | 'location' | 'device' | 'custom';
  operator: 'eq' | 'ne' | 'in' | 'nin' | 'gt' | 'lt' | 'between';
  value: any;
  metadata?: Record<string, any>;
}

/**
 * 审计日志条目
 */
export interface AuditLogEntry {
  id: string;
  timestamp: Date;
  eventType: AuditEventType;
  userId?: string;
  username?: string;
  resource?: string;
  action?: string;
  result: 'success' | 'failure' | 'error';
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * 安全配置
 */
export interface SecurityConfig {
  enableAccessControl: boolean;
  enableAuditLogging: boolean;
  enableDataEncryption: boolean;
  sessionTimeout: number;
  maxLoginAttempts: number;
  passwordPolicy: PasswordPolicy;
  encryptionAlgorithm: string;
  auditRetentionDays: number;
}

/**
 * 密码策略
 */
export interface PasswordPolicy {
  minLength: number;
  maxLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  preventReuse: number;
  expirationDays: number;
}

/**
 * 会话信息
 */
export interface Session {
  id: string;
  userId: string;
  username: string;
  roles: UserRole[];
  permissions: Permission[];
  createdAt: Date;
  lastActivity: Date;
  expiresAt: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  metadata?: Record<string, any>;
}

/**
 * 安全事件
 */
export interface SecurityEvent {
  id: string;
  type: 'authentication' | 'authorization' | 'data_access' | 'system' | 'threat';
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  source: string;
  description: string;
  userId?: string;
  resource?: string;
  details?: Record<string, any>;
  resolved: boolean;
  resolvedAt?: Date;
  resolvedBy?: string;
}

/**
 * 威胁检测规则
 */
export interface ThreatDetectionRule {
  id: string;
  name: string;
  description: string;
  type: 'brute_force' | 'suspicious_activity' | 'data_exfiltration' | 'privilege_escalation';
  conditions: ThreatCondition[];
  actions: ThreatAction[];
  isActive: boolean;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

/**
 * 威胁条件
 */
export interface ThreatCondition {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'lt' | 'contains' | 'pattern';
  value: any;
  timeWindow?: number; // 时间窗口（毫秒）
  threshold?: number; // 阈值
}

/**
 * 威胁响应动作
 */
export interface ThreatAction {
  type: 'log' | 'alert' | 'block' | 'quarantine' | 'notify';
  parameters: Record<string, any>;
  delay?: number;
}

/**
 * 数据分类
 */
export interface DataClassification {
  id: string;
  name: string;
  level: SecurityLevel;
  description: string;
  rules: DataClassificationRule[];
  retention: DataRetentionPolicy;
  encryption: EncryptionRequirement;
}

/**
 * 数据分类规则
 */
export interface DataClassificationRule {
  field: string;
  pattern: string;
  weight: number;
}

/**
 * 数据保留策略
 */
export interface DataRetentionPolicy {
  retentionPeriod: number; // 保留期（天）
  archiveAfter: number; // 归档时间（天）
  deleteAfter: number; // 删除时间（天）
  backupRequired: boolean;
}

/**
 * 加密要求
 */
export interface EncryptionRequirement {
  required: boolean;
  algorithm: string;
  keySize: number;
  rotationPeriod: number; // 密钥轮换期（天）
}

/**
 * 安全策略
 */
export interface SecurityPolicy {
  id: string;
  name: string;
  description: string;
  type: 'access_control' | 'data_protection' | 'audit' | 'threat_detection';
  rules: any[];
  isActive: boolean;
  priority: number;
  effectiveFrom: Date;
  effectiveTo?: Date;
  approvedBy: string;
  approvedAt: Date;
}

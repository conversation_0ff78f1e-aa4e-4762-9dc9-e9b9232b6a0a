/**
 * 空间信息系统视觉脚本节点
 * 为视觉脚本系统提供空间信息相关的节点
 */
import { 
  VisualScriptNode, 
  NodeInput, 
  NodeOutput, 
  NodeCategory,
  ExecutionContext 
} from '../base/VisualScriptNode';
import { 
  GeographicCoordinate, 
  CoordinateSystemType, 
  CoordinateTransformer,
  CoordinateSystemManager 
} from '../../spatial/coordinate/CoordinateSystem';
import { GeospatialComponent } from '../../spatial/components/GeospatialComponent';
import { SpatialAnalysisEngine, BufferAnalysisParams } from '../../spatial/analysis/SpatialAnalysisEngine';
import { Entity } from '../../core/Entity';

/**
 * 创建地理坐标节点
 */
export class CreateGeographicCoordinateNode extends VisualScriptNode {
  constructor() {
    super('CreateGeographicCoordinate', '创建地理坐标', NodeCategory.SPATIAL);
    
    this.addInput('longitude', '经度', 'number', 0);
    this.addInput('latitude', '纬度', 'number', 0);
    this.addInput('altitude', '海拔', 'number', 0);
    
    this.addOutput('coordinate', '坐标', 'GeographicCoordinate');
  }
  
  execute(context: ExecutionContext): void {
    const longitude = this.getInputValue('longitude', context) as number;
    const latitude = this.getInputValue('latitude', context) as number;
    const altitude = this.getInputValue('altitude', context) as number;
    
    const coordinate: GeographicCoordinate = {
      longitude,
      latitude,
      altitude: altitude || undefined
    };
    
    this.setOutputValue('coordinate', coordinate, context);
  }
}

/**
 * 坐标转换节点
 */
export class CoordinateTransformNode extends VisualScriptNode {
  constructor() {
    super('CoordinateTransform', '坐标转换', NodeCategory.SPATIAL);
    
    this.addInput('coordinate', '输入坐标', 'GeographicCoordinate');
    this.addInput('sourceSystem', '源坐标系', 'string', 'WGS84');
    this.addInput('targetSystem', '目标坐标系', 'string', 'GCJ02');
    
    this.addOutput('transformedCoordinate', '转换后坐标', 'GeographicCoordinate');
    this.addOutput('success', '转换成功', 'boolean');
  }
  
  execute(context: ExecutionContext): void {
    const coordinate = this.getInputValue('coordinate', context) as GeographicCoordinate;
    const sourceSystem = this.getInputValue('sourceSystem', context) as string;
    const targetSystem = this.getInputValue('targetSystem', context) as string;
    
    try {
      const manager = CoordinateSystemManager.getInstance();
      const result = manager.transform(
        coordinate,
        sourceSystem as CoordinateSystemType,
        targetSystem as CoordinateSystemType
      );
      
      this.setOutputValue('transformedCoordinate', result, context);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('坐标转换失败:', error);
      this.setOutputValue('transformedCoordinate', coordinate, context);
      this.setOutputValue('success', false, context);
    }
  }
}

/**
 * 创建地理空间组件节点
 */
export class CreateGeospatialComponentNode extends VisualScriptNode {
  constructor() {
    super('CreateGeospatialComponent', '创建地理空间组件', NodeCategory.SPATIAL);
    
    this.addInput('coordinate', '地理坐标', 'GeographicCoordinate');
    this.addInput('geometryType', '几何类型', 'string', 'Point');
    this.addInput('coordinateSystem', '坐标系', 'string', 'WGS84');
    this.addInput('properties', '属性', 'object', {});
    
    this.addOutput('component', '地理空间组件', 'GeospatialComponent');
  }
  
  execute(context: ExecutionContext): void {
    const coordinate = this.getInputValue('coordinate', context) as GeographicCoordinate;
    const geometryType = this.getInputValue('geometryType', context) as 'Point' | 'LineString' | 'Polygon';
    const coordinateSystem = this.getInputValue('coordinateSystem', context) as CoordinateSystemType;
    const properties = this.getInputValue('properties', context) as any;
    
    const component = new GeospatialComponent(coordinate, coordinateSystem, geometryType);
    component.setProperties(properties);
    
    this.setOutputValue('component', component, context);
  }
}

/**
 * 添加地理空间组件到实体节点
 */
export class AddGeospatialComponentNode extends VisualScriptNode {
  constructor() {
    super('AddGeospatialComponent', '添加地理空间组件', NodeCategory.SPATIAL);
    
    this.addInput('entity', '实体', 'Entity');
    this.addInput('component', '地理空间组件', 'GeospatialComponent');
    this.addInput('execute', '执行', 'exec');
    
    this.addOutput('onComplete', '完成', 'exec');
    this.addOutput('success', '成功', 'boolean');
  }
  
  execute(context: ExecutionContext): void {
    const entity = this.getInputValue('entity', context) as Entity;
    const component = this.getInputValue('component', context) as GeospatialComponent;
    
    try {
      entity.addComponent(component);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('添加地理空间组件失败:', error);
      this.setOutputValue('success', false, context);
    }
    
    this.executeOutput('onComplete', context);
  }
}

/**
 * 获取地理坐标节点
 */
export class GetGeographicCoordinateNode extends VisualScriptNode {
  constructor() {
    super('GetGeographicCoordinate', '获取地理坐标', NodeCategory.SPATIAL);
    
    this.addInput('component', '地理空间组件', 'GeospatialComponent');
    
    this.addOutput('coordinate', '地理坐标', 'GeographicCoordinate');
    this.addOutput('longitude', '经度', 'number');
    this.addOutput('latitude', '纬度', 'number');
    this.addOutput('altitude', '海拔', 'number');
  }
  
  execute(context: ExecutionContext): void {
    const component = this.getInputValue('component', context) as GeospatialComponent;
    
    if (component) {
      const coordinate = component.getGeographicCoordinate();
      
      this.setOutputValue('coordinate', coordinate, context);
      this.setOutputValue('longitude', coordinate.longitude, context);
      this.setOutputValue('latitude', coordinate.latitude, context);
      this.setOutputValue('altitude', coordinate.altitude || 0, context);
    }
  }
}

/**
 * 设置地理坐标节点
 */
export class SetGeographicCoordinateNode extends VisualScriptNode {
  constructor() {
    super('SetGeographicCoordinate', '设置地理坐标', NodeCategory.SPATIAL);
    
    this.addInput('component', '地理空间组件', 'GeospatialComponent');
    this.addInput('coordinate', '新坐标', 'GeographicCoordinate');
    this.addInput('execute', '执行', 'exec');
    
    this.addOutput('onComplete', '完成', 'exec');
    this.addOutput('success', '成功', 'boolean');
  }
  
  execute(context: ExecutionContext): void {
    const component = this.getInputValue('component', context) as GeospatialComponent;
    const coordinate = this.getInputValue('coordinate', context) as GeographicCoordinate;
    
    try {
      component.setGeographicCoordinate(coordinate);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('设置地理坐标失败:', error);
      this.setOutputValue('success', false, context);
    }
    
    this.executeOutput('onComplete', context);
  }
}

/**
 * 计算距离节点
 */
export class CalculateDistanceNode extends VisualScriptNode {
  constructor() {
    super('CalculateDistance', '计算距离', NodeCategory.SPATIAL);
    
    this.addInput('coordinate1', '坐标1', 'GeographicCoordinate');
    this.addInput('coordinate2', '坐标2', 'GeographicCoordinate');
    
    this.addOutput('distance', '距离(米)', 'number');
    this.addOutput('distanceKm', '距离(公里)', 'number');
  }
  
  execute(context: ExecutionContext): void {
    const coord1 = this.getInputValue('coordinate1', context) as GeographicCoordinate;
    const coord2 = this.getInputValue('coordinate2', context) as GeographicCoordinate;
    
    if (coord1 && coord2) {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const distance = analysisEngine.haversineDistance(coord1, coord2);
      
      this.setOutputValue('distance', distance, context);
      this.setOutputValue('distanceKm', distance / 1000, context);
    } else {
      this.setOutputValue('distance', 0, context);
      this.setOutputValue('distanceKm', 0, context);
    }
  }
}

/**
 * 缓冲区分析节点
 */
export class BufferAnalysisNode extends VisualScriptNode {
  constructor() {
    super('BufferAnalysis', '缓冲区分析', NodeCategory.SPATIAL);
    
    this.addInput('geometry', '输入几何', 'object');
    this.addInput('distance', '缓冲距离', 'number', 100);
    this.addInput('unit', '距离单位', 'string', 'meters');
    this.addInput('segments', '分段数', 'number', 32);
    this.addInput('execute', '执行', 'exec');
    
    this.addOutput('onComplete', '完成', 'exec');
    this.addOutput('result', '缓冲区几何', 'object');
    this.addOutput('success', '成功', 'boolean');
    this.addOutput('error', '错误信息', 'string');
  }
  
  execute(context: ExecutionContext): void {
    const geometry = this.getInputValue('geometry', context) as any;
    const distance = this.getInputValue('distance', context) as number;
    const unit = this.getInputValue('unit', context) as 'meters' | 'kilometers' | 'degrees';
    const segments = this.getInputValue('segments', context) as number;
    
    const params: BufferAnalysisParams = {
      distance,
      unit,
      segments
    };
    
    try {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const result = analysisEngine.buffer(geometry, params);
      
      this.setOutputValue('result', result.result, context);
      this.setOutputValue('success', result.success, context);
      this.setOutputValue('error', result.error || '', context);
    } catch (error) {
      this.setOutputValue('result', null, context);
      this.setOutputValue('success', false, context);
      this.setOutputValue('error', error instanceof Error ? error.message : '分析失败', context);
    }
    
    this.executeOutput('onComplete', context);
  }
}

/**
 * 相交分析节点
 */
export class IntersectionAnalysisNode extends VisualScriptNode {
  constructor() {
    super('IntersectionAnalysis', '相交分析', NodeCategory.SPATIAL);
    
    this.addInput('geometry1', '几何1', 'object');
    this.addInput('geometry2', '几何2', 'object');
    this.addInput('execute', '执行', 'exec');
    
    this.addOutput('onComplete', '完成', 'exec');
    this.addOutput('result', '相交几何', 'object');
    this.addOutput('hasIntersection', '有相交', 'boolean');
    this.addOutput('success', '成功', 'boolean');
  }
  
  execute(context: ExecutionContext): void {
    const geometry1 = this.getInputValue('geometry1', context) as any;
    const geometry2 = this.getInputValue('geometry2', context) as any;
    
    try {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const result = analysisEngine.intersection(geometry1, geometry2);
      
      this.setOutputValue('result', result.result, context);
      this.setOutputValue('hasIntersection', !!result.result, context);
      this.setOutputValue('success', result.success, context);
    } catch (error) {
      this.setOutputValue('result', null, context);
      this.setOutputValue('hasIntersection', false, context);
      this.setOutputValue('success', false, context);
    }
    
    this.executeOutput('onComplete', context);
  }
}

/**
 * 点在多边形内判断节点
 */
export class PointInPolygonNode extends VisualScriptNode {
  constructor() {
    super('PointInPolygon', '点在多边形内', NodeCategory.SPATIAL);
    
    this.addInput('point', '点坐标', 'GeographicCoordinate');
    this.addInput('polygon', '多边形坐标', 'array');
    
    this.addOutput('inside', '在内部', 'boolean');
  }
  
  execute(context: ExecutionContext): void {
    const point = this.getInputValue('point', context) as GeographicCoordinate;
    const polygon = this.getInputValue('polygon', context) as GeographicCoordinate[];
    
    if (point && polygon && polygon.length >= 3) {
      const analysisEngine = SpatialAnalysisEngine.getInstance();
      const pointArray = [point.longitude, point.latitude];
      const polygonArray = polygon.map(coord => [coord.longitude, coord.latitude]);
      
      const inside = analysisEngine.pointInPolygon(pointArray, polygonArray);
      this.setOutputValue('inside', inside, context);
    } else {
      this.setOutputValue('inside', false, context);
    }
  }
}

/**
 * 创建GeoJSON节点
 */
export class CreateGeoJSONNode extends VisualScriptNode {
  constructor() {
    super('CreateGeoJSON', '创建GeoJSON', NodeCategory.SPATIAL);
    
    this.addInput('component', '地理空间组件', 'GeospatialComponent');
    
    this.addOutput('geoJSON', 'GeoJSON对象', 'object');
    this.addOutput('geoJSONString', 'GeoJSON字符串', 'string');
  }
  
  execute(context: ExecutionContext): void {
    const component = this.getInputValue('component', context) as GeospatialComponent;
    
    if (component) {
      const geoJSON = component.toGeoJSON();
      
      this.setOutputValue('geoJSON', geoJSON, context);
      this.setOutputValue('geoJSONString', JSON.stringify(geoJSON, null, 2), context);
    } else {
      this.setOutputValue('geoJSON', null, context);
      this.setOutputValue('geoJSONString', '', context);
    }
  }
}

/**
 * 从GeoJSON创建组件节点
 */
export class CreateFromGeoJSONNode extends VisualScriptNode {
  constructor() {
    super('CreateFromGeoJSON', '从GeoJSON创建', NodeCategory.SPATIAL);
    
    this.addInput('geoJSON', 'GeoJSON对象', 'object');
    this.addInput('coordinateSystem', '坐标系', 'string', 'WGS84');
    
    this.addOutput('component', '地理空间组件', 'GeospatialComponent');
    this.addOutput('success', '成功', 'boolean');
  }
  
  execute(context: ExecutionContext): void {
    const geoJSON = this.getInputValue('geoJSON', context) as any;
    const coordinateSystem = this.getInputValue('coordinateSystem', context) as CoordinateSystemType;
    
    try {
      const component = GeospatialComponent.fromGeoJSON(geoJSON, coordinateSystem);
      
      this.setOutputValue('component', component, context);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('从GeoJSON创建组件失败:', error);
      this.setOutputValue('component', null, context);
      this.setOutputValue('success', false, context);
    }
  }
}

/**
 * 设置地图视图节点
 */
export class SetMapViewNode extends VisualScriptNode {
  constructor() {
    super('SetMapView', '设置地图视图', NodeCategory.SPATIAL);

    this.addInput('spatialSystem', '空间系统', 'SpatialInfoSystem');
    this.addInput('center', '中心坐标', 'GeographicCoordinate');
    this.addInput('zoom', '缩放级别', 'number', 10);
    this.addInput('execute', '执行', 'exec');

    this.addOutput('onComplete', '完成', 'exec');
    this.addOutput('success', '成功', 'boolean');
  }

  execute(context: ExecutionContext): void {
    const spatialSystem = this.getInputValue('spatialSystem', context) as any;
    const center = this.getInputValue('center', context) as GeographicCoordinate;
    const zoom = this.getInputValue('zoom', context) as number;

    try {
      spatialSystem.setMapView(center, zoom);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('设置地图视图失败:', error);
      this.setOutputValue('success', false, context);
    }

    this.executeOutput('onComplete', context);
  }
}

/**
 * 获取地图视图节点
 */
export class GetMapViewNode extends VisualScriptNode {
  constructor() {
    super('GetMapView', '获取地图视图', NodeCategory.SPATIAL);

    this.addInput('spatialSystem', '空间系统', 'SpatialInfoSystem');

    this.addOutput('center', '中心坐标', 'GeographicCoordinate');
    this.addOutput('zoom', '缩放级别', 'number');
    this.addOutput('hasView', '有视图', 'boolean');
  }

  execute(context: ExecutionContext): void {
    const spatialSystem = this.getInputValue('spatialSystem', context) as any;

    try {
      const view = spatialSystem.getMapView();

      if (view) {
        this.setOutputValue('center', view.center, context);
        this.setOutputValue('zoom', view.zoom, context);
        this.setOutputValue('hasView', true, context);
      } else {
        this.setOutputValue('center', null, context);
        this.setOutputValue('zoom', 0, context);
        this.setOutputValue('hasView', false, context);
      }
    } catch (error) {
      console.error('获取地图视图失败:', error);
      this.setOutputValue('center', null, context);
      this.setOutputValue('zoom', 0, context);
      this.setOutputValue('hasView', false, context);
    }
  }
}

/**
 * 设置地图提供者节点
 */
export class SetMapProviderNode extends VisualScriptNode {
  constructor() {
    super('SetMapProvider', '设置地图提供者', NodeCategory.SPATIAL);

    this.addInput('spatialSystem', '空间系统', 'SpatialInfoSystem');
    this.addInput('provider', '提供者', 'string', 'osm');
    this.addInput('execute', '执行', 'exec');

    this.addOutput('onComplete', '完成', 'exec');
    this.addOutput('success', '成功', 'boolean');
  }

  execute(context: ExecutionContext): void {
    const spatialSystem = this.getInputValue('spatialSystem', context) as any;
    const provider = this.getInputValue('provider', context) as string;

    try {
      spatialSystem.setTileProvider(provider);
      this.setOutputValue('success', true, context);
    } catch (error) {
      console.error('设置地图提供者失败:', error);
      this.setOutputValue('success', false, context);
    }

    this.executeOutput('onComplete', context);
  }
}

// 导出所有空间节点
export const SPATIAL_NODES = [
  CreateGeographicCoordinateNode,
  CoordinateTransformNode,
  CreateGeospatialComponentNode,
  AddGeospatialComponentNode,
  GetGeographicCoordinateNode,
  SetGeographicCoordinateNode,
  CalculateDistanceNode,
  BufferAnalysisNode,
  IntersectionAnalysisNode,
  PointInPolygonNode,
  CreateGeoJSONNode,
  CreateFromGeoJSONNode,
  SetMapViewNode,
  GetMapViewNode,
  SetMapProviderNode
];

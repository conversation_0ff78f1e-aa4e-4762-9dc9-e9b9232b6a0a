/**
 * 安全管理器
 * 统一管理系统安全功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { AccessControl } from './AccessControl';
import { DataProtection } from './DataProtection';
import { AuditLogger } from './AuditLogger';
import { 
  SecurityConfig, 
  User, 
  Session, 
  SecurityEvent, 
  ThreatDetectionRule,
  AuditEventType,
  SecurityLevel
} from './types';

export class SecurityManager extends EventEmitter {
  private static instance: SecurityManager;
  private config: SecurityConfig;
  private accessControl: AccessControl;
  private dataProtection: DataProtection;
  private auditLogger: AuditLogger;
  private sessions: Map<string, Session> = new Map();
  private securityEvents: SecurityEvent[] = [];
  private threatRules: ThreatDetectionRule[] = [];
  private isInitialized: boolean = false;

  /**
   * 获取单例实例
   */
  public static getInstance(): SecurityManager {
    if (!SecurityManager.instance) {
      SecurityManager.instance = new SecurityManager();
    }
    return SecurityManager.instance;
  }

  private constructor() {
    super();
    
    // 默认安全配置
    this.config = {
      enableAccessControl: true,
      enableAuditLogging: true,
      enableDataEncryption: true,
      sessionTimeout: 3600000, // 1小时
      maxLoginAttempts: 5,
      passwordPolicy: {
        minLength: 8,
        maxLength: 128,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true,
        preventReuse: 5,
        expirationDays: 90
      },
      encryptionAlgorithm: 'AES-256-GCM',
      auditRetentionDays: 365
    };
  }

  /**
   * 初始化安全管理器
   * @param config 安全配置
   */
  public async initialize(config?: Partial<SecurityConfig>): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // 合并配置
    if (config) {
      this.config = { ...this.config, ...config };
    }

    // 初始化子模块
    this.accessControl = new AccessControl();
    this.dataProtection = new DataProtection(this.config);
    this.auditLogger = new AuditLogger(this.config);

    // 监听子模块事件
    this.accessControl.on('access-denied', (event) => {
      this.handleSecurityEvent('authorization', 'medium', 'Access denied', event);
    });

    this.dataProtection.on('encryption-error', (event) => {
      this.handleSecurityEvent('data_access', 'high', 'Encryption error', event);
    });

    // 启动会话清理定时器
    this.startSessionCleanup();

    // 启动威胁检测
    this.startThreatDetection();

    this.isInitialized = true;
    this.emit('initialized');
  }

  /**
   * 用户认证
   * @param username 用户名
   * @param password 密码
   * @param metadata 元数据
   * @returns 会话信息
   */
  public async authenticate(username: string, password: string, metadata: any = {}): Promise<Session | null> {
    try {
      // 检查登录尝试次数
      if (await this.isAccountLocked(username)) {
        await this.auditLogger.log({
          eventType: AuditEventType.LOGIN,
          username,
          result: 'failure',
          details: { reason: 'account_locked' },
          severity: 'medium'
        });
        return null;
      }

      // 验证用户凭据（这里应该连接到实际的用户存储）
      const user = await this.validateCredentials(username, password);
      if (!user) {
        await this.recordFailedLogin(username);
        await this.auditLogger.log({
          eventType: AuditEventType.LOGIN,
          username,
          result: 'failure',
          details: { reason: 'invalid_credentials' },
          severity: 'medium'
        });
        return null;
      }

      // 创建会话
      const session = await this.createSession(user, metadata);

      // 记录成功登录
      await this.auditLogger.log({
        eventType: AuditEventType.LOGIN,
        userId: user.id,
        username: user.username,
        result: 'success',
        sessionId: session.id,
        severity: 'low'
      });

      this.emit('user-authenticated', { user, session });
      return session;

    } catch (error) {
      await this.auditLogger.log({
        eventType: AuditEventType.ERROR,
        username,
        result: 'error',
        details: { error: String(error) },
        severity: 'high'
      });
      throw error;
    }
  }

  /**
   * 用户注销
   * @param sessionId 会话ID
   */
  public async logout(sessionId: string): Promise<void> {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.isActive = false;
      this.sessions.delete(sessionId);

      await this.auditLogger.log({
        eventType: AuditEventType.LOGOUT,
        userId: session.userId,
        username: session.username,
        sessionId: session.id,
        result: 'success',
        severity: 'low'
      });

      this.emit('user-logout', { session });
    }
  }

  /**
   * 验证会话
   * @param sessionId 会话ID
   * @returns 会话信息
   */
  public validateSession(sessionId: string): Session | null {
    const session = this.sessions.get(sessionId);
    
    if (!session || !session.isActive) {
      return null;
    }

    // 检查会话是否过期
    if (Date.now() > session.expiresAt.getTime()) {
      session.isActive = false;
      this.sessions.delete(sessionId);
      return null;
    }

    // 更新最后活动时间
    session.lastActivity = new Date();
    
    return session;
  }

  /**
   * 检查权限
   * @param sessionId 会话ID
   * @param resource 资源
   * @param action 动作
   * @returns 是否有权限
   */
  public async checkPermission(sessionId: string, resource: string, action: string): Promise<boolean> {
    const session = this.validateSession(sessionId);
    if (!session) {
      return false;
    }

    const hasPermission = await this.accessControl.checkPermission(session, resource, action);

    // 记录访问尝试
    await this.auditLogger.log({
      eventType: hasPermission ? AuditEventType.ACCESS_GRANTED : AuditEventType.ACCESS_DENIED,
      userId: session.userId,
      username: session.username,
      resource,
      action,
      result: hasPermission ? 'success' : 'failure',
      sessionId: session.id,
      severity: hasPermission ? 'low' : 'medium'
    });

    return hasPermission;
  }

  /**
   * 加密数据
   * @param data 原始数据
   * @param level 安全级别
   * @returns 加密后的数据
   */
  public async encryptData(data: any, level: SecurityLevel = SecurityLevel.INTERNAL): Promise<string> {
    return await this.dataProtection.encrypt(data, level);
  }

  /**
   * 解密数据
   * @param encryptedData 加密的数据
   * @param level 安全级别
   * @returns 解密后的数据
   */
  public async decryptData(encryptedData: string, level: SecurityLevel = SecurityLevel.INTERNAL): Promise<any> {
    return await this.dataProtection.decrypt(encryptedData, level);
  }

  /**
   * 添加威胁检测规则
   * @param rule 威胁检测规则
   */
  public addThreatDetectionRule(rule: ThreatDetectionRule): void {
    this.threatRules.push(rule);
  }

  /**
   * 获取安全事件
   * @param limit 限制数量
   * @returns 安全事件列表
   */
  public getSecurityEvents(limit: number = 100): SecurityEvent[] {
    return this.securityEvents.slice(-limit);
  }

  /**
   * 获取活动会话
   * @returns 活动会话列表
   */
  public getActiveSessions(): Session[] {
    return Array.from(this.sessions.values()).filter(session => session.isActive);
  }

  /**
   * 强制注销用户
   * @param userId 用户ID
   */
  public async forceLogoutUser(userId: string): Promise<void> {
    const userSessions = Array.from(this.sessions.values()).filter(
      session => session.userId === userId && session.isActive
    );

    for (const session of userSessions) {
      await this.logout(session.id);
    }
  }

  /**
   * 销毁安全管理器
   */
  public async destroy(): Promise<void> {
    if (this.accessControl) {
      await this.accessControl.destroy();
    }
    
    if (this.dataProtection) {
      await this.dataProtection.destroy();
    }
    
    if (this.auditLogger) {
      await this.auditLogger.destroy();
    }

    this.sessions.clear();
    this.securityEvents = [];
    this.threatRules = [];
    this.removeAllListeners();
    this.isInitialized = false;
  }

  /**
   * 验证用户凭据
   */
  private async validateCredentials(username: string, password: string): Promise<User | null> {
    // 这里应该连接到实际的用户存储系统
    // 简化实现，返回模拟用户
    if (username === 'admin' && password === 'admin123') {
      return {
        id: '1',
        username: 'admin',
        email: '<EMAIL>',
        roles: ['admin'],
        permissions: ['read', 'write', 'execute', 'delete', 'admin'],
        securityLevel: SecurityLevel.SECRET,
        isActive: true
      } as any;
    }
    return null;
  }

  /**
   * 创建会话
   */
  private async createSession(user: User, metadata: any): Promise<Session> {
    const sessionId = this.generateSessionId();
    const now = new Date();
    const expiresAt = new Date(now.getTime() + this.config.sessionTimeout);

    const session: Session = {
      id: sessionId,
      userId: user.id,
      username: user.username,
      roles: user.roles,
      permissions: user.permissions,
      createdAt: now,
      lastActivity: now,
      expiresAt,
      ipAddress: metadata.ipAddress || 'unknown',
      userAgent: metadata.userAgent || 'unknown',
      isActive: true,
      metadata
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * 生成会话ID
   */
  private generateSessionId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * 检查账户是否被锁定
   */
  private async isAccountLocked(username: string): Promise<boolean> {
    // 简化实现
    return false;
  }

  /**
   * 记录失败登录
   */
  private async recordFailedLogin(username: string): Promise<void> {
    // 简化实现
  }

  /**
   * 处理安全事件
   */
  private handleSecurityEvent(type: string, severity: string, description: string, details: any): void {
    const event: SecurityEvent = {
      id: this.generateEventId(),
      type: type as any,
      severity: severity as any,
      timestamp: new Date(),
      source: 'SecurityManager',
      description,
      details,
      resolved: false
    };

    this.securityEvents.push(event);
    this.emit('security-event', event);

    // 保持事件列表大小
    if (this.securityEvents.length > 10000) {
      this.securityEvents = this.securityEvents.slice(-5000);
    }
  }

  /**
   * 生成事件ID
   */
  private generateEventId(): string {
    return Math.random().toString(36).substring(2) + Date.now().toString(36);
  }

  /**
   * 启动会话清理
   */
  private startSessionCleanup(): void {
    setInterval(() => {
      const now = Date.now();
      const expiredSessions: string[] = [];

      this.sessions.forEach((session, sessionId) => {
        if (!session.isActive || now > session.expiresAt.getTime()) {
          expiredSessions.push(sessionId);
        }
      });

      expiredSessions.forEach(sessionId => {
        this.sessions.delete(sessionId);
      });
    }, 60000); // 每分钟清理一次
  }

  /**
   * 启动威胁检测
   */
  private startThreatDetection(): void {
    // 简化实现
    setInterval(() => {
      this.runThreatDetection();
    }, 30000); // 每30秒检测一次
  }

  /**
   * 运行威胁检测
   */
  private runThreatDetection(): void {
    // 简化实现
    this.threatRules.forEach(rule => {
      if (rule.isActive) {
        // 检测威胁逻辑
      }
    });
  }
}

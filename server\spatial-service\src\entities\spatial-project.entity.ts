/**
 * 空间项目实体
 */
import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  OneToMany, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index
} from 'typeorm';
import { SpatialLayer } from './spatial-layer.entity';

@Entity('spatial_projects')
@Index(['createdBy'])
@Index(['status'])
@Index(['createdAt'])
export class SpatialProject {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ 
    type: 'enum',
    enum: ['active', 'archived', 'deleted'],
    default: 'active'
  })
  status: 'active' | 'archived' | 'deleted';

  @Column({ type: 'jsonb', nullable: true })
  settings: {
    defaultCRS?: string; // 默认坐标参考系统
    defaultExtent?: [number, number, number, number]; // 默认范围
    defaultZoom?: number;
    defaultCenter?: [number, number];
    mapSettings?: {
      basemap?: string;
      enableMeasurement?: boolean;
      enableDrawing?: boolean;
      enableGeocoding?: boolean;
      enablePrint?: boolean;
      enableExport?: boolean;
    };
    analysisSettings?: {
      enableSpatialAnalysis?: boolean;
      enableStatistics?: boolean;
      enableReporting?: boolean;
      maxFeatureCount?: number;
      maxAnalysisArea?: number;
    };
    collaborationSettings?: {
      enableSharing?: boolean;
      enableComments?: boolean;
      enableVersionControl?: boolean;
      enableRealTimeEditing?: boolean;
    };
    [key: string]: any;
  };

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    tags?: string[];
    category?: string;
    keywords?: string[];
    license?: string;
    attribution?: string;
    contact?: {
      name?: string;
      email?: string;
      organization?: string;
    };
    version?: string;
    lastBackup?: string;
    statistics?: {
      layerCount?: number;
      featureCount?: number;
      totalSize?: number;
      lastActivity?: string;
    };
    [key: string]: any;
  };

  @OneToMany(() => SpatialLayer, layer => layer.project, { cascade: true })
  layers: SpatialLayer[];

  @Column({ type: 'jsonb', nullable: true })
  permissions: {
    owner?: string;
    admins?: string[];
    editors?: string[];
    viewers?: string[];
    public?: boolean;
    shareSettings?: {
      allowDownload?: boolean;
      allowPrint?: boolean;
      allowEmbed?: boolean;
      expiresAt?: string;
    };
  };

  @Column({ type: 'boolean', default: false })
  isTemplate: boolean;

  @Column({ type: 'varchar', nullable: true })
  templateCategory: string;

  @Column({ type: 'jsonb', nullable: true })
  backup: {
    enabled?: boolean;
    frequency?: 'daily' | 'weekly' | 'monthly';
    retention?: number; // 保留天数
    location?: string;
    lastBackup?: string;
    autoBackup?: boolean;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  // 计算属性
  get layerCount(): number {
    return this.layers ? this.layers.length : 0;
  }

  get totalFeatureCount(): number {
    if (!this.layers) return 0;
    return this.layers.reduce((total, layer) => total + layer.featureCount, 0);
  }

  get extent(): [number, number, number, number] | null {
    if (!this.layers || this.layers.length === 0) {
      return this.settings?.defaultExtent || null;
    }

    let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
    let hasExtent = false;

    this.layers.forEach(layer => {
      const layerExtent = layer.extent;
      if (layerExtent) {
        minX = Math.min(minX, layerExtent[0]);
        minY = Math.min(minY, layerExtent[1]);
        maxX = Math.max(maxX, layerExtent[2]);
        maxY = Math.max(maxY, layerExtent[3]);
        hasExtent = true;
      }
    });

    return hasExtent ? [minX, minY, maxX, maxY] : null;
  }

  get isPublic(): boolean {
    return this.permissions?.public || false;
  }

  get size(): number {
    return this.metadata?.statistics?.totalSize || 0;
  }

  // 辅助方法
  hasPermission(userId: string, action: 'read' | 'write' | 'admin' | 'owner'): boolean {
    if (!this.permissions) {
      return false;
    }

    // 检查所有者权限
    if (action === 'owner') {
      return this.permissions.owner === userId;
    }

    // 所有者拥有所有权限
    if (this.permissions.owner === userId) {
      return true;
    }

    // 检查管理员权限
    if (action === 'admin') {
      return this.permissions.admins?.includes(userId) || false;
    }

    // 管理员拥有读写权限
    if (this.permissions.admins?.includes(userId)) {
      return true;
    }

    // 检查编辑权限
    if (action === 'write') {
      return this.permissions.editors?.includes(userId) || false;
    }

    // 编辑者拥有读权限
    if (this.permissions.editors?.includes(userId)) {
      return true;
    }

    // 检查查看权限
    if (action === 'read') {
      return this.permissions.viewers?.includes(userId) || 
             this.permissions.public || false;
    }

    return false;
  }

  // 添加用户权限
  addUserPermission(userId: string, role: 'admin' | 'editor' | 'viewer'): void {
    if (!this.permissions) {
      this.permissions = {};
    }

    // 从其他角色中移除用户
    this.removeUserPermission(userId);

    // 添加到新角色
    switch (role) {
      case 'admin':
        if (!this.permissions.admins) this.permissions.admins = [];
        this.permissions.admins.push(userId);
        break;
      case 'editor':
        if (!this.permissions.editors) this.permissions.editors = [];
        this.permissions.editors.push(userId);
        break;
      case 'viewer':
        if (!this.permissions.viewers) this.permissions.viewers = [];
        this.permissions.viewers.push(userId);
        break;
    }
  }

  // 移除用户权限
  removeUserPermission(userId: string): void {
    if (!this.permissions) return;

    if (this.permissions.admins) {
      this.permissions.admins = this.permissions.admins.filter(id => id !== userId);
    }
    if (this.permissions.editors) {
      this.permissions.editors = this.permissions.editors.filter(id => id !== userId);
    }
    if (this.permissions.viewers) {
      this.permissions.viewers = this.permissions.viewers.filter(id => id !== userId);
    }
  }

  // 获取用户角色
  getUserRole(userId: string): 'owner' | 'admin' | 'editor' | 'viewer' | null {
    if (!this.permissions) return null;

    if (this.permissions.owner === userId) return 'owner';
    if (this.permissions.admins?.includes(userId)) return 'admin';
    if (this.permissions.editors?.includes(userId)) return 'editor';
    if (this.permissions.viewers?.includes(userId)) return 'viewer';

    return null;
  }

  // 添加图层
  addLayer(layer: SpatialLayer): void {
    if (!this.layers) {
      this.layers = [];
    }
    layer.projectId = this.id;
    this.layers.push(layer);
    this.updateStatistics();
  }

  // 移除图层
  removeLayer(layerId: string): boolean {
    if (!this.layers) return false;

    const index = this.layers.findIndex(l => l.id === layerId);
    if (index >= 0) {
      this.layers.splice(index, 1);
      this.updateStatistics();
      return true;
    }
    return false;
  }

  // 更新统计信息
  updateStatistics(): void {
    if (!this.metadata) {
      this.metadata = {};
    }
    if (!this.metadata.statistics) {
      this.metadata.statistics = {};
    }

    this.metadata.statistics.layerCount = this.layerCount;
    this.metadata.statistics.featureCount = this.totalFeatureCount;
    this.metadata.statistics.lastActivity = new Date().toISOString();

    // 计算总大小（简化实现）
    let totalSize = 0;
    if (this.layers) {
      this.layers.forEach(layer => {
        if (layer.features) {
          totalSize += layer.features.length * 1024; // 假设每个要素1KB
        }
      });
    }
    this.metadata.statistics.totalSize = totalSize;
  }

  // 导出项目配置
  exportConfig(): any {
    return {
      name: this.name,
      description: this.description,
      settings: this.settings,
      metadata: {
        ...this.metadata,
        exportedAt: new Date().toISOString(),
        version: this.metadata?.version || '1.0.0'
      },
      layers: this.layers ? this.layers.map(layer => ({
        name: layer.name,
        description: layer.description,
        layerType: layer.layerType,
        metadata: layer.metadata,
        style: layer.style,
        visible: layer.visible,
        editable: layer.editable,
        selectable: layer.selectable,
        zIndex: layer.zIndex,
        opacity: layer.opacity,
        minZoom: layer.minZoom,
        maxZoom: layer.maxZoom
      })) : []
    };
  }

  // 从配置导入
  static fromConfig(config: any, createdBy: string): Partial<SpatialProject> {
    return {
      name: config.name,
      description: config.description,
      settings: config.settings,
      metadata: {
        ...config.metadata,
        importedAt: new Date().toISOString()
      },
      createdBy,
      status: 'active'
    };
  }

  // 克隆项目
  clone(newName?: string, userId?: string): Partial<SpatialProject> {
    return {
      name: newName || `${this.name}_copy`,
      description: this.description,
      settings: JSON.parse(JSON.stringify(this.settings)),
      metadata: {
        ...JSON.parse(JSON.stringify(this.metadata)),
        clonedFrom: this.id,
        clonedAt: new Date().toISOString()
      },
      createdBy: userId || this.createdBy,
      status: 'active',
      isTemplate: false
    };
  }

  // 创建模板
  createTemplate(templateName: string, category?: string): Partial<SpatialProject> {
    return {
      name: templateName,
      description: `${this.description} (模板)`,
      settings: JSON.parse(JSON.stringify(this.settings)),
      metadata: {
        ...JSON.parse(JSON.stringify(this.metadata)),
        templateSource: this.id,
        createdAsTemplate: new Date().toISOString()
      },
      createdBy: this.createdBy,
      status: 'active',
      isTemplate: true,
      templateCategory: category || 'custom'
    };
  }

  // 验证项目配置
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.name || this.name.trim().length === 0) {
      errors.push('项目名称不能为空');
    }

    if (!this.createdBy) {
      errors.push('创建者不能为空');
    }

    if (this.settings?.defaultExtent) {
      const extent = this.settings.defaultExtent;
      if (extent.length !== 4 || extent[0] >= extent[2] || extent[1] >= extent[3]) {
        errors.push('默认范围格式不正确');
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 获取项目摘要
  getSummary(): {
    id: string;
    name: string;
    description: string;
    layerCount: number;
    featureCount: number;
    size: number;
    lastActivity: string;
    isPublic: boolean;
    userRole?: string;
  } {
    return {
      id: this.id,
      name: this.name,
      description: this.description || '',
      layerCount: this.layerCount,
      featureCount: this.totalFeatureCount,
      size: this.size,
      lastActivity: this.metadata?.statistics?.lastActivity || this.updatedAt.toISOString(),
      isPublic: this.isPublic
    };
  }
}

# 空间信息系统服务 Docker Compose 配置
version: '3.8'

services:
  # 空间服务
  spatial-service:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: spatial-service
    ports:
      - "3001:3001"
      - "9090:9090"  # 监控端口
    environment:
      - NODE_ENV=production
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USERNAME=postgres
      - DB_PASSWORD=spatial_password
      - DB_DATABASE=spatial_db
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - ./backups:/app/backups
    depends_on:
      - postgres
      - redis
    networks:
      - spatial-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/v1/spatial-data/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # PostgreSQL + PostGIS 数据库
  postgres:
    image: postgis/postgis:15-3.3-alpine
    container_name: spatial-postgres
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=spatial_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=spatial_password
      - POSTGRES_INITDB_ARGS=--encoding=UTF-8 --lc-collate=C --lc-ctype=C
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - spatial-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d spatial_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: spatial-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - spatial-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx 反向代理
  nginx:
    image: nginx:alpine
    container_name: spatial-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - spatial-service
    networks:
      - spatial-network
    restart: unless-stopped

  # 监控服务 (Prometheus)
  prometheus:
    image: prom/prometheus:latest
    container_name: spatial-prometheus
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - spatial-network
    restart: unless-stopped

  # 可视化监控 (Grafana)
  grafana:
    image: grafana/grafana:latest
    container_name: spatial-grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    depends_on:
      - prometheus
    networks:
      - spatial-network
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  spatial-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

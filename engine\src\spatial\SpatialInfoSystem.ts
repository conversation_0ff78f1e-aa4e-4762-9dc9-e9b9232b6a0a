/**
 * 空间信息系统主类
 * 整合所有空间功能模块，提供统一的API接口
 */
import { World } from '../core/World';
import { Entity } from '../core/Entity';
import { 
  GeographicCoordinate, 
  CoordinateSystemType, 
  CoordinateSystemManager 
} from './coordinate/CoordinateSystem';
import { GeospatialComponent } from './components/GeospatialComponent';
import { 
  TileMapSystem, 
  ITileProvider, 
  OSMTileProvider, 
  SatelliteTileProvider, 
  TerrainTileProvider 
} from './map/TileMapSystem';
import { 
  SpatialAnalysisEngine, 
  BufferAnalysisParams, 
  SpatialQueryParams,
  SpatialAnalysisResult 
} from './analysis/SpatialAnalysisEngine';
import { SpatialSystem } from './systems/SpatialSystem';

/**
 * 空间信息系统配置接口
 */
export interface SpatialInfoSystemConfig {
  defaultCoordinateSystem?: CoordinateSystemType;
  enableTileMap?: boolean;
  enableSpatialAnalysis?: boolean;
  tileProvider?: ITileProvider;
  maxCacheSize?: number;
  enableDebug?: boolean;
}

/**
 * 地图视图配置接口
 */
export interface MapViewConfig {
  center: GeographicCoordinate;
  zoom: number;
  tileProvider?: string | ITileProvider;
}

/**
 * 空间信息系统主类
 */
export class SpatialInfoSystem {
  private world: World;
  private config: SpatialInfoSystemConfig;
  private coordinateManager: CoordinateSystemManager;
  private tileMapSystem?: TileMapSystem;
  private analysisEngine: SpatialAnalysisEngine;
  private spatialSystem: SpatialSystem;
  
  // 瓦片提供者注册表
  private tileProviders: Map<string, ITileProvider> = new Map();
  
  // 地理空间组件注册表
  private geospatialComponents: Map<string, GeospatialComponent> = new Map();
  
  constructor(world: World, config: SpatialInfoSystemConfig = {}) {
    this.world = world;
    this.config = {
      defaultCoordinateSystem: CoordinateSystemType.WGS84,
      enableTileMap: true,
      enableSpatialAnalysis: true,
      maxCacheSize: 1000,
      enableDebug: false,
      ...config
    };
    
    this.initializeSystem();
  }
  
  /**
   * 初始化系统
   */
  private initializeSystem(): void {
    // 初始化坐标系统管理器
    this.coordinateManager = CoordinateSystemManager.getInstance();
    this.coordinateManager.setCurrentSystem(this.config.defaultCoordinateSystem!);
    
    // 初始化空间分析引擎
    this.analysisEngine = SpatialAnalysisEngine.getInstance();
    
    // 初始化空间系统
    this.spatialSystem = new SpatialSystem(this.world);
    this.world.addSystem(this.spatialSystem);
    
    // 注册默认瓦片提供者
    this.registerTileProviders();
    
    // 初始化瓦片地图系统
    if (this.config.enableTileMap) {
      this.initializeTileMapSystem();
    }
    
    if (this.config.enableDebug) {
      console.log('SpatialInfoSystem: 系统初始化完成', this.config);
    }
  }
  
  /**
   * 注册瓦片提供者
   */
  private registerTileProviders(): void {
    this.tileProviders.set('osm', new OSMTileProvider());
    this.tileProviders.set('satellite', new SatelliteTileProvider());
    this.tileProviders.set('terrain', new TerrainTileProvider());
  }
  
  /**
   * 初始化瓦片地图系统
   */
  private initializeTileMapSystem(): void {
    const tileProvider = this.config.tileProvider || this.tileProviders.get('osm')!;
    
    this.tileMapSystem = new TileMapSystem(this.world, {
      tileProvider,
      maxCacheSize: this.config.maxCacheSize,
      enableDebug: this.config.enableDebug
    });
    
    this.world.addSystem(this.tileMapSystem);
  }
  
  /**
   * 设置地图视图
   */
  setMapView(center: GeographicCoordinate, zoom: number): void {
    if (!this.tileMapSystem) {
      throw new Error('瓦片地图系统未启用');
    }
    
    this.tileMapSystem.setView(center, zoom);
    
    if (this.config.enableDebug) {
      console.log(`SpatialInfoSystem: 设置地图视图 - 中心: ${center.longitude}, ${center.latitude}, 缩放: ${zoom}`);
    }
  }
  
  /**
   * 获取地图视图
   */
  getMapView(): { center: GeographicCoordinate; zoom: number } | null {
    if (!this.tileMapSystem) {
      return null;
    }
    
    return {
      center: this.tileMapSystem.getCenter(),
      zoom: this.tileMapSystem.getZoom()
    };
  }
  
  /**
   * 设置瓦片提供者
   */
  setTileProvider(provider: string | ITileProvider): void {
    if (!this.tileMapSystem) {
      throw new Error('瓦片地图系统未启用');
    }
    
    let tileProvider: ITileProvider;
    
    if (typeof provider === 'string') {
      const registeredProvider = this.tileProviders.get(provider);
      if (!registeredProvider) {
        throw new Error(`未找到瓦片提供者: ${provider}`);
      }
      tileProvider = registeredProvider;
    } else {
      tileProvider = provider;
    }
    
    this.tileMapSystem.setTileProvider(tileProvider);
  }
  
  /**
   * 注册自定义瓦片提供者
   */
  registerTileProvider(name: string, provider: ITileProvider): void {
    this.tileProviders.set(name, provider);
  }
  
  /**
   * 获取已注册的瓦片提供者列表
   */
  getTileProviders(): string[] {
    return Array.from(this.tileProviders.keys());
  }
  
  /**
   * 创建地理空间组件
   */
  createGeospatialComponent(
    coordinate: GeographicCoordinate,
    geometryType: 'Point' | 'LineString' | 'Polygon' = 'Point',
    coordinateSystem?: CoordinateSystemType
  ): GeospatialComponent {
    const system = coordinateSystem || this.config.defaultCoordinateSystem!;
    const component = new GeospatialComponent(coordinate, system, geometryType);
    
    return component;
  }
  
  /**
   * 添加地理空间组件到实体
   */
  addGeospatialComponent(entity: Entity, component: GeospatialComponent): void {
    entity.addComponent(component);
    this.geospatialComponents.set(entity.id, component);
    
    // 通知空间系统
    this.spatialSystem.addGeospatialEntity(entity);
  }
  
  /**
   * 从实体移除地理空间组件
   */
  removeGeospatialComponent(entity: Entity): void {
    const component = entity.getComponent('Geospatial') as GeospatialComponent;
    if (component) {
      entity.removeComponent('Geospatial');
      this.geospatialComponents.delete(entity.id);
      
      // 通知空间系统
      this.spatialSystem.removeGeospatialEntity(entity);
    }
  }
  
  /**
   * 获取所有地理空间组件
   */
  getAllGeospatialComponents(): GeospatialComponent[] {
    return Array.from(this.geospatialComponents.values());
  }
  
  /**
   * 设置当前坐标系统
   */
  setCoordinateSystem(system: CoordinateSystemType): void {
    this.coordinateManager.setCurrentSystem(system);
    
    if (this.config.enableDebug) {
      console.log(`SpatialInfoSystem: 设置坐标系统为 ${system}`);
    }
  }
  
  /**
   * 获取当前坐标系统
   */
  getCurrentCoordinateSystem(): CoordinateSystemType {
    return this.coordinateManager.getCurrentSystem();
  }
  
  /**
   * 坐标转换
   */
  transformCoordinate(
    coordinate: GeographicCoordinate,
    sourceSystem: CoordinateSystemType,
    targetSystem: CoordinateSystemType
  ): GeographicCoordinate {
    return this.coordinateManager.transform(coordinate, sourceSystem, targetSystem);
  }
  
  /**
   * 缓冲区分析
   */
  buffer(geometry: any, params: BufferAnalysisParams): SpatialAnalysisResult {
    if (!this.config.enableSpatialAnalysis) {
      throw new Error('空间分析功能未启用');
    }
    
    return this.analysisEngine.buffer(geometry, params);
  }
  
  /**
   * 相交分析
   */
  intersection(geometry1: any, geometry2: any): SpatialAnalysisResult {
    if (!this.config.enableSpatialAnalysis) {
      throw new Error('空间分析功能未启用');
    }
    
    return this.analysisEngine.intersection(geometry1, geometry2);
  }
  
  /**
   * 联合分析
   */
  union(geometries: any[]): SpatialAnalysisResult {
    if (!this.config.enableSpatialAnalysis) {
      throw new Error('空间分析功能未启用');
    }
    
    return this.analysisEngine.union(geometries);
  }
  
  /**
   * 差异分析
   */
  difference(geometry1: any, geometry2: any): SpatialAnalysisResult {
    if (!this.config.enableSpatialAnalysis) {
      throw new Error('空间分析功能未启用');
    }
    
    return this.analysisEngine.difference(geometry1, geometry2);
  }
  
  /**
   * 空间查询
   */
  spatialQuery(params: SpatialQueryParams): GeospatialComponent[] {
    if (!this.config.enableSpatialAnalysis) {
      throw new Error('空间分析功能未启用');
    }
    
    const allComponents = this.getAllGeospatialComponents();
    return this.analysisEngine.spatialQuery(allComponents, params);
  }
  
  /**
   * 计算距离
   */
  calculateDistance(coord1: GeographicCoordinate, coord2: GeographicCoordinate): number {
    return this.analysisEngine.haversineDistance(coord1, coord2);
  }
  
  /**
   * 计算面积
   */
  calculateArea(coordinates: GeographicCoordinate[]): number {
    return this.analysisEngine.polygonArea(coordinates);
  }
  
  /**
   * 从GeoJSON创建地理空间组件
   */
  createFromGeoJSON(geoJSON: any, coordinateSystem?: CoordinateSystemType): GeospatialComponent {
    const system = coordinateSystem || this.config.defaultCoordinateSystem!;
    return GeospatialComponent.fromGeoJSON(geoJSON, system);
  }
  
  /**
   * 导出为GeoJSON
   */
  exportToGeoJSON(): any {
    const features = this.getAllGeospatialComponents().map(component => 
      component.toGeoJSON()
    );
    
    return {
      type: 'FeatureCollection',
      features
    };
  }
  
  /**
   * 获取系统统计信息
   */
  getSystemStats(): {
    coordinateSystem: CoordinateSystemType;
    componentCount: number;
    tileMapEnabled: boolean;
    analysisEnabled: boolean;
    tileCache?: any;
  } {
    const stats = {
      coordinateSystem: this.getCurrentCoordinateSystem(),
      componentCount: this.geospatialComponents.size,
      tileMapEnabled: !!this.tileMapSystem,
      analysisEnabled: this.config.enableSpatialAnalysis!
    };
    
    if (this.tileMapSystem) {
      (stats as any).tileCache = this.tileMapSystem.getCacheStats();
    }
    
    return stats;
  }
  
  /**
   * 销毁系统
   */
  destroy(): void {
    // 清理地理空间组件
    this.geospatialComponents.clear();
    
    // 移除系统
    if (this.spatialSystem) {
      this.world.removeSystem(this.spatialSystem);
    }
    
    if (this.tileMapSystem) {
      this.world.removeSystem(this.tileMapSystem);
      this.tileMapSystem.destroy();
    }
    
    // 清理瓦片提供者
    this.tileProviders.clear();
    
    if (this.config.enableDebug) {
      console.log('SpatialInfoSystem: 系统已销毁');
    }
  }
}

/**
 * 数据管理器
 * 统一管理各种数据存储和查询操作
 */
import { EventEmitter } from '../utils/EventEmitter';
import { DataStore } from './DataStore';
import { DataStoreConfig, DataItem, QueryOptions, QueryResult, DataEvent, DataEventType } from './types';

export class DataManager extends EventEmitter {
  private stores: Map<string, DataStore> = new Map();
  private static instance: DataManager;

  /**
   * 获取单例实例
   */
  public static getInstance(): DataManager {
    if (!DataManager.instance) {
      DataManager.instance = new DataManager();
    }
    return DataManager.instance;
  }

  private constructor() {
    super();
  }

  /**
   * 创建数据存储
   * @param config 存储配置
   * @returns 数据存储实例
   */
  public createStore(config: DataStoreConfig): DataStore {
    if (this.stores.has(config.name)) {
      throw new Error(`Data store '${config.name}' already exists`);
    }

    const store = new DataStore(config);
    this.stores.set(config.name, store);

    // 监听存储事件
    store.on('data-changed', (event: DataEvent) => {
      this.emit('data-changed', event);
    });

    return store;
  }

  /**
   * 获取数据存储
   * @param name 存储名称
   * @returns 数据存储实例
   */
  public getStore(name: string): DataStore | undefined {
    return this.stores.get(name);
  }

  /**
   * 删除数据存储
   * @param name 存储名称
   */
  public async deleteStore(name: string): Promise<void> {
    const store = this.stores.get(name);
    if (store) {
      await store.clear();
      this.stores.delete(name);
    }
  }

  /**
   * 获取所有存储名称
   * @returns 存储名称数组
   */
  public getStoreNames(): string[] {
    return Array.from(this.stores.keys());
  }

  /**
   * 存储数据项
   * @param storeName 存储名称
   * @param item 数据项
   */
  public async set(storeName: string, item: DataItem): Promise<void> {
    const store = this.getStore(storeName);
    if (!store) {
      throw new Error(`Data store '${storeName}' not found`);
    }
    await store.set(item);
  }

  /**
   * 获取数据项
   * @param storeName 存储名称
   * @param key 数据键
   * @returns 数据项
   */
  public async get(storeName: string, key: string): Promise<DataItem | undefined> {
    const store = this.getStore(storeName);
    if (!store) {
      throw new Error(`Data store '${storeName}' not found`);
    }
    return await store.get(key);
  }

  /**
   * 删除数据项
   * @param storeName 存储名称
   * @param key 数据键
   */
  public async delete(storeName: string, key: string): Promise<void> {
    const store = this.getStore(storeName);
    if (!store) {
      throw new Error(`Data store '${storeName}' not found`);
    }
    await store.delete(key);
  }

  /**
   * 查询数据
   * @param storeName 存储名称
   * @param options 查询选项
   * @returns 查询结果
   */
  public async query(storeName: string, options?: QueryOptions): Promise<QueryResult> {
    const store = this.getStore(storeName);
    if (!store) {
      throw new Error(`Data store '${storeName}' not found`);
    }
    return await store.query(options);
  }

  /**
   * 清空所有存储
   */
  public async clearAll(): Promise<void> {
    const promises = Array.from(this.stores.values()).map(store => store.clear());
    await Promise.all(promises);
  }

  /**
   * 获取存储统计信息
   * @param storeName 存储名称
   * @returns 统计信息
   */
  public async getStats(storeName: string): Promise<any> {
    const store = this.getStore(storeName);
    if (!store) {
      throw new Error(`Data store '${storeName}' not found`);
    }
    return await store.getStats();
  }

  /**
   * 销毁数据管理器
   */
  public async destroy(): Promise<void> {
    const promises = Array.from(this.stores.values()).map(store => store.destroy());
    await Promise.all(promises);
    this.stores.clear();
    this.removeAllListeners();
  }
}

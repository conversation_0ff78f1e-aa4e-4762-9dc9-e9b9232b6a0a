/**
 * 瓦片地图系统
 * 负责地图瓦片的加载、缓存和渲染
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { World } from '../../core/World';
import { GeographicCoordinate, CoordinateTransformer } from '../coordinate/CoordinateSystem';

/**
 * 瓦片坐标接口
 */
export interface TileCoordinate {
  x: number;
  y: number;
  z: number; // 缩放级别
}

/**
 * 地图瓦片接口
 */
export interface MapTile {
  coordinate: TileCoordinate;
  url: string;
  texture?: THREE.Texture;
  mesh?: THREE.Mesh;
  loaded: boolean;
  loading: boolean;
  error?: string;
  timestamp: number;
}

/**
 * 瓦片提供者接口
 */
export interface ITileProvider {
  name: string;
  getTileUrl(coordinate: TileCoordinate): string;
  getMaxZoom(): number;
  getMinZoom(): number;
  getAttribution(): string;
}

/**
 * OpenStreetMap瓦片提供者
 */
export class OSMTileProvider implements ITileProvider {
  name = 'OpenStreetMap';
  
  getTileUrl(coordinate: TileCoordinate): string {
    const servers = ['a', 'b', 'c'];
    const server = servers[Math.abs(coordinate.x + coordinate.y) % servers.length];
    return `https://${server}.tile.openstreetmap.org/${coordinate.z}/${coordinate.x}/${coordinate.y}.png`;
  }
  
  getMaxZoom(): number {
    return 19;
  }
  
  getMinZoom(): number {
    return 0;
  }
  
  getAttribution(): string {
    return '© OpenStreetMap contributors';
  }
}

/**
 * 卫星影像瓦片提供者
 */
export class SatelliteTileProvider implements ITileProvider {
  name = 'Satellite';
  
  getTileUrl(coordinate: TileCoordinate): string {
    return `https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/${coordinate.z}/${coordinate.y}/${coordinate.x}`;
  }
  
  getMaxZoom(): number {
    return 19;
  }
  
  getMinZoom(): number {
    return 0;
  }
  
  getAttribution(): string {
    return 'Tiles © Esri';
  }
}

/**
 * 地形瓦片提供者
 */
export class TerrainTileProvider implements ITileProvider {
  name = 'Terrain';
  
  getTileUrl(coordinate: TileCoordinate): string {
    return `https://server.arcgisonline.com/ArcGIS/rest/services/World_Terrain_Base/MapServer/tile/${coordinate.z}/${coordinate.y}/${coordinate.x}`;
  }
  
  getMaxZoom(): number {
    return 13;
  }
  
  getMinZoom(): number {
    return 0;
  }
  
  getAttribution(): string {
    return 'Tiles © Esri';
  }
}

/**
 * 瓦片地图系统配置
 */
export interface TileMapSystemConfig {
  tileProvider?: ITileProvider;
  maxCacheSize?: number;
  tileSize?: number;
  maxConcurrentLoads?: number;
  retryAttempts?: number;
  retryDelay?: number;
  enableDebug?: boolean;
}

/**
 * 瓦片地图系统
 */
export class TileMapSystem extends System {
  private tileCache: Map<string, MapTile> = new Map();
  private tileProvider: ITileProvider;
  private maxCacheSize: number;
  private tileSize: number;
  private maxConcurrentLoads: number;
  private retryAttempts: number;
  private retryDelay: number;
  private enableDebug: boolean;
  
  private loadingQueue: TileCoordinate[] = [];
  private currentLoads: number = 0;
  private textureLoader: THREE.TextureLoader;
  
  // 地图根节点
  private mapRoot: THREE.Group;
  
  // 当前视图参数
  private currentCenter: GeographicCoordinate = { longitude: 0, latitude: 0 };
  private currentZoom: number = 1;
  private viewBounds: {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  } = { minX: 0, maxX: 0, minY: 0, maxY: 0 };
  
  constructor(world: World, config: TileMapSystemConfig = {}) {
    super(world);
    
    this.tileProvider = config.tileProvider || new OSMTileProvider();
    this.maxCacheSize = config.maxCacheSize || 1000;
    this.tileSize = config.tileSize || 256;
    this.maxConcurrentLoads = config.maxConcurrentLoads || 6;
    this.retryAttempts = config.retryAttempts || 3;
    this.retryDelay = config.retryDelay || 1000;
    this.enableDebug = config.enableDebug || false;
    
    this.textureLoader = new THREE.TextureLoader();
    this.mapRoot = new THREE.Group();
    this.mapRoot.name = 'TileMapRoot';
    
    // 添加到场景
    if (world.scene) {
      world.scene.add(this.mapRoot);
    }
  }
  
  /**
   * 系统更新
   */
  update(deltaTime: number): void {
    this.processLoadingQueue();
    this.cleanupCache();
    this.updateVisibleTiles();
  }
  
  /**
   * 设置地图中心和缩放级别
   */
  setView(center: GeographicCoordinate, zoom: number): void {
    this.currentCenter = { ...center };
    this.currentZoom = Math.max(this.tileProvider.getMinZoom(), Math.min(this.tileProvider.getMaxZoom(), zoom));
    
    this.updateViewBounds();
    this.loadVisibleTiles();
    
    if (this.enableDebug) {
      console.log(`TileMapSystem: 设置视图 - 中心: ${center.longitude}, ${center.latitude}, 缩放: ${this.currentZoom}`);
    }
  }
  
  /**
   * 获取当前视图中心
   */
  getCenter(): GeographicCoordinate {
    return { ...this.currentCenter };
  }
  
  /**
   * 获取当前缩放级别
   */
  getZoom(): number {
    return this.currentZoom;
  }
  
  /**
   * 设置瓦片提供者
   */
  setTileProvider(provider: ITileProvider): void {
    this.tileProvider = provider;
    this.clearCache();
    this.loadVisibleTiles();
  }
  
  /**
   * 获取瓦片提供者
   */
  getTileProvider(): ITileProvider {
    return this.tileProvider;
  }
  
  /**
   * 更新视图边界
   */
  private updateViewBounds(): void {
    // 计算当前视图的瓦片边界
    const zoom = Math.floor(this.currentZoom);
    const tileCoord = this.geographicToTile(this.currentCenter, zoom);
    
    // 假设视图大小为5x5瓦片
    const viewSize = 5;
    this.viewBounds = {
      minX: tileCoord.x - Math.floor(viewSize / 2),
      maxX: tileCoord.x + Math.floor(viewSize / 2),
      minY: tileCoord.y - Math.floor(viewSize / 2),
      maxY: tileCoord.y + Math.floor(viewSize / 2)
    };
  }
  
  /**
   * 加载可见瓦片
   */
  private loadVisibleTiles(): void {
    const zoom = Math.floor(this.currentZoom);
    
    for (let x = this.viewBounds.minX; x <= this.viewBounds.maxX; x++) {
      for (let y = this.viewBounds.minY; y <= this.viewBounds.maxY; y++) {
        const tileCoord: TileCoordinate = { x, y, z: zoom };
        
        // 检查瓦片坐标是否有效
        if (this.isValidTileCoordinate(tileCoord)) {
          this.loadTile(tileCoord);
        }
      }
    }
  }
  
  /**
   * 更新可见瓦片
   */
  private updateVisibleTiles(): void {
    this.mapRoot.children.forEach(child => {
      if (child.userData.tileCoordinate) {
        const coord = child.userData.tileCoordinate as TileCoordinate;
        const visible = this.isTileVisible(coord);
        child.visible = visible;
      }
    });
  }
  
  /**
   * 判断瓦片是否可见
   */
  private isTileVisible(coord: TileCoordinate): boolean {
    return coord.z === Math.floor(this.currentZoom) &&
           coord.x >= this.viewBounds.minX &&
           coord.x <= this.viewBounds.maxX &&
           coord.y >= this.viewBounds.minY &&
           coord.y <= this.viewBounds.maxY;
  }
  
  /**
   * 加载瓦片
   */
  private async loadTile(coordinate: TileCoordinate): Promise<void> {
    const key = this.getTileKey(coordinate);
    
    // 检查缓存
    if (this.tileCache.has(key)) {
      const tile = this.tileCache.get(key)!;
      if (tile.loaded && tile.mesh) {
        this.addTileToScene(tile);
      }
      return;
    }
    
    // 检查是否已在加载队列中
    if (this.loadingQueue.some(coord => this.getTileKey(coord) === key)) {
      return;
    }
    
    // 添加到加载队列
    this.loadingQueue.push(coordinate);
  }
  
  /**
   * 处理加载队列
   */
  private processLoadingQueue(): void {
    while (this.loadingQueue.length > 0 && this.currentLoads < this.maxConcurrentLoads) {
      const coordinate = this.loadingQueue.shift()!;
      this.loadTileAsync(coordinate);
    }
  }
  
  /**
   * 异步加载瓦片
   */
  private async loadTileAsync(coordinate: TileCoordinate): Promise<void> {
    const key = this.getTileKey(coordinate);
    this.currentLoads++;
    
    try {
      const tile: MapTile = {
        coordinate,
        url: this.tileProvider.getTileUrl(coordinate),
        loaded: false,
        loading: true,
        timestamp: Date.now()
      };
      
      this.tileCache.set(key, tile);
      
      // 加载纹理
      const texture = await this.loadTexture(tile.url);
      
      // 创建瓦片网格
      const mesh = this.createTileMesh(coordinate, texture);
      
      tile.texture = texture;
      tile.mesh = mesh;
      tile.loaded = true;
      tile.loading = false;
      
      // 添加到场景
      this.addTileToScene(tile);
      
      if (this.enableDebug) {
        console.log(`TileMapSystem: 瓦片加载成功 - ${key}`);
      }
      
    } catch (error) {
      const tile = this.tileCache.get(key);
      if (tile) {
        tile.loading = false;
        tile.error = error instanceof Error ? error.message : '加载失败';
      }
      
      if (this.enableDebug) {
        console.error(`TileMapSystem: 瓦片加载失败 - ${key}`, error);
      }
    } finally {
      this.currentLoads--;
    }
  }
  
  /**
   * 加载纹理
   */
  private loadTexture(url: string): Promise<THREE.Texture> {
    return new Promise((resolve, reject) => {
      this.textureLoader.load(
        url,
        (texture) => {
          texture.wrapS = THREE.ClampToEdgeWrapping;
          texture.wrapT = THREE.ClampToEdgeWrapping;
          texture.minFilter = THREE.LinearFilter;
          texture.magFilter = THREE.LinearFilter;
          resolve(texture);
        },
        undefined,
        (error) => reject(error)
      );
    });
  }
  
  /**
   * 创建瓦片网格
   */
  private createTileMesh(coordinate: TileCoordinate, texture: THREE.Texture): THREE.Mesh {
    const geometry = new THREE.PlaneGeometry(this.tileSize, this.tileSize);
    const material = new THREE.MeshBasicMaterial({
      map: texture,
      transparent: true
    });
    
    const mesh = new THREE.Mesh(geometry, material);
    
    // 计算瓦片的世界坐标
    const worldPos = this.tileToWorldPosition(coordinate);
    mesh.position.set(worldPos.x, 0, worldPos.y);
    mesh.rotation.x = -Math.PI / 2; // 水平放置
    
    // 存储瓦片坐标信息
    mesh.userData.tileCoordinate = coordinate;
    mesh.name = `Tile_${this.getTileKey(coordinate)}`;
    
    return mesh;
  }
  
  /**
   * 将瓦片添加到场景
   */
  private addTileToScene(tile: MapTile): void {
    if (tile.mesh && !this.mapRoot.children.includes(tile.mesh)) {
      this.mapRoot.add(tile.mesh);
    }
  }
  
  /**
   * 地理坐标转瓦片坐标
   */
  private geographicToTile(coord: GeographicCoordinate, zoom: number): TileCoordinate {
    const n = Math.pow(2, zoom);
    const x = Math.floor((coord.longitude + 180) / 360 * n);
    const latRad = coord.latitude * Math.PI / 180;
    const y = Math.floor((1 - Math.asinh(Math.tan(latRad)) / Math.PI) / 2 * n);
    
    return { x, y, z: zoom };
  }
  
  /**
   * 瓦片坐标转世界坐标
   */
  private tileToWorldPosition(coordinate: TileCoordinate): { x: number; y: number } {
    const scale = Math.pow(2, coordinate.z);
    const x = (coordinate.x - scale / 2) * this.tileSize;
    const y = (coordinate.y - scale / 2) * this.tileSize;
    
    return { x, y };
  }
  
  /**
   * 检查瓦片坐标是否有效
   */
  private isValidTileCoordinate(coordinate: TileCoordinate): boolean {
    const maxTile = Math.pow(2, coordinate.z);
    return coordinate.x >= 0 && coordinate.x < maxTile &&
           coordinate.y >= 0 && coordinate.y < maxTile &&
           coordinate.z >= this.tileProvider.getMinZoom() &&
           coordinate.z <= this.tileProvider.getMaxZoom();
  }
  
  /**
   * 获取瓦片键值
   */
  private getTileKey(coordinate: TileCoordinate): string {
    return `${coordinate.z}_${coordinate.x}_${coordinate.y}`;
  }
  
  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    if (this.tileCache.size <= this.maxCacheSize) {
      return;
    }
    
    // 按时间戳排序，删除最旧的瓦片
    const tiles = Array.from(this.tileCache.entries())
      .sort((a, b) => a[1].timestamp - b[1].timestamp);
    
    const deleteCount = this.tileCache.size - this.maxCacheSize;
    for (let i = 0; i < deleteCount; i++) {
      const [key, tile] = tiles[i];
      
      // 从场景中移除
      if (tile.mesh && this.mapRoot.children.includes(tile.mesh)) {
        this.mapRoot.remove(tile.mesh);
      }
      
      // 释放纹理资源
      if (tile.texture) {
        tile.texture.dispose();
      }
      
      // 释放几何体和材质
      if (tile.mesh) {
        tile.mesh.geometry.dispose();
        if (tile.mesh.material instanceof THREE.Material) {
          tile.mesh.material.dispose();
        }
      }
      
      this.tileCache.delete(key);
    }
    
    if (this.enableDebug) {
      console.log(`TileMapSystem: 清理缓存，删除 ${deleteCount} 个瓦片`);
    }
  }
  
  /**
   * 清空缓存
   */
  private clearCache(): void {
    this.tileCache.forEach((tile, key) => {
      if (tile.mesh && this.mapRoot.children.includes(tile.mesh)) {
        this.mapRoot.remove(tile.mesh);
      }
      
      if (tile.texture) {
        tile.texture.dispose();
      }
      
      if (tile.mesh) {
        tile.mesh.geometry.dispose();
        if (tile.mesh.material instanceof THREE.Material) {
          tile.mesh.material.dispose();
        }
      }
    });
    
    this.tileCache.clear();
    this.loadingQueue = [];
  }
  
  /**
   * 获取地图根节点
   */
  getMapRoot(): THREE.Group {
    return this.mapRoot;
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats(): {
    size: number;
    maxSize: number;
    loadingCount: number;
    queueLength: number;
  } {
    return {
      size: this.tileCache.size,
      maxSize: this.maxCacheSize,
      loadingCount: this.currentLoads,
      queueLength: this.loadingQueue.length
    };
  }

  /**
   * 销毁系统
   */
  destroy(): void {
    this.clearCache();

    if (this.world.scene && this.mapRoot.parent) {
      this.world.scene.remove(this.mapRoot);
    }

    super.destroy();
  }
}

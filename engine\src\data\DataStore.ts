/**
 * 数据存储
 * 提供基础的数据存储和查询功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { DataStoreConfig, DataItem, QueryOptions, QueryResult, DataEvent, DataEventType, DataType } from './types';

export class DataStore extends EventEmitter {
  private config: DataStoreConfig;
  private data: Map<string, DataItem> = new Map();
  private isDestroyed: boolean = false;

  constructor(config: DataStoreConfig) {
    super();
    this.config = config;
  }

  /**
   * 存储数据项
   * @param item 数据项
   */
  public async set(item: DataItem): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('DataStore has been destroyed');
    }

    // 检查存储大小限制
    if (this.config.maxSize && this.data.size >= this.config.maxSize) {
      // 清理过期数据
      await this.cleanup();
      
      // 如果仍然超过限制，抛出错误
      if (this.data.size >= this.config.maxSize) {
        throw new Error('DataStore size limit exceeded');
      }
    }

    const existingItem = this.data.get(item.key);
    this.data.set(item.key, { ...item, timestamp: new Date() });

    // 发出事件
    const eventType = existingItem ? DataEventType.UPDATED : DataEventType.CREATED;
    this.emitDataEvent(eventType, item.id, item);
  }

  /**
   * 获取数据项
   * @param key 数据键
   * @returns 数据项
   */
  public async get(key: string): Promise<DataItem | undefined> {
    if (this.isDestroyed) {
      throw new Error('DataStore has been destroyed');
    }

    const item = this.data.get(key);
    
    // 检查TTL
    if (item && this.config.ttl) {
      const now = Date.now();
      const itemTime = item.timestamp.getTime();
      if (now - itemTime > this.config.ttl) {
        await this.delete(key);
        return undefined;
      }
    }

    return item;
  }

  /**
   * 删除数据项
   * @param key 数据键
   */
  public async delete(key: string): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('DataStore has been destroyed');
    }

    const item = this.data.get(key);
    if (item) {
      this.data.delete(key);
      this.emitDataEvent(DataEventType.DELETED, item.id);
    }
  }

  /**
   * 查询数据
   * @param options 查询选项
   * @returns 查询结果
   */
  public async query(options?: QueryOptions): Promise<QueryResult> {
    if (this.isDestroyed) {
      throw new Error('DataStore has been destroyed');
    }

    let items = Array.from(this.data.values());

    // 应用条件过滤
    if (options?.conditions) {
      items = items.filter(item => this.matchesConditions(item, options.conditions!));
    }

    // 应用排序
    if (options?.sort) {
      items.sort((a, b) => this.compareItems(a, b, options.sort!));
    }

    // 计算总数
    const total = items.length;

    // 应用分页
    const offset = options?.offset || 0;
    const limit = options?.limit;
    
    if (limit !== undefined) {
      items = items.slice(offset, offset + limit);
    } else if (offset > 0) {
      items = items.slice(offset);
    }

    // 应用字段选择
    if (options?.fields) {
      items = items.map(item => this.selectFields(item, options.fields!));
    }

    const hasMore = limit !== undefined && offset + limit < total;
    const nextOffset = hasMore ? offset + limit : undefined;

    this.emitDataEvent(DataEventType.QUERIED, undefined, { query: options, resultCount: items.length });

    return {
      data: items,
      total,
      hasMore,
      nextOffset
    };
  }

  /**
   * 清空存储
   */
  public async clear(): Promise<void> {
    if (this.isDestroyed) {
      throw new Error('DataStore has been destroyed');
    }

    this.data.clear();
  }

  /**
   * 获取存储统计信息
   * @returns 统计信息
   */
  public async getStats(): Promise<any> {
    if (this.isDestroyed) {
      throw new Error('DataStore has been destroyed');
    }

    const items = Array.from(this.data.values());
    const typeStats: Record<string, number> = {};
    
    items.forEach(item => {
      typeStats[item.type] = (typeStats[item.type] || 0) + 1;
    });

    return {
      name: this.config.name,
      type: this.config.type,
      itemCount: items.length,
      maxSize: this.config.maxSize,
      ttl: this.config.ttl,
      typeDistribution: typeStats,
      oldestItem: items.length > 0 ? Math.min(...items.map(i => i.timestamp.getTime())) : null,
      newestItem: items.length > 0 ? Math.max(...items.map(i => i.timestamp.getTime())) : null
    };
  }

  /**
   * 销毁存储
   */
  public async destroy(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    await this.clear();
    this.removeAllListeners();
    this.isDestroyed = true;
  }

  /**
   * 清理过期数据
   */
  private async cleanup(): Promise<void> {
    if (!this.config.ttl) {
      return;
    }

    const now = Date.now();
    const expiredKeys: string[] = [];

    this.data.forEach((item, key) => {
      if (now - item.timestamp.getTime() > this.config.ttl!) {
        expiredKeys.push(key);
      }
    });

    for (const key of expiredKeys) {
      await this.delete(key);
    }
  }

  /**
   * 检查项目是否匹配条件
   */
  private matchesConditions(item: DataItem, conditions: any[]): boolean {
    return conditions.every(condition => {
      const fieldValue = this.getFieldValue(item, condition.field);
      return this.evaluateCondition(fieldValue, condition.operator, condition.value);
    });
  }

  /**
   * 获取字段值
   */
  private getFieldValue(item: DataItem, field: string): any {
    const parts = field.split('.');
    let value: any = item;
    
    for (const part of parts) {
      if (value && typeof value === 'object') {
        value = value[part];
      } else {
        return undefined;
      }
    }
    
    return value;
  }

  /**
   * 评估条件
   */
  private evaluateCondition(fieldValue: any, operator: string, conditionValue: any): boolean {
    switch (operator) {
      case 'eq': return fieldValue === conditionValue;
      case 'ne': return fieldValue !== conditionValue;
      case 'gt': return fieldValue > conditionValue;
      case 'gte': return fieldValue >= conditionValue;
      case 'lt': return fieldValue < conditionValue;
      case 'lte': return fieldValue <= conditionValue;
      case 'in': return Array.isArray(conditionValue) && conditionValue.includes(fieldValue);
      case 'nin': return Array.isArray(conditionValue) && !conditionValue.includes(fieldValue);
      case 'contains': return typeof fieldValue === 'string' && fieldValue.includes(conditionValue);
      case 'startsWith': return typeof fieldValue === 'string' && fieldValue.startsWith(conditionValue);
      case 'endsWith': return typeof fieldValue === 'string' && fieldValue.endsWith(conditionValue);
      default: return false;
    }
  }

  /**
   * 比较项目用于排序
   */
  private compareItems(a: DataItem, b: DataItem, sortOptions: any[]): number {
    for (const sort of sortOptions) {
      const aValue = this.getFieldValue(a, sort.field);
      const bValue = this.getFieldValue(b, sort.field);
      
      let comparison = 0;
      if (aValue < bValue) comparison = -1;
      else if (aValue > bValue) comparison = 1;
      
      if (comparison !== 0) {
        return sort.order === 'desc' ? -comparison : comparison;
      }
    }
    return 0;
  }

  /**
   * 选择字段
   */
  private selectFields(item: DataItem, fields: string[]): any {
    const result: any = {};
    fields.forEach(field => {
      const value = this.getFieldValue(item, field);
      this.setFieldValue(result, field, value);
    });
    return result;
  }

  /**
   * 设置字段值
   */
  private setFieldValue(obj: any, field: string, value: any): void {
    const parts = field.split('.');
    let current = obj;
    
    for (let i = 0; i < parts.length - 1; i++) {
      const part = parts[i];
      if (!(part in current)) {
        current[part] = {};
      }
      current = current[part];
    }
    
    current[parts[parts.length - 1]] = value;
  }

  /**
   * 发出数据事件
   */
  private emitDataEvent(type: DataEventType, itemId?: string, data?: any): void {
    const event: DataEvent = {
      type,
      storeId: this.config.name,
      itemId,
      data,
      timestamp: new Date()
    };
    
    this.emit('data-changed', event);
  }
}

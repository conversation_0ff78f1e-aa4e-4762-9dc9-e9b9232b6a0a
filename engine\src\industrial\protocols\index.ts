/**
 * 工业通信协议模块导出
 */

// 协议管理器
export { IndustrialProtocolManager } from './IndustrialProtocolManager';

// 具体协议实现
export { ModbusProtocol } from './ModbusProtocol';
export { OPCUAProtocol } from './OPCUAProtocol';
export { MQTTProtocol } from './MQTTProtocol';
export { EtherCATProtocol } from './EtherCATProtocol';

// 协议工厂
export { ProtocolFactory, getProtocolFactory, createProtocol } from './ProtocolFactory';
export type { ProtocolInfo } from './ProtocolFactory';

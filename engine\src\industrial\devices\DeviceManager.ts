import { EventEmitter } from 'events';
import { Entity } from '../../core/Entity';
import { Scene } from '../../scene/Scene';
import { IndustrialDeviceComponent } from './IndustrialDeviceComponent';
import { CNCMachineComponent } from './CNCMachineComponent';
import { RobotArmComponent } from './RobotArmComponent';
import { ConveyorComponent } from './ConveyorComponent';
import { SensorComponent } from './SensorComponent';
import { 
  DeviceType, 
  DeviceConfig, 
  DeviceStatus, 
  FactoryConfig,
  IndustrialDataPoint 
} from '../types';
import { Debug } from '../../utils/Debug';

/**
 * 设备统计信息接口
 */
interface DeviceStatistics {
  totalDevices: number;
  devicesByType: Record<DeviceType, number>;
  devicesByStatus: Record<DeviceStatus, number>;
  averageOEE: number;
  totalDataPoints: number;
  activeAlarms: number;
}

/**
 * 设备管理器
 * 负责管理工厂中的所有工业设备
 */
export class DeviceManager extends EventEmitter {
  private scene: Scene;
  private devices: Map<string, Entity> = new Map();
  private deviceConfigs: Map<string, DeviceConfig> = new Map();
  private deviceGroups: Map<string, string[]> = new Map();
  private statistics: DeviceStatistics;
  
  // 配置选项
  private options = {
    autoStartDevices: true,
    enablePerformanceMonitoring: true,
    enableAlarmProcessing: true,
    statisticsUpdateInterval: 5000
  };

  constructor(scene: Scene, options?: Partial<typeof DeviceManager.prototype.options>) {
    super();
    this.scene = scene;
    
    if (options) {
      this.options = { ...this.options, ...options };
    }
    
    // 初始化统计信息
    this.initializeStatistics();
    
    // 启动统计更新
    this.startStatisticsUpdate();
    
    Debug.log('DeviceManager', '设备管理器已初始化');
  }

  /**
   * 初始化统计信息
   */
  private initializeStatistics(): void {
    this.statistics = {
      totalDevices: 0,
      devicesByType: {} as Record<DeviceType, number>,
      devicesByStatus: {} as Record<DeviceStatus, number>,
      averageOEE: 0,
      totalDataPoints: 0,
      activeAlarms: 0
    };
    
    // 初始化设备类型计数
    Object.values(DeviceType).forEach(type => {
      this.statistics.devicesByType[type] = 0;
    });
    
    // 初始化设备状态计数
    Object.values(DeviceStatus).forEach(status => {
      this.statistics.devicesByStatus[status] = 0;
    });
  }

  /**
   * 创建设备
   * @param config 设备配置
   * @returns 设备实体
   */
  public createDevice(config: DeviceConfig): Entity | null {
    try {
      // 检查设备是否已存在
      if (this.devices.has(config.id)) {
        Debug.warn('DeviceManager', `设备已存在: ${config.id}`);
        return this.devices.get(config.id)!;
      }

      // 创建设备实体
      const entity = new Entity(config.id);
      
      // 根据设备类型添加相应的组件
      let deviceComponent: IndustrialDeviceComponent | null = null;
      
      switch (config.type) {
        case DeviceType.CNC_MACHINE:
          deviceComponent = new CNCMachineComponent(entity, config);
          break;
          
        case DeviceType.ROBOT_ARM:
          deviceComponent = new RobotArmComponent(entity, config);
          break;
          
        case DeviceType.CONVEYOR:
          deviceComponent = new ConveyorComponent(entity, config);
          break;
          
        case DeviceType.TEMPERATURE_SENSOR:
        case DeviceType.PRESSURE_SENSOR:
        case DeviceType.VIBRATION_SENSOR:
        case DeviceType.PROXIMITY_SENSOR:
        case DeviceType.VISION_SENSOR:
          deviceComponent = new SensorComponent(entity, config);
          break;
          
        default:
          Debug.warn('DeviceManager', `不支持的设备类型: ${config.type}`);
          return null;
      }
      
      if (!deviceComponent) {
        Debug.error('DeviceManager', `创建设备组件失败: ${config.id}`);
        return null;
      }
      
      // 添加组件到实体
      entity.addComponent(deviceComponent);
      
      // 设置设备事件监听
      this.setupDeviceEventListeners(deviceComponent);
      
      // 添加到场景
      this.scene.addEntity(entity);
      
      // 存储设备信息
      this.devices.set(config.id, entity);
      this.deviceConfigs.set(config.id, config);
      
      // 自动启动设备
      if (this.options.autoStartDevices) {
        deviceComponent.updateStatus(DeviceStatus.ONLINE);
      }
      
      Debug.log('DeviceManager', `设备创建成功: ${config.name} (${config.id})`);
      
      // 发送设备创建事件
      this.emit('deviceCreated', { deviceId: config.id, config });
      
      return entity;
      
    } catch (error) {
      Debug.error('DeviceManager', `创建设备失败: ${config.id}`, error);
      return null;
    }
  }

  /**
   * 删除设备
   * @param deviceId 设备ID
   */
  public removeDevice(deviceId: string): boolean {
    try {
      const entity = this.devices.get(deviceId);
      if (!entity) {
        Debug.warn('DeviceManager', `设备不存在: ${deviceId}`);
        return false;
      }

      // 从场景中移除
      this.scene.removeEntity(entity);
      
      // 清理设备信息
      this.devices.delete(deviceId);
      this.deviceConfigs.delete(deviceId);
      
      // 从设备组中移除
      this.deviceGroups.forEach((devices, groupId) => {
        const index = devices.indexOf(deviceId);
        if (index !== -1) {
          devices.splice(index, 1);
        }
      });
      
      Debug.log('DeviceManager', `设备删除成功: ${deviceId}`);
      
      // 发送设备删除事件
      this.emit('deviceRemoved', { deviceId });
      
      return true;
      
    } catch (error) {
      Debug.error('DeviceManager', `删除设备失败: ${deviceId}`, error);
      return false;
    }
  }

  /**
   * 获取设备
   * @param deviceId 设备ID
   * @returns 设备实体
   */
  public getDevice(deviceId: string): Entity | null {
    return this.devices.get(deviceId) || null;
  }

  /**
   * 获取设备组件
   * @param deviceId 设备ID
   * @returns 设备组件
   */
  public getDeviceComponent(deviceId: string): IndustrialDeviceComponent | null {
    const entity = this.getDevice(deviceId);
    if (!entity) return null;
    
    return entity.getComponent('IndustrialDevice') ||
           entity.getComponent('CNCMachine') ||
           entity.getComponent('RobotArm') ||
           entity.getComponent('Conveyor') ||
           entity.getComponent('Sensor') ||
           null;
  }

  /**
   * 获取所有设备
   * @returns 设备实体数组
   */
  public getAllDevices(): Entity[] {
    return Array.from(this.devices.values());
  }

  /**
   * 根据类型获取设备
   * @param deviceType 设备类型
   * @returns 设备实体数组
   */
  public getDevicesByType(deviceType: DeviceType): Entity[] {
    return this.getAllDevices().filter(entity => {
      const component = this.getDeviceComponent(entity.id);
      return component && component.deviceType === deviceType;
    });
  }

  /**
   * 根据状态获取设备
   * @param status 设备状态
   * @returns 设备实体数组
   */
  public getDevicesByStatus(status: DeviceStatus): Entity[] {
    return this.getAllDevices().filter(entity => {
      const component = this.getDeviceComponent(entity.id);
      return component && component.status === status;
    });
  }

  /**
   * 创建设备组
   * @param groupId 组ID
   * @param deviceIds 设备ID列表
   */
  public createDeviceGroup(groupId: string, deviceIds: string[]): void {
    // 验证设备是否存在
    const validDeviceIds = deviceIds.filter(id => this.devices.has(id));
    
    if (validDeviceIds.length !== deviceIds.length) {
      Debug.warn('DeviceManager', `部分设备不存在，组 ${groupId} 只包含有效设备`);
    }
    
    this.deviceGroups.set(groupId, validDeviceIds);
    
    Debug.log('DeviceManager', `设备组创建: ${groupId} (${validDeviceIds.length} 个设备)`);
    
    // 发送设备组创建事件
    this.emit('deviceGroupCreated', { groupId, deviceIds: validDeviceIds });
  }

  /**
   * 获取设备组
   * @param groupId 组ID
   * @returns 设备实体数组
   */
  public getDeviceGroup(groupId: string): Entity[] {
    const deviceIds = this.deviceGroups.get(groupId) || [];
    return deviceIds.map(id => this.devices.get(id)!).filter(Boolean);
  }

  /**
   * 批量控制设备组
   * @param groupId 组ID
   * @param action 操作类型
   * @param parameters 操作参数
   */
  public controlDeviceGroup(groupId: string, action: string, parameters?: any): void {
    const devices = this.getDeviceGroup(groupId);
    
    devices.forEach(entity => {
      const component = this.getDeviceComponent(entity.id);
      if (component) {
        switch (action) {
          case 'start':
            component.updateStatus(DeviceStatus.RUNNING);
            break;
          case 'stop':
            component.updateStatus(DeviceStatus.ONLINE);
            break;
          case 'maintenance':
            component.setMaintenanceMode(true);
            break;
          case 'reset':
            component.updateStatus(DeviceStatus.ONLINE);
            component.setMaintenanceMode(false);
            break;
        }
      }
    });
    
    Debug.log('DeviceManager', `设备组操作: ${groupId} - ${action}`);
    
    // 发送设备组操作事件
    this.emit('deviceGroupControlled', { groupId, action, parameters });
  }

  /**
   * 从工厂配置加载设备
   * @param factoryConfig 工厂配置
   */
  public loadFromFactoryConfig(factoryConfig: FactoryConfig): void {
    Debug.log('DeviceManager', `正在加载工厂配置: ${factoryConfig.name}`);
    
    // 创建所有设备
    const createdDevices: string[] = [];
    
    factoryConfig.devices.forEach(deviceConfig => {
      const entity = this.createDevice(deviceConfig);
      if (entity) {
        createdDevices.push(deviceConfig.id);
      }
    });
    
    // 创建生产线设备组
    factoryConfig.productionLines.forEach(line => {
      this.createDeviceGroup(line.id, line.devices);
    });
    
    Debug.log('DeviceManager', `工厂配置加载完成: ${createdDevices.length}/${factoryConfig.devices.length} 个设备`);
    
    // 发送配置加载事件
    this.emit('factoryConfigLoaded', { factoryConfig, createdDevices });
  }

  /**
   * 设置设备事件监听
   * @param component 设备组件
   */
  private setupDeviceEventListeners(component: IndustrialDeviceComponent): void {
    // 状态变更事件
    component.onStatusChanged = (newStatus, oldStatus) => {
      this.emit('deviceStatusChanged', {
        deviceId: component.deviceId,
        newStatus,
        oldStatus
      });
    };
    
    // 数据更新事件
    component.onDataUpdated = (dataPoint) => {
      this.emit('deviceDataUpdated', {
        deviceId: component.deviceId,
        dataPoint
      });
    };
    
    // 报警触发事件
    component.onAlarmTriggered = (alarmId, alarm) => {
      this.emit('deviceAlarmTriggered', {
        deviceId: component.deviceId,
        alarmId,
        alarm
      });
    };
    
    // 报警清除事件
    component.onAlarmCleared = (alarmId) => {
      this.emit('deviceAlarmCleared', {
        deviceId: component.deviceId,
        alarmId
      });
    };
  }

  /**
   * 启动统计更新
   */
  private startStatisticsUpdate(): void {
    setInterval(() => {
      this.updateStatistics();
    }, this.options.statisticsUpdateInterval);
  }

  /**
   * 更新统计信息
   */
  private updateStatistics(): void {
    // 重置统计
    this.initializeStatistics();
    
    // 统计设备数量
    this.statistics.totalDevices = this.devices.size;
    
    let totalOEE = 0;
    let oeeCount = 0;
    let totalDataPoints = 0;
    let totalAlarms = 0;
    
    // 遍历所有设备
    this.devices.forEach(entity => {
      const component = this.getDeviceComponent(entity.id);
      if (component) {
        // 按类型统计
        this.statistics.devicesByType[component.deviceType]++;
        
        // 按状态统计
        this.statistics.devicesByStatus[component.status]++;
        
        // OEE统计
        if (component.performance && component.performance.oee > 0) {
          totalOEE += component.performance.oee;
          oeeCount++;
        }
        
        // 数据点统计
        totalDataPoints += component.getAllDataPoints().length;
        
        // 报警统计
        totalAlarms += component.getActiveAlarms().size;
      }
    });
    
    // 计算平均OEE
    this.statistics.averageOEE = oeeCount > 0 ? totalOEE / oeeCount : 0;
    this.statistics.totalDataPoints = totalDataPoints;
    this.statistics.activeAlarms = totalAlarms;
    
    // 发送统计更新事件
    this.emit('statisticsUpdated', this.statistics);
  }

  /**
   * 获取统计信息
   * @returns 设备统计信息
   */
  public getStatistics(): DeviceStatistics {
    return { ...this.statistics };
  }

  /**
   * 获取设备健康报告
   * @returns 设备健康报告
   */
  public getDeviceHealthReport(): any {
    const report = {
      timestamp: new Date(),
      totalDevices: this.statistics.totalDevices,
      healthyDevices: 0,
      warningDevices: 0,
      errorDevices: 0,
      offlineDevices: 0,
      devices: [] as any[]
    };
    
    this.devices.forEach(entity => {
      const component = this.getDeviceComponent(entity.id);
      if (component) {
        const deviceHealth = {
          deviceId: component.deviceId,
          deviceName: component.deviceName,
          deviceType: component.deviceType,
          status: component.status,
          performance: component.performance,
          activeAlarms: Array.from(component.getActiveAlarms()),
          lastUpdate: component.lastStatusChange
        };
        
        report.devices.push(deviceHealth);
        
        // 统计健康状态
        switch (component.status) {
          case DeviceStatus.RUNNING:
          case DeviceStatus.ONLINE:
            if (component.getActiveAlarms().size === 0) {
              report.healthyDevices++;
            } else {
              report.warningDevices++;
            }
            break;
          case DeviceStatus.ERROR:
          case DeviceStatus.ALARM:
            report.errorDevices++;
            break;
          case DeviceStatus.OFFLINE:
            report.offlineDevices++;
            break;
        }
      }
    });
    
    return report;
  }

  /**
   * 销毁设备管理器
   */
  public destroy(): void {
    // 删除所有设备
    const deviceIds = Array.from(this.devices.keys());
    deviceIds.forEach(id => this.removeDevice(id));
    
    // 清理设备组
    this.deviceGroups.clear();
    
    Debug.log('DeviceManager', '设备管理器已销毁');
  }
}

/**
 * 空间数据服务
 * 提供空间数据的CRUD操作和空间查询功能
 */
import { Injectable, BadRequestException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { CreateSpatialFeatureDto, UpdateSpatialFeatureDto } from '../dto/spatial-feature.dto';
import { CreateSpatialLayerDto, UpdateSpatialLayerDto } from '../dto/spatial-layer.dto';
import { SpatialQueryDto, SpatialAnalysisDto } from '../dto/spatial-query.dto';
import { Logger } from '@nestjs/common';

@Injectable()
export class SpatialDataService {
  private readonly logger = new Logger(SpatialDataService.name);

  constructor(
    @InjectRepository(SpatialFeature)
    private spatialFeatureRepository: Repository<SpatialFeature>,
    @InjectRepository(SpatialLayer)
    private spatialLayerRepository: Repository<SpatialLayer>,
  ) {}

  /**
   * 创建空间要素
   */
  async createFeature(createFeatureDto: CreateSpatialFeatureDto): Promise<SpatialFeature> {
    try {
      // 验证几何数据
      const validatedGeometry = this.validateAndNormalizeGeometry(createFeatureDto.geometry);
      
      const feature = this.spatialFeatureRepository.create({
        ...createFeatureDto,
        geometry: validatedGeometry,
        createdAt: new Date(),
        updatedAt: new Date()
      });

      const savedFeature = await this.spatialFeatureRepository.save(feature);
      this.logger.log(`创建空间要素成功: ${savedFeature.id}`);
      
      return savedFeature;
    } catch (error) {
      this.logger.error(`创建空间要素失败: ${error.message}`);
      throw new BadRequestException(`创建空间要素失败: ${error.message}`);
    }
  }

  /**
   * 获取空间要素
   */
  async getFeature(id: string): Promise<SpatialFeature> {
    const feature = await this.spatialFeatureRepository.findOne({
      where: { id },
      relations: ['layer']
    });

    if (!feature) {
      throw new NotFoundException(`空间要素不存在: ${id}`);
    }

    return feature;
  }

  /**
   * 更新空间要素
   */
  async updateFeature(id: string, updateFeatureDto: UpdateSpatialFeatureDto): Promise<SpatialFeature> {
    const feature = await this.getFeature(id);

    // 如果更新几何数据，需要验证
    if (updateFeatureDto.geometry) {
      updateFeatureDto.geometry = this.validateAndNormalizeGeometry(updateFeatureDto.geometry);
    }

    Object.assign(feature, updateFeatureDto);
    feature.updatedAt = new Date();

    const updatedFeature = await this.spatialFeatureRepository.save(feature);
    this.logger.log(`更新空间要素成功: ${id}`);
    
    return updatedFeature;
  }

  /**
   * 删除空间要素
   */
  async deleteFeature(id: string): Promise<void> {
    const feature = await this.getFeature(id);
    await this.spatialFeatureRepository.remove(feature);
    this.logger.log(`删除空间要素成功: ${id}`);
  }

  /**
   * 查询空间要素
   */
  async queryFeatures(options: {
    layerId?: string;
    bbox?: { minX: number; minY: number; maxX: number; maxY: number };
    limit?: number;
    offset?: number;
  }): Promise<{ features: SpatialFeature[]; total: number }> {
    const queryBuilder = this.spatialFeatureRepository.createQueryBuilder('feature')
      .leftJoinAndSelect('feature.layer', 'layer');

    // 图层过滤
    if (options.layerId) {
      queryBuilder.andWhere('feature.layerId = :layerId', { layerId: options.layerId });
    }

    // 边界框过滤
    if (options.bbox) {
      queryBuilder.andWhere(
        'ST_Intersects(feature.geometry, ST_MakeEnvelope(:minX, :minY, :maxX, :maxY, 4326))',
        options.bbox
      );
    }

    // 分页
    if (options.limit) {
      queryBuilder.limit(options.limit);
    }
    if (options.offset) {
      queryBuilder.offset(options.offset);
    }

    const [features, total] = await queryBuilder.getManyAndCount();
    
    return { features, total };
  }

  /**
   * 空间查询
   */
  async spatialQuery(queryDto: SpatialQueryDto): Promise<SpatialFeature[]> {
    const queryBuilder = this.spatialFeatureRepository.createQueryBuilder('feature')
      .leftJoinAndSelect('feature.layer', 'layer');

    switch (queryDto.type) {
      case 'intersects':
        queryBuilder.where(
          'ST_Intersects(feature.geometry, ST_GeomFromGeoJSON(:geometry))',
          { geometry: JSON.stringify(queryDto.geometry) }
        );
        break;

      case 'within':
        queryBuilder.where(
          'ST_Within(feature.geometry, ST_GeomFromGeoJSON(:geometry))',
          { geometry: JSON.stringify(queryDto.geometry) }
        );
        break;

      case 'contains':
        queryBuilder.where(
          'ST_Contains(feature.geometry, ST_GeomFromGeoJSON(:geometry))',
          { geometry: JSON.stringify(queryDto.geometry) }
        );
        break;

      case 'buffer':
        queryBuilder.where(
          'ST_DWithin(feature.geometry, ST_GeomFromGeoJSON(:geometry), :distance)',
          { 
            geometry: JSON.stringify(queryDto.geometry),
            distance: queryDto.distance || 1000
          }
        );
        break;

      default:
        throw new BadRequestException(`不支持的查询类型: ${queryDto.type}`);
    }

    if (queryDto.layerId) {
      queryBuilder.andWhere('feature.layerId = :layerId', { layerId: queryDto.layerId });
    }

    if (queryDto.bbox) {
      queryBuilder.andWhere(
        'ST_Intersects(feature.geometry, ST_MakeEnvelope(:minX, :minY, :maxX, :maxY, 4326))',
        queryDto.bbox
      );
    }

    return queryBuilder.getMany();
  }

  /**
   * 空间分析
   */
  async spatialAnalysis(analysisDto: SpatialAnalysisDto): Promise<any> {
    try {
      switch (analysisDto.type) {
        case 'buffer':
          return this.bufferAnalysis(analysisDto);
        case 'intersection':
          return this.intersectionAnalysis(analysisDto);
        case 'union':
          return this.unionAnalysis(analysisDto);
        case 'difference':
          return this.differenceAnalysis(analysisDto);
        default:
          throw new BadRequestException(`不支持的分析类型: ${analysisDto.type}`);
      }
    } catch (error) {
      this.logger.error(`空间分析失败: ${error.message}`);
      throw new BadRequestException(`空间分析失败: ${error.message}`);
    }
  }

  /**
   * 缓冲区分析
   */
  private async bufferAnalysis(analysisDto: SpatialAnalysisDto): Promise<any> {
    const { inputLayerId, distance, unit = 'meters' } = analysisDto.parameters;

    // 转换距离单位
    let bufferDistance = distance;
    if (unit === 'kilometers') {
      bufferDistance = distance * 1000;
    } else if (unit === 'degrees') {
      bufferDistance = distance * 111320; // 1度约等于111320米
    }

    const query = `
      SELECT 
        id,
        name,
        properties,
        ST_AsGeoJSON(ST_Buffer(geometry, $1)) as geometry
      FROM spatial_features 
      WHERE layer_id = $2
    `;

    const result = await this.spatialFeatureRepository.query(query, [bufferDistance, inputLayerId]);

    return result.map(row => ({
      id: row.id,
      name: `${row.name}_buffer`,
      properties: {
        ...row.properties,
        originalId: row.id,
        bufferDistance: distance,
        bufferUnit: unit
      },
      geometry: JSON.parse(row.geometry)
    }));
  }

  /**
   * 相交分析
   */
  private async intersectionAnalysis(analysisDto: SpatialAnalysisDto): Promise<any> {
    const { inputLayer1Id, inputLayer2Id } = analysisDto.parameters;

    const query = `
      SELECT 
        f1.id as id1,
        f2.id as id2,
        f1.name as name1,
        f2.name as name2,
        f1.properties as properties1,
        f2.properties as properties2,
        ST_AsGeoJSON(ST_Intersection(f1.geometry, f2.geometry)) as geometry
      FROM spatial_features f1
      JOIN spatial_features f2 ON ST_Intersects(f1.geometry, f2.geometry)
      WHERE f1.layer_id = $1 AND f2.layer_id = $2
        AND ST_GeometryType(ST_Intersection(f1.geometry, f2.geometry)) != 'GEOMETRYCOLLECTION'
        AND NOT ST_IsEmpty(ST_Intersection(f1.geometry, f2.geometry))
    `;

    const result = await this.spatialFeatureRepository.query(query, [inputLayer1Id, inputLayer2Id]);

    return result.map(row => ({
      id: `intersection_${row.id1}_${row.id2}`,
      name: `${row.name1}_${row.name2}_intersection`,
      properties: {
        ...row.properties1,
        ...row.properties2,
        sourceId1: row.id1,
        sourceId2: row.id2,
        analysisType: 'intersection'
      },
      geometry: JSON.parse(row.geometry)
    }));
  }

  /**
   * 联合分析
   */
  private async unionAnalysis(analysisDto: SpatialAnalysisDto): Promise<any> {
    const { inputLayerIds } = analysisDto.parameters;

    if (!inputLayerIds || inputLayerIds.length < 2) {
      throw new BadRequestException('联合分析至少需要两个输入图层');
    }

    const placeholders = inputLayerIds.map((_, index) => `$${index + 1}`).join(',');
    
    const query = `
      SELECT 
        ST_AsGeoJSON(ST_Union(geometry)) as geometry,
        array_agg(id) as source_ids,
        array_agg(name) as source_names
      FROM spatial_features 
      WHERE layer_id IN (${placeholders})
    `;

    const result = await this.spatialFeatureRepository.query(query, inputLayerIds);

    if (result.length > 0 && result[0].geometry) {
      return [{
        id: `union_${Date.now()}`,
        name: 'Union_Result',
        properties: {
          sourceIds: result[0].source_ids,
          sourceNames: result[0].source_names,
          analysisType: 'union'
        },
        geometry: JSON.parse(result[0].geometry)
      }];
    }

    return [];
  }

  /**
   * 差异分析
   */
  private async differenceAnalysis(analysisDto: SpatialAnalysisDto): Promise<any> {
    const { inputLayer1Id, inputLayer2Id } = analysisDto.parameters;

    const query = `
      SELECT 
        f1.id,
        f1.name,
        f1.properties,
        ST_AsGeoJSON(ST_Difference(f1.geometry, f2.geometry)) as geometry
      FROM spatial_features f1
      JOIN spatial_features f2 ON ST_Intersects(f1.geometry, f2.geometry)
      WHERE f1.layer_id = $1 AND f2.layer_id = $2
        AND NOT ST_IsEmpty(ST_Difference(f1.geometry, f2.geometry))
    `;

    const result = await this.spatialFeatureRepository.query(query, [inputLayer1Id, inputLayer2Id]);

    return result.map(row => ({
      id: `${row.id}_difference`,
      name: `${row.name}_difference`,
      properties: {
        ...row.properties,
        originalId: row.id,
        analysisType: 'difference'
      },
      geometry: JSON.parse(row.geometry)
    }));
  }

  /**
   * 创建图层
   */
  async createLayer(createLayerDto: CreateSpatialLayerDto): Promise<SpatialLayer> {
    const layer = this.spatialLayerRepository.create({
      ...createLayerDto,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    const savedLayer = await this.spatialLayerRepository.save(layer);
    this.logger.log(`创建图层成功: ${savedLayer.id}`);
    
    return savedLayer;
  }

  /**
   * 获取图层列表
   */
  async getLayers(projectId?: string): Promise<SpatialLayer[]> {
    const queryBuilder = this.spatialLayerRepository.createQueryBuilder('layer')
      .leftJoinAndSelect('layer.features', 'features')
      .leftJoinAndSelect('layer.project', 'project');

    if (projectId) {
      queryBuilder.where('layer.projectId = :projectId', { projectId });
    }

    return queryBuilder.getMany();
  }

  /**
   * 导入空间数据
   */
  async importSpatialData(file: Express.Multer.File, options: any): Promise<{ layerId: string; featureCount: number }> {
    try {
      const fileContent = file.buffer.toString('utf-8');
      const geoJSON = JSON.parse(fileContent);

      if (geoJSON.type !== 'FeatureCollection') {
        throw new BadRequestException('不支持的GeoJSON格式');
      }

      // 创建新图层
      const layer = await this.createLayer({
        name: options.layerName || `导入图层_${Date.now()}`,
        description: options.description || '从文件导入的图层',
        layerType: 'vector',
        projectId: options.projectId,
        metadata: {
          importedFrom: file.originalname,
          importedAt: new Date().toISOString(),
          featureCount: geoJSON.features.length
        }
      });

      // 导入要素
      let featureCount = 0;
      for (const feature of geoJSON.features) {
        try {
          await this.createFeature({
            name: feature.properties?.name || `要素_${featureCount + 1}`,
            description: feature.properties?.description,
            featureType: feature.geometry.type as any,
            geometry: feature.geometry,
            properties: feature.properties || {},
            layerId: layer.id,
            style: feature.properties?.style
          });
          featureCount++;
        } catch (error) {
          this.logger.warn(`导入要素失败: ${error.message}`);
        }
      }

      this.logger.log(`导入空间数据成功: 图层 ${layer.id}, 要素 ${featureCount} 个`);
      
      return { layerId: layer.id, featureCount };
    } catch (error) {
      this.logger.error(`导入空间数据失败: ${error.message}`);
      throw new BadRequestException(`导入空间数据失败: ${error.message}`);
    }
  }

  /**
   * 导出空间数据
   */
  async exportSpatialData(layerId: string, format: 'geojson' | 'shapefile' | 'kml' = 'geojson'): Promise<any> {
    const features = await this.spatialFeatureRepository.find({
      where: { layerId },
      relations: ['layer']
    });

    switch (format) {
      case 'geojson':
        return this.exportAsGeoJSON(features);
      case 'kml':
        return this.exportAsKML(features);
      case 'shapefile':
        throw new BadRequestException('Shapefile导出功能暂未实现');
      default:
        throw new BadRequestException(`不支持的导出格式: ${format}`);
    }
  }

  /**
   * 导出为GeoJSON
   */
  private exportAsGeoJSON(features: SpatialFeature[]): any {
    return {
      type: 'FeatureCollection',
      features: features.map(feature => ({
        type: 'Feature',
        geometry: feature.geometry,
        properties: {
          ...feature.properties,
          id: feature.id,
          name: feature.name,
          description: feature.description,
          style: feature.style
        }
      }))
    };
  }

  /**
   * 导出为KML
   */
  private exportAsKML(features: SpatialFeature[]): string {
    // 简化的KML导出实现
    const kmlFeatures = features.map(feature => {
      // 这里需要实现GeoJSON到KML的转换
      return `
        <Placemark>
          <name>${feature.name}</name>
          <description>${feature.description || ''}</description>
        </Placemark>
      `;
    }).join('\n');

    return `<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2">
  <Document>
    <name>Exported Spatial Data</name>
    ${kmlFeatures}
  </Document>
</kml>`;
  }

  /**
   * 验证和标准化几何数据
   */
  private validateAndNormalizeGeometry(geometry: any): any {
    if (!geometry || !geometry.type || !geometry.coordinates) {
      throw new BadRequestException('无效的几何数据格式');
    }

    const validTypes = ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'];
    if (!validTypes.includes(geometry.type)) {
      throw new BadRequestException(`不支持的几何类型: ${geometry.type}`);
    }

    // 标准化坐标精度
    const normalizeCoordinates = (coords: any): any => {
      if (Array.isArray(coords[0])) {
        return coords.map(normalizeCoordinates);
      }
      return coords.map((coord: number) => Math.round(coord * 1000000) / 1000000);
    };

    return {
      ...geometry,
      coordinates: normalizeCoordinates(geometry.coordinates)
    };
  }
}

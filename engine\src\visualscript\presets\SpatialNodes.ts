/**
 * 空间信息系统视觉脚本节点注册
 * 为视觉脚本系统注册空间信息相关的节点
 */
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory } from '../nodes/Node';
import { 
  CreateGeographicCoordinateNode,
  CoordinateTransformNode,
  CreateGeospatialComponentNode,
  AddGeospatialComponentNode,
  GetGeographicCoordinateNode,
  SetGeographicCoordinateNode,
  CalculateDistanceNode,
  BufferAnalysisNode,
  IntersectionAnalysisNode,
  PointInPolygonNode,
  CreateGeoJSONNode,
  CreateFromGeoJSONNode,
  SetMapViewNode,
  GetMapViewNode,
  SetMapProviderNode
} from '../nodes/spatial/SpatialNodes';

/**
 * 注册空间信息系统节点
 * @param registry 节点注册表
 */
export function registerSpatialNodes(registry: NodeRegistry): void {
  // 地理坐标相关节点
  registry.registerNodeType({
    type: 'spatial/coordinate/create',
    category: NodeCategory.SPATIAL,
    constructor: CreateGeographicCoordinateNode,
    label: '创建地理坐标',
    description: '创建一个地理坐标对象，包含经度、纬度和海拔信息',
    icon: 'location',
    color: '#4CAF50',
    tags: ['spatial', 'coordinate', 'geography', 'location'],
    version: '1.0.0',
    performanceLevel: 1,
    memoryLevel: 1
  });

  registry.registerNodeType({
    type: 'spatial/coordinate/transform',
    category: NodeCategory.SPATIAL,
    constructor: CoordinateTransformNode,
    label: '坐标转换',
    description: '在不同坐标系统之间转换地理坐标（WGS84、GCJ02、BD09等）',
    icon: 'transform',
    color: '#2196F3',
    tags: ['spatial', 'coordinate', 'transform', 'conversion'],
    version: '1.0.0',
    performanceLevel: 2,
    memoryLevel: 1
  });

  // 地理空间组件相关节点
  registry.registerNodeType({
    type: 'spatial/component/create',
    category: NodeCategory.SPATIAL,
    constructor: CreateGeospatialComponentNode,
    label: '创建地理空间组件',
    description: '创建一个地理空间组件，用于为实体添加地理位置信息',
    icon: 'component',
    color: '#FF9800',
    tags: ['spatial', 'component', 'entity', 'geospatial'],
    version: '1.0.0',
    performanceLevel: 2,
    memoryLevel: 2
  });

  registry.registerNodeType({
    type: 'spatial/component/add',
    category: NodeCategory.SPATIAL,
    constructor: AddGeospatialComponentNode,
    label: '添加地理空间组件',
    description: '将地理空间组件添加到实体上',
    icon: 'add',
    color: '#4CAF50',
    tags: ['spatial', 'component', 'entity', 'attach'],
    version: '1.0.0',
    performanceLevel: 1,
    memoryLevel: 1
  });

  registry.registerNodeType({
    type: 'spatial/coordinate/get',
    category: NodeCategory.SPATIAL,
    constructor: GetGeographicCoordinateNode,
    label: '获取地理坐标',
    description: '从地理空间组件中获取地理坐标信息',
    icon: 'get',
    color: '#9C27B0',
    tags: ['spatial', 'coordinate', 'get', 'query'],
    version: '1.0.0',
    performanceLevel: 1,
    memoryLevel: 1
  });

  registry.registerNodeType({
    type: 'spatial/coordinate/set',
    category: NodeCategory.SPATIAL,
    constructor: SetGeographicCoordinateNode,
    label: '设置地理坐标',
    description: '设置地理空间组件的地理坐标',
    icon: 'set',
    color: '#E91E63',
    tags: ['spatial', 'coordinate', 'set', 'update'],
    version: '1.0.0',
    performanceLevel: 1,
    memoryLevel: 1
  });

  // 空间分析相关节点
  registry.registerNodeType({
    type: 'spatial/analysis/distance',
    category: NodeCategory.SPATIAL,
    constructor: CalculateDistanceNode,
    label: '计算距离',
    description: '计算两个地理坐标之间的距离（使用Haversine公式）',
    icon: 'ruler',
    color: '#607D8B',
    tags: ['spatial', 'analysis', 'distance', 'measurement'],
    version: '1.0.0',
    performanceLevel: 2,
    memoryLevel: 1
  });

  registry.registerNodeType({
    type: 'spatial/analysis/buffer',
    category: NodeCategory.SPATIAL,
    constructor: BufferAnalysisNode,
    label: '缓冲区分析',
    description: '对几何对象进行缓冲区分析，生成指定距离的缓冲区',
    icon: 'buffer',
    color: '#795548',
    tags: ['spatial', 'analysis', 'buffer', 'geometry'],
    version: '1.0.0',
    performanceLevel: 3,
    memoryLevel: 2
  });

  registry.registerNodeType({
    type: 'spatial/analysis/intersection',
    category: NodeCategory.SPATIAL,
    constructor: IntersectionAnalysisNode,
    label: '相交分析',
    description: '分析两个几何对象的相交关系，返回相交部分',
    icon: 'intersection',
    color: '#3F51B5',
    tags: ['spatial', 'analysis', 'intersection', 'geometry'],
    version: '1.0.0',
    performanceLevel: 3,
    memoryLevel: 2
  });

  registry.registerNodeType({
    type: 'spatial/analysis/point-in-polygon',
    category: NodeCategory.SPATIAL,
    constructor: PointInPolygonNode,
    label: '点在多边形内',
    description: '判断一个点是否在多边形内部',
    icon: 'point-in-polygon',
    color: '#009688',
    tags: ['spatial', 'analysis', 'point', 'polygon', 'containment'],
    version: '1.0.0',
    performanceLevel: 2,
    memoryLevel: 1
  });

  // GeoJSON相关节点
  registry.registerNodeType({
    type: 'spatial/geojson/create',
    category: NodeCategory.SPATIAL,
    constructor: CreateGeoJSONNode,
    label: '创建GeoJSON',
    description: '将地理空间组件转换为GeoJSON格式',
    icon: 'json',
    color: '#FF5722',
    tags: ['spatial', 'geojson', 'export', 'format'],
    version: '1.0.0',
    performanceLevel: 1,
    memoryLevel: 1
  });

  registry.registerNodeType({
    type: 'spatial/geojson/parse',
    category: NodeCategory.SPATIAL,
    constructor: CreateFromGeoJSONNode,
    label: '解析GeoJSON',
    description: '从GeoJSON数据创建地理空间组件',
    icon: 'parse',
    color: '#673AB7',
    tags: ['spatial', 'geojson', 'import', 'parse'],
    version: '1.0.0',
    performanceLevel: 2,
    memoryLevel: 2
  });

  // 地图相关节点
  registry.registerNodeType({
    type: 'spatial/map/set-view',
    category: NodeCategory.SPATIAL,
    constructor: SetMapViewNode,
    label: '设置地图视图',
    description: '设置地图的中心位置和缩放级别',
    icon: 'map',
    color: '#8BC34A',
    tags: ['spatial', 'map', 'view', 'navigation'],
    version: '1.0.0',
    performanceLevel: 2,
    memoryLevel: 1
  });

  registry.registerNodeType({
    type: 'spatial/map/get-view',
    category: NodeCategory.SPATIAL,
    constructor: GetMapViewNode,
    label: '获取地图视图',
    description: '获取当前地图的中心位置和缩放级别',
    icon: 'map-view',
    color: '#CDDC39',
    tags: ['spatial', 'map', 'view', 'query'],
    version: '1.0.0',
    performanceLevel: 1,
    memoryLevel: 1
  });

  registry.registerNodeType({
    type: 'spatial/map/set-provider',
    category: NodeCategory.SPATIAL,
    constructor: SetMapProviderNode,
    label: '设置地图提供者',
    description: '设置地图瓦片提供者（OSM、卫星、地形等）',
    icon: 'provider',
    color: '#FFC107',
    tags: ['spatial', 'map', 'provider', 'tiles'],
    version: '1.0.0',
    performanceLevel: 1,
    memoryLevel: 1
  });

  console.log('✅ 空间信息系统节点注册完成 - 已注册 15 个节点类型');
}

/**
 * 获取空间节点统计信息
 */
export function getSpatialNodeStats(): {
  totalNodes: number;
  categories: string[];
  nodeTypes: string[];
} {
  return {
    totalNodes: 15,
    categories: ['SPATIAL'],
    nodeTypes: [
      'spatial/coordinate/create',
      'spatial/coordinate/transform',
      'spatial/component/create',
      'spatial/component/add',
      'spatial/coordinate/get',
      'spatial/coordinate/set',
      'spatial/analysis/distance',
      'spatial/analysis/buffer',
      'spatial/analysis/intersection',
      'spatial/analysis/point-in-polygon',
      'spatial/geojson/create',
      'spatial/geojson/parse',
      'spatial/map/set-view',
      'spatial/map/get-view',
      'spatial/map/set-provider'
    ]
  };
}

/**
 * 验证空间节点注册
 */
export function validateSpatialNodeRegistration(registry: NodeRegistry): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  const stats = getSpatialNodeStats();

  // 检查所有节点是否已注册
  for (const nodeType of stats.nodeTypes) {
    if (!registry.hasNodeType(nodeType)) {
      errors.push(`空间节点类型未注册: ${nodeType}`);
    }
  }

  // 检查节点类别
  const spatialNodes = registry.getNodesByCategory(NodeCategory.SPATIAL);
  if (spatialNodes.length === 0) {
    warnings.push('未找到空间类别的节点');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

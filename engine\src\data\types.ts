/**
 * 数据管理类型定义
 */

/**
 * 数据类型
 */
export enum DataType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
  DATE = 'date',
  BINARY = 'binary'
}

/**
 * 数据存储配置
 */
export interface DataStoreConfig {
  name: string;
  type: 'memory' | 'indexeddb' | 'localstorage' | 'remote';
  maxSize?: number;
  ttl?: number; // Time to live in milliseconds
  compression?: boolean;
  encryption?: boolean;
}

/**
 * 数据项
 */
export interface DataItem {
  id: string;
  key: string;
  value: any;
  type: DataType;
  timestamp: Date;
  metadata?: Record<string, any>;
}

/**
 * 查询条件
 */
export interface QueryCondition {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
}

/**
 * 查询选项
 */
export interface QueryOptions {
  conditions?: QueryCondition[];
  sort?: { field: string; order: 'asc' | 'desc' }[];
  limit?: number;
  offset?: number;
  fields?: string[];
}

/**
 * 查询结果
 */
export interface QueryResult<T = any> {
  data: T[];
  total: number;
  hasMore: boolean;
  nextOffset?: number;
}

/**
 * 数据事件类型
 */
export enum DataEventType {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  QUERIED = 'queried'
}

/**
 * 数据事件
 */
export interface DataEvent {
  type: DataEventType;
  storeId: string;
  itemId?: string;
  data?: any;
  timestamp: Date;
}

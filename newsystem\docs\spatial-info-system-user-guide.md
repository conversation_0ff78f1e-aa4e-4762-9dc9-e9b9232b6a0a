# DL引擎空间信息系统使用指南

## 1. 系统介绍

DL引擎空间信息系统是一个集成在多媒体/游戏引擎中的地理信息系统（GIS），提供地图显示、空间数据管理、地理分析等功能。

### 1.1 主要功能
- 🗺️ **地图显示**：支持多种地图类型和交互操作
- 📍 **空间数据管理**：创建、编辑、管理地理要素
- 🔍 **空间分析**：缓冲区、相交、联合等空间分析工具
- 📊 **数据可视化**：丰富的地图样式和图例配置
- 🔄 **数据导入导出**：支持多种地理数据格式
- 🎯 **视觉脚本集成**：通过节点进行空间操作编程

## 2. 快速开始

### 2.1 启动系统
```bash
# 启动服务器端
cd server/spatial-service
npm install
npm run start:dev

# 启动编辑器
cd editor
npm install
npm start
```

### 2.2 访问系统
- **编辑器界面**：http://localhost:3000
- **API文档**：http://localhost:3001/api/docs
- **服务健康检查**：http://localhost:3001/api/v1/spatial-data/health

## 3. 编辑器使用指南

### 3.1 空间信息系统主界面

#### 3.1.1 界面布局
```
┌─────────────────────────────────────────────────────────┐
│  空间信息系统                                [设置] [信息] │
├─────────────────────────────────────────────────────────┤
│                                                         │
│                    地图显示区域                          │
│                                                         │
│                                                         │
├─────────────────────────────────────────────────────────┤
│ [地图] [编辑] [分析]                                     │
│ ┌─────────────────────────────────────────────────────┐ │
│ │                侧边栏内容区域                        │ │
│ │                                                     │ │
│ └─────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────┘
```

#### 3.1.2 基本操作
- **地图导航**：鼠标拖拽平移，滚轮缩放
- **坐标显示**：鼠标移动显示当前坐标
- **地图类型切换**：街道地图、卫星地图、地形地图
- **定位功能**：点击定位按钮获取当前位置

### 3.2 地图视图组件 (MapView)

#### 3.2.1 地图控制
```tsx
// 基本使用
<MapView
  center={{ longitude: 116.404, latitude: 39.915 }}
  zoom={10}
  mapType="osm"
  showControls={true}
  showCoordinates={true}
/>
```

#### 3.2.2 控制面板功能
- **坐标输入**：手动输入经纬度定位
- **缩放控制**：放大/缩小按钮和数值输入
- **地图类型选择**：下拉菜单切换地图类型
- **定位按钮**：获取用户当前位置

### 3.3 地理数据编辑器 (GeospatialEditor)

#### 3.3.1 工具栏
- **选择工具** 🔍：选择和查看要素
- **点工具** 📍：创建点要素
- **线工具** 📏：创建线要素
- **面工具** 🔲：创建面要素

#### 3.3.2 要素操作
1. **创建要素**：
   ```
   1. 选择对应的绘制工具
   2. 在地图上点击创建要素
   3. 设置要素属性和样式
   4. 保存要素
   ```

2. **编辑要素**：
   ```
   1. 使用选择工具点击要素
   2. 在要素列表中点击编辑按钮
   3. 修改要素属性或几何形状
   4. 保存修改
   ```

3. **删除要素**：
   ```
   1. 选择要删除的要素
   2. 点击删除按钮
   3. 确认删除操作
   ```

#### 3.3.3 要素列表
- **要素信息**：显示要素名称、类型、坐标数量
- **可见性控制**：显示/隐藏要素
- **编辑操作**：编辑、删除要素
- **选择高亮**：点击要素在地图上高亮显示

#### 3.3.4 数据导入导出
1. **导入数据**：
   ```
   1. 点击导入按钮
   2. 选择GeoJSON文件
   3. 确认导入设置
   4. 等待导入完成
   ```

2. **导出数据**：
   ```
   1. 点击导出按钮
   2. 选择导出格式（GeoJSON/KML等）
   3. 下载导出文件
   ```

### 3.4 空间分析面板 (SpatialAnalysisPanel)

#### 3.4.1 分析工具
- **缓冲区分析** 🎯：为几何对象创建缓冲区
- **相交分析** ⚡：计算两个几何对象的相交部分
- **联合分析** 🔗：合并多个几何对象
- **差异分析** ➖：计算几何对象的差异部分
- **距离分析** 📏：计算几何对象之间的距离

#### 3.4.2 缓冲区分析操作
```
1. 选择"缓冲区分析"
2. 选择输入图层
3. 设置缓冲距离和单位
4. 设置分段数（影响圆滑度）
5. 点击"运行分析"
6. 查看分析结果
```

#### 3.4.3 相交分析操作
```
1. 选择"相交分析"
2. 选择输入图层1
3. 选择输入图层2
4. 点击"运行分析"
5. 查看相交结果
```

#### 3.4.4 分析历史
- **任务列表**：显示所有分析任务
- **状态跟踪**：等待中、运行中、已完成、失败
- **进度显示**：实时显示分析进度
- **结果导出**：导出分析结果
- **任务管理**：删除不需要的任务

## 4. 视觉脚本系统使用

### 4.1 空间节点类别
空间信息系统为视觉脚本系统提供了15个专用节点，分为以下类别：

#### 4.1.1 坐标操作节点
- **创建地理坐标**：创建经纬度坐标对象
- **坐标转换**：在不同坐标系间转换
- **获取地理坐标**：从组件获取坐标信息
- **设置地理坐标**：更新组件坐标

#### 4.1.2 组件操作节点
- **创建地理空间组件**：创建新的地理空间组件
- **添加地理空间组件**：将组件添加到实体

#### 4.1.3 分析节点
- **计算距离**：计算两点间距离
- **缓冲区分析**：创建缓冲区
- **相交分析**：计算相交关系
- **点在多边形内**：判断点是否在多边形内

#### 4.1.4 地图操作节点
- **设置地图视图**：设置地图中心和缩放
- **获取地图视图**：获取当前地图状态
- **设置地图提供者**：切换地图类型

#### 4.1.5 数据格式节点
- **创建GeoJSON**：将组件转换为GeoJSON
- **解析GeoJSON**：从GeoJSON创建组件

### 4.2 节点使用示例

#### 4.2.1 创建地理坐标
```
[创建地理坐标]
├─ 经度: 116.404
├─ 纬度: 39.915
├─ 海拔: 0
└─ 输出: 坐标对象
```

#### 4.2.2 坐标转换流程
```
[创建地理坐标] → [坐标转换] → [创建地理空间组件]
                     ↓
                [WGS84转GCJ02]
```

#### 4.2.3 缓冲区分析流程
```
[获取几何对象] → [缓冲区分析] → [显示结果]
                    ↓
               [距离: 1000米]
               [单位: 米]
               [分段: 32]
```

## 5. API使用指南

### 5.1 认证
所有API请求需要在Header中包含JWT Token：
```http
Authorization: Bearer <your-jwt-token>
```

### 5.2 空间要素API

#### 5.2.1 创建要素
```http
POST /api/v1/spatial-data/features
Content-Type: application/json

{
  "name": "测试点",
  "featureType": "Point",
  "geometry": {
    "type": "Point",
    "coordinates": [116.404, 39.915]
  },
  "properties": {
    "description": "这是一个测试点"
  },
  "layerId": "layer-uuid"
}
```

#### 5.2.2 查询要素
```http
GET /api/v1/spatial-data/features?layerId=layer-uuid&limit=100&offset=0
```

#### 5.2.3 空间查询
```http
POST /api/v1/spatial-data/query/spatial
Content-Type: application/json

{
  "type": "intersects",
  "geometry": {
    "type": "Polygon",
    "coordinates": [[
      [116.3, 39.9],
      [116.5, 39.9],
      [116.5, 40.0],
      [116.3, 40.0],
      [116.3, 39.9]
    ]]
  },
  "layerId": "layer-uuid"
}
```

### 5.3 空间分析API

#### 5.3.1 缓冲区分析
```http
POST /api/v1/spatial-data/analysis/spatial
Content-Type: application/json

{
  "type": "buffer",
  "parameters": {
    "inputLayerId": "layer-uuid",
    "distance": 1000,
    "unit": "meters",
    "segments": 32
  }
}
```

## 6. 数据格式说明

### 6.1 GeoJSON格式
```json
{
  "type": "FeatureCollection",
  "features": [
    {
      "type": "Feature",
      "geometry": {
        "type": "Point",
        "coordinates": [116.404, 39.915]
      },
      "properties": {
        "name": "北京天安门",
        "description": "中华人民共和国首都北京的象征"
      }
    }
  ]
}
```

### 6.2 坐标系统
- **WGS84 (EPSG:4326)**：国际标准坐标系
- **GCJ02 (EPSG:4490)**：中国火星坐标系
- **BD09**：百度坐标系
- **WebMercator (EPSG:3857)**：Web地图投影

### 6.3 几何类型
- **Point**：点 `[longitude, latitude]`
- **LineString**：线 `[[lon1, lat1], [lon2, lat2], ...]`
- **Polygon**：面 `[[[外环坐标]], [[内环坐标]]]`

## 7. 常见问题解答

### 7.1 地图显示问题
**Q: 地图无法显示或显示空白？**
A: 检查网络连接，确认地图服务可访问，检查坐标系统设置。

**Q: 地图显示位置不准确？**
A: 确认使用的坐标系统，可能需要进行坐标转换。

### 7.2 数据导入问题
**Q: GeoJSON文件导入失败？**
A: 检查文件格式是否正确，确认坐标系统匹配，检查文件大小限制。

**Q: 导入的要素不显示？**
A: 检查图层可见性设置，确认要素在当前地图范围内。

### 7.3 空间分析问题
**Q: 分析结果为空？**
A: 检查输入数据是否有效，确认分析参数设置正确。

**Q: 分析速度很慢？**
A: 减少输入要素数量，优化分析参数，考虑使用异步分析。

### 7.4 性能优化建议
1. **大数据集处理**：使用分页查询，启用空间索引
2. **地图渲染优化**：合理设置缩放级别，使用瓦片缓存
3. **内存管理**：及时清理不需要的要素，使用对象池
4. **网络优化**：启用数据压缩，使用CDN加速

## 8. 最佳实践

### 8.1 数据组织
- 按功能或主题创建不同图层
- 合理设置要素属性和样式
- 定期备份重要数据
- 使用有意义的命名规范

### 8.2 性能优化
- 避免在单个图层中存储过多要素
- 合理使用空间索引
- 优化复杂几何对象
- 使用适当的坐标精度

### 8.3 用户体验
- 提供清晰的操作反馈
- 合理设置默认参数
- 提供撤销/重做功能
- 支持键盘快捷键

## 9. 技术支持

### 9.1 文档资源
- **技术文档**：`/docs/spatial-info-system-tech-analysis.md`
- **API文档**：http://localhost:3001/api/docs
- **示例代码**：`/examples/spatial-examples/`

### 9.2 社区支持
- **GitHub Issues**：报告问题和建议
- **开发者论坛**：技术讨论和经验分享
- **在线文档**：最新功能和更新说明

### 9.3 联系方式
- **技术支持邮箱**：<EMAIL>
- **开发者QQ群**：123456789
- **官方网站**：https://dl-engine.com

---

*本指南将随着系统功能的更新而持续完善，建议定期查看最新版本。*

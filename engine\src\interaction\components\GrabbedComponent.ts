/**
 * 被抓取组件
 * 用于标记当前被抓取的对象
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Hand } from './GrabbableComponent';

/**
 * 被抓取组件配置
 */
export interface GrabbedComponentConfig {
  /** 抓取者 */
  grabber: Entity;
  /** 抓取手 */
  hand: Hand;
  /** 抓取偏移 */
  offset?: { x: number; y: number; z: number };
}

/**
 * 被抓取组件
 * 当一个实体被抓取时，会添加此组件
 */
export class GrabbedComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'GrabbedComponent';

  /** 抓取者 */
  private _grabber: Entity;

  /** 抓取手 */
  private _hand: Hand;

  /** 抓取偏移 */
  private _offset: { x: number; y: number; z: number };

  /** 抓取时间戳 */
  private _grabTime: number;

  /**
   * 构造函数
   * @param entity 关联的实体（可选，为了兼容旧的API）
   * @param config 组件配置
   */
  constructor(entity?: Entity | GrabbedComponentConfig, config?: GrabbedComponentConfig) {
    // 处理参数重载
    let actualConfig: GrabbedComponentConfig;
    if (entity && typeof entity === 'object' && !('id' in entity)) {
      // 第一个参数是配置对象
      actualConfig = entity as GrabbedComponentConfig;
    } else {
      // 第一个参数是实体或为空
      if (!config) {
        throw new Error('GrabbedComponent requires config when entity is provided');
      }
      actualConfig = config;
    }

    // 调用基类构造函数，传入组件类型名称
    super(GrabbedComponent.TYPE, {
      enabled: true,
      updatePriority: 0,
      serializable: true
    });

    // 如果传入了实体，设置实体引用
    if (entity && 'id' in entity) {
      this.setEntity(entity as Entity);
    }

    // 初始化属性
    this._grabber = actualConfig.grabber;
    this._hand = actualConfig.hand;
    this._offset = actualConfig.offset || { x: 0, y: 0, z: 0 };
    this._grabTime = Date.now();
  }

  /**
   * 获取抓取者
   */
  get grabber(): Entity {
    return this._grabber;
  }

  /**
   * 获取抓取手
   */
  get hand(): Hand {
    return this._hand;
  }

  /**
   * 获取抓取偏移
   */
  get offset(): { x: number; y: number; z: number } {
    return this._offset;
  }

  /**
   * 设置抓取偏移
   */
  set offset(value: { x: number; y: number; z: number }) {
    this._offset = value;
  }

  /**
   * 获取抓取时间戳
   */
  get grabTime(): number {
    return this._grabTime;
  }

  /**
   * 获取抓取持续时间（毫秒）
   */
  getDuration(): number {
    return Date.now() - this._grabTime;
  }

  /**
   * 创建组件实例
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new GrabbedComponent({
      grabber: this._grabber,
      hand: this._hand,
      offset: this._offset
    });
  }

  /**
   * 序列化自定义数据
   * @returns 自定义数据
   */
  protected serializeData(): any {
    return {
      grabber: this._grabber?.id,
      hand: this._hand,
      offset: this._offset,
      grabTime: this._grabTime
    };
  }

  /**
   * 反序列化自定义数据
   * @param data 自定义数据
   */
  protected deserializeData(data: any): void {
    // 注意：grabber 需要在反序列化时重新设置实体引用
    if (data.hand !== undefined) this._hand = data.hand;
    if (data.offset !== undefined) this._offset = data.offset;
    if (data.grabTime !== undefined) this._grabTime = data.grabTime;
  }
}

/**
 * XR抓取组件
 * 用于处理VR/AR环境中的抓取交互
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter, EventCallback } from '../../utils/EventEmitter';
import { Debug } from '../../utils/Debug';
import { Vector3, Quaternion, Matrix4, Euler } from 'three';
import { Hand } from './GrabbableComponent';
import { GrabberComponent } from './GrabberComponent';
import type { Transform } from '../../scene/Transform';

/**
 * XR控制器类型
 */
export enum XRControllerType {
  /** 左手控制器 */
  LEFT = 'left',
  /** 右手控制器 */
  RIGHT = 'right',
  /** 头戴设备 */
  HEAD = 'head',
  /** 手部追踪 */
  HAND = 'hand'
}

/**
 * XR抓取组件配置
 */
export interface XRGrabComponentConfig {
  /** 是否启用 */
  enabled?: boolean;
  /** 控制器类型 */
  controllerType?: XRControllerType;
  /** 抓取按钮索引 */
  grabButtonIndex?: number;
  /** 释放按钮索引 */
  releaseButtonIndex?: number;
  /** 抓取距离 */
  grabDistance?: number;
  /** 是否使用射线检测 */
  useRaycasting?: boolean;
  /** 是否使用触觉反馈 */
  useHapticFeedback?: boolean;
  /** 触觉反馈强度 */
  hapticFeedbackIntensity?: number;
  /** 触觉反馈持续时间（毫秒） */
  hapticFeedbackDuration?: number;
  /** 是否使用手部姿势识别 */
  useHandPoseDetection?: boolean;
  /** 抓取回调 */
  onGrab?: (entity: Entity, hand: Hand) => void;
  /** 释放回调 */
  onRelease?: (entity: Entity, hand: Hand) => void;
}

/**
 * XR抓取组件
 */
export class XRGrabComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'XRGrabComponent';

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 是否启用 */
  private _enabled: boolean;

  /** 控制器类型 */
  private _controllerType: XRControllerType;

  /** 抓取按钮索引 */
  private _grabButtonIndex: number;

  /** 释放按钮索引 */
  private _releaseButtonIndex: number;

  /** 抓取距离 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _grabDistance: number;

  /** 是否使用射线检测 */
  private _useRaycasting: boolean;

  /** 是否使用触觉反馈 */
  private _useHapticFeedback: boolean;

  /** 触觉反馈强度 */
  private _hapticFeedbackIntensity: number;

  /** 触觉反馈持续时间（毫秒） */
  private _hapticFeedbackDuration: number;

  /** 是否使用手部姿势识别 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _useHandPoseDetection: boolean;

  /** 抓取回调 */
  private _onGrab?: (entity: Entity, hand: Hand) => void;

  /** 释放回调 */
  private _onRelease?: (entity: Entity, hand: Hand) => void;

  /** 当前抓取的实体 */
  private _grabbedEntity?: Entity;

  /** 控制器变换 */
  private _controllerTransform?: Transform;

  /** 控制器矩阵 */
  private _controllerMatrix: Matrix4 = new Matrix4();

  /** 抓取偏移矩阵 */
  private _grabOffsetMatrix: Matrix4 = new Matrix4();

  /** 是否正在抓取 */
  private _isGrabbing: boolean = false;

  /** 上一帧按钮状态 */
  private _prevButtonStates: boolean[] = [];

  /** 关联的抓取者组件 */
  private _grabberComponent?: GrabberComponent;

  /** XR会话 */
  private _xrSession?: any;

  /** XR输入源 */
  private _xrInputSource?: any;

  /** XR参考空间 - 预留功能 */
  // @ts-ignore: 预留功能，暂未使用
  private _xrReferenceSpace?: any;

  /**
   * 构造函数
   * @param entity 实体（可选，为了兼容旧的API）
   * @param config 配置
   */
  constructor(entity?: Entity | XRGrabComponentConfig, config: XRGrabComponentConfig = {}) {
    // 处理参数重载
    let actualConfig: XRGrabComponentConfig;
    if (entity && typeof entity === 'object' && !('id' in entity)) {
      // 第一个参数是配置对象
      actualConfig = entity as XRGrabComponentConfig;
    } else {
      // 第一个参数是实体或为空
      actualConfig = config;
    }

    super(XRGrabComponent.TYPE, {
      enabled: actualConfig.enabled,
      updatePriority: 0,
      serializable: true
    });

    // 如果传入了实体，设置实体引用
    if (entity && 'id' in entity) {
      this.setEntity(entity as Entity);
    }

    // 设置配置
    this._enabled = actualConfig.enabled !== undefined ? actualConfig.enabled : true;
    this._controllerType = actualConfig.controllerType || XRControllerType.RIGHT;
    this._grabButtonIndex = actualConfig.grabButtonIndex !== undefined ? actualConfig.grabButtonIndex : 0;
    this._releaseButtonIndex = actualConfig.releaseButtonIndex !== undefined ? actualConfig.releaseButtonIndex : 0;
    this._grabDistance = actualConfig.grabDistance || 0.1;
    this._useRaycasting = actualConfig.useRaycasting !== undefined ? actualConfig.useRaycasting : true;
    this._useHapticFeedback = actualConfig.useHapticFeedback !== undefined ? actualConfig.useHapticFeedback : true;
    this._hapticFeedbackIntensity = actualConfig.hapticFeedbackIntensity || 1.0;
    this._hapticFeedbackDuration = actualConfig.hapticFeedbackDuration || 100;
    this._useHandPoseDetection = actualConfig.useHandPoseDetection !== undefined ? actualConfig.useHandPoseDetection : false;
    this._onGrab = actualConfig.onGrab;
    this._onRelease = actualConfig.onRelease;

    // 查找抓取者组件
    if (this.entity) {
      this._grabberComponent = this.entity.getComponent<GrabberComponent>(GrabberComponent.TYPE);
      if (!this._grabberComponent && this._enabled) {
        Debug.warn('XRGrabComponent', `实体 ${this.entity.id} 没有 GrabberComponent，XR抓取功能可能无法正常工作`);
      }

      // 获取控制器变换
      this._controllerTransform = this.entity.getComponent<Transform>('Transform');
      if (!this._controllerTransform) {
        Debug.warn('XRGrabComponent', `实体 ${this.entity.id} 没有 Transform 组件，XR抓取功能可能无法正常工作`);
      }
    }
  }

  /**
   * 初始化组件
   */
  initialize(): void {
    if (!this._enabled) return;

    // 如果没有抓取者组件，尝试添加一个
    if (!this._grabberComponent) {
      this._grabberComponent = new GrabberComponent(this.entity);
      this.entity.addComponent(this._grabberComponent);
    }

    // 初始化按钮状态
    this._prevButtonStates = [];
  }

  /**
   * 设置XR会话
   * @param session XR会话
   */
  setXRSession(session: any): void {
    this._xrSession = session;

    // 查找匹配的输入源
    if (session && session.inputSources) {
      for (const inputSource of session.inputSources) {
        if (this.matchesControllerType(inputSource)) {
          this._xrInputSource = inputSource;
          break;
        }
      }
    }

    // 监听输入源变化
    if (session) {
      session.addEventListener('inputsourceschange', this.handleInputSourcesChange.bind(this));
    }
  }

  /**
   * 设置XR参考空间
   * @param referenceSpace XR参考空间
   */
  setXRReferenceSpace(referenceSpace: any): void {
    this._xrReferenceSpace = referenceSpace;
  }

  /**
   * 处理输入源变化
   * @param event 输入源变化事件
   */
  private handleInputSourcesChange(event: any): void {
    // 处理添加的输入源
    if (event.added) {
      for (const inputSource of event.added) {
        if (this.matchesControllerType(inputSource) && !this._xrInputSource) {
          this._xrInputSource = inputSource;
          break;
        }
      }
    }

    // 处理移除的输入源
    if (event.removed) {
      for (const inputSource of event.removed) {
        if (this._xrInputSource === inputSource) {
          this._xrInputSource = undefined;

          // 如果正在抓取，则释放
          if (this._isGrabbing && this._grabbedEntity) {
            this.releaseObject();
          }

          break;
        }
      }
    }
  }

  /**
   * 检查输入源是否匹配控制器类型
   * @param inputSource XR输入源
   * @returns 是否匹配
   */
  private matchesControllerType(inputSource: any): boolean {
    switch (this._controllerType) {
      case XRControllerType.LEFT:
        return inputSource.handedness === 'left';
      case XRControllerType.RIGHT:
        return inputSource.handedness === 'right';
      case XRControllerType.HEAD:
        return inputSource.targetRayMode === 'gaze';
      case XRControllerType.HAND:
        return inputSource.hand !== undefined;
      default:
        return false;
    }
  }

  /**
   * 更新组件
   * @param frame XR帧
   * @param referenceSpace XR参考空间
   */
  updateXR(frame: any, referenceSpace: any): void {
    if (!this._enabled || !this._xrInputSource) return;

    // 更新控制器姿态
    this.updateControllerPose(frame, referenceSpace);

    // 更新按钮状态
    this.updateButtonStates();

    // 如果正在抓取，更新抓取对象的位置
    if (this._isGrabbing && this._grabbedEntity) {
      this.updateGrabbedObjectPose();
    }
  }

  /**
   * 更新控制器姿态
   * @param frame XR帧
   * @param referenceSpace XR参考空间
   */
  private updateControllerPose(frame: any, referenceSpace: any): void {
    if (!this._xrInputSource || !this._controllerTransform) return;

    // 获取控制器姿态
    let pose: any;
    if (this._xrInputSource.targetRaySpace) {
      pose = frame.getPose(this._xrInputSource.targetRaySpace, referenceSpace);
    } else if (this._xrInputSource.gripSpace) {
      pose = frame.getPose(this._xrInputSource.gripSpace, referenceSpace);
    }

    if (pose) {
      // 更新控制器矩阵
      this._controllerMatrix.fromArray(pose.transform.matrix);

      // 更新控制器变换
      const position = new Vector3();
      const rotation = new Quaternion();
      const scale = new Vector3();
      this._controllerMatrix.decompose(position, rotation, scale);

      this._controllerTransform.setPosition(position);
      // 将四元数转换为欧拉角
      const euler = new Euler().setFromQuaternion(rotation);
      this._controllerTransform.setRotation(euler);
    }
  }

  /**
   * 更新按钮状态
   */
  private updateButtonStates(): void {
    if (!this._xrInputSource || !this._xrInputSource.gamepad) return;

    const gamepad = this._xrInputSource.gamepad;
    const buttons = gamepad.buttons;

    // 确保前一帧按钮状态数组长度正确
    while (this._prevButtonStates.length < buttons.length) {
      this._prevButtonStates.push(false);
    }

    // 检查抓取按钮
    if (this._grabButtonIndex < buttons.length) {
      const grabButton = buttons[this._grabButtonIndex];
      const wasPressed = this._prevButtonStates[this._grabButtonIndex];
      const isPressed = grabButton.pressed;

      // 按钮刚被按下
      if (isPressed && !wasPressed) {
        this.handleGrabButtonPressed();
      }

      // 更新前一帧状态
      this._prevButtonStates[this._grabButtonIndex] = isPressed;
    }

    // 检查释放按钮（如果与抓取按钮不同）
    if (this._releaseButtonIndex !== this._grabButtonIndex && this._releaseButtonIndex < buttons.length) {
      const releaseButton = buttons[this._releaseButtonIndex];
      const wasPressed = this._prevButtonStates[this._releaseButtonIndex];
      const isPressed = releaseButton.pressed;

      // 按钮刚被按下
      if (isPressed && !wasPressed) {
        this.handleReleaseButtonPressed();
      }

      // 更新前一帧状态
      this._prevButtonStates[this._releaseButtonIndex] = isPressed;
    }
  }

  /**
   * 处理抓取按钮按下
   */
  private handleGrabButtonPressed(): void {
    // 如果已经在抓取，则忽略
    if (this._isGrabbing) return;

    // 查找可抓取的实体
    const grabbableEntity = this.findGrabbableEntity();
    if (grabbableEntity) {
      this.grabObject(grabbableEntity);
    }
  }

  /**
   * 处理释放按钮按下
   */
  private handleReleaseButtonPressed(): void {
    // 如果没有在抓取，则忽略
    if (!this._isGrabbing || !this._grabbedEntity) return;

    this.releaseObject();
  }

  /**
   * 查找可抓取的实体
   * @returns 可抓取的实体
   */
  private findGrabbableEntity(): Entity | undefined {
    // 如果使用射线检测
    if (this._useRaycasting) {
      // TODO: 实现射线检测查找可抓取实体
      return undefined;
    } else {
      // 使用距离检测
      // TODO: 实现距离检测查找可抓取实体
      return undefined;
    }
  }

  /**
   * 抓取对象
   * @param entity 要抓取的实体
   */
  private grabObject(entity: Entity): void {
    // 确定使用哪只手
    const hand = this._controllerType === XRControllerType.LEFT ? Hand.LEFT : Hand.RIGHT;

    // 尝试抓取
    if (this._grabberComponent) {
      const success = this._grabberComponent.grab(entity, hand);

      if (success) {
        // 更新状态
        this._isGrabbing = true;
        this._grabbedEntity = entity;

        // 计算抓取偏移
        this.calculateGrabOffset(entity);

        // 触发触觉反馈
        if (this._useHapticFeedback) {
          this.triggerHapticFeedback();
        }

        // 触发事件
        this.eventEmitter.emit('grab', this.entity, entity, hand);

        // 调用回调
        if (this._onGrab) {
          this._onGrab(entity, hand);
        }

        Debug.log('XRGrabComponent', `通过XR控制器抓取实体 ${entity.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
      }
    }
  }

  /**
   * 释放对象
   */
  private releaseObject(): void {
    if (!this._isGrabbing || !this._grabbedEntity) return;

    // 确定使用哪只手
    const hand = this._controllerType === XRControllerType.LEFT ? Hand.LEFT : Hand.RIGHT;

    // 保存实体引用
    const entity = this._grabbedEntity;

    // 尝试释放
    if (this._grabberComponent) {
      const success = this._grabberComponent.release(hand);

      if (success) {
        // 更新状态
        this._isGrabbing = false;
        this._grabbedEntity = undefined;

        // 触发触觉反馈
        if (this._useHapticFeedback) {
          this.triggerHapticFeedback(0.5); // 释放时使用较弱的反馈
        }

        // 触发事件
        this.eventEmitter.emit('release', this.entity, entity, hand);

        // 调用回调
        if (this._onRelease) {
          this._onRelease(entity, hand);
        }

        Debug.log('XRGrabComponent', `通过XR控制器释放实体 ${entity.id}，使用${hand === Hand.LEFT ? '左手' : '右手'}`);
      }
    }
  }

  /**
   * 计算抓取偏移
   * @param entity 被抓取的实体
   */
  private calculateGrabOffset(entity: Entity): void {
    // 获取实体的变换
    const entityTransform = entity.getComponent<Transform>('Transform');
    if (!entityTransform || !this._controllerTransform) return;

    // 创建实体的世界矩阵
    const entityMatrix = entityTransform.getWorldMatrix();

    // 计算偏移矩阵
    this._grabOffsetMatrix.copy(this._controllerMatrix).invert().multiply(entityMatrix);
  }

  /**
   * 更新被抓取对象的姿态
   */
  private updateGrabbedObjectPose(): void {
    if (!this._isGrabbing || !this._grabbedEntity || !this._controllerTransform) return;

    // 获取实体的变换
    const entityTransform = this._grabbedEntity.getComponent<Transform>('Transform');
    if (!entityTransform) return;

    // 计算新的世界矩阵
    const newWorldMatrix = new Matrix4().copy(this._controllerMatrix).multiply(this._grabOffsetMatrix);

    // 分解为位置、旋转和缩放
    const position = new Vector3();
    const rotation = new Quaternion();
    const scale = new Vector3();
    newWorldMatrix.decompose(position, rotation, scale);

    // 更新实体的变换
    entityTransform.setWorldPosition(position);
    // 将四元数转换为欧拉角
    const euler = new Euler().setFromQuaternion(rotation);
    entityTransform.setWorldRotation(euler);
  }

  /**
   * 触发触觉反馈
   * @param intensity 强度（默认为配置的强度）
   */
  private triggerHapticFeedback(intensity?: number): void {
    if (!this._xrInputSource || !this._xrInputSource.gamepad) return;

    const gamepad = this._xrInputSource.gamepad;
    const hapticActuators = gamepad.hapticActuators || [];

    if (hapticActuators.length > 0) {
      const actuator = hapticActuators[0];
      const feedbackIntensity = intensity !== undefined ? intensity : this._hapticFeedbackIntensity;

      // 触发触觉反馈
      if (actuator.pulse) {
        actuator.pulse(feedbackIntensity, this._hapticFeedbackDuration);
      } else if (actuator.playEffect) {
        actuator.playEffect('dual-rumble', {
          duration: this._hapticFeedbackDuration,
          strongMagnitude: feedbackIntensity,
          weakMagnitude: feedbackIntensity
        });
      }
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  on(event: string, listener: EventCallback): this {
    this.eventEmitter.on(event, listener);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param listener 监听器函数
   */
  off(event: string, listener?: EventCallback): this {
    this.eventEmitter.off(event, listener);
    return this;
  }

  /**
   * 销毁组件
   */
  destroy(): void {
    // 如果正在抓取，释放对象
    if (this._isGrabbing && this._grabbedEntity) {
      this.releaseObject();
    }

    // 清空事件监听器
    this.eventEmitter.removeAllListeners();

    // 移除XR会话监听器
    if (this._xrSession) {
      this._xrSession.removeEventListener('inputsourceschange', this.handleInputSourcesChange);
    }

    // 重置状态
    this._xrSession = undefined;
    this._xrInputSource = undefined;
    this._xrReferenceSpace = undefined;
    this._isGrabbing = false;
    this._grabbedEntity = undefined;
    this._prevButtonStates = [];
  }

  /**
   * 创建组件实例
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new XRGrabComponent();
  }

  /**
   * 序列化自定义数据
   * @returns 自定义数据
   */
  protected serializeData(): any {
    return {};
  }

  /**
   * 反序列化自定义数据
   * @param data 自定义数据
   */
  protected deserializeData(data: any): void {
    // 子类可以重写此方法
  }

}

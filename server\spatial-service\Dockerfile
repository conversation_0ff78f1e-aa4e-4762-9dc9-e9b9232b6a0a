# 空间信息系统服务 Dockerfile
FROM node:18-alpine AS builder

# 安装必要的系统依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    gdal-dev \
    proj-dev \
    geos-dev

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产环境镜像
FROM node:18-alpine AS production

# 安装运行时依赖
RUN apk add --no-cache \
    gdal \
    proj \
    geos

# 创建应用用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# 设置工作目录
WORKDIR /app

# 从构建阶段复制文件
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nestjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nestjs:nodejs /app/package*.json ./

# 创建必要的目录
RUN mkdir -p uploads logs backups && \
    chown -R nestjs:nodejs uploads logs backups

# 切换到应用用户
USER nestjs

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node dist/health-check.js

# 启动命令
CMD ["node", "dist/main.js"]

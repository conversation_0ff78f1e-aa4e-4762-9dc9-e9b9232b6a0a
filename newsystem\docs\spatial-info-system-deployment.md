# DL引擎空间信息系统部署指南

## 1. 部署概述

### 1.1 系统架构
```
┌─────────────────────────────────────────────────────────────┐
│                        负载均衡器                            │
│                      (Nginx/HAProxy)                       │
├─────────────────────────────────────────────────────────────┤
│  前端应用          │  空间服务          │  监控服务           │
│  (React SPA)       │  (NestJS API)      │  (Prometheus)      │
│  Port: 3000        │  Port: 3001        │  Port: 9090        │
├─────────────────────────────────────────────────────────────┤
│  数据存储层                                                  │
│  ├── PostgreSQL + PostGIS (Port: 5432)                    │
│  ├── Redis Cache (Port: 6379)                             │
│  └── 文件存储 (本地/OSS/S3)                                │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 部署方式
- **开发环境**：本地开发部署
- **测试环境**：Docker Compose部署
- **生产环境**：Kubernetes集群部署
- **云服务**：云原生部署（阿里云/AWS/Azure）

## 2. 环境要求

### 2.1 硬件要求
| 环境 | CPU | 内存 | 存储 | 网络 |
|------|-----|------|------|------|
| 开发 | 4核 | 8GB | 100GB SSD | 100Mbps |
| 测试 | 8核 | 16GB | 200GB SSD | 1Gbps |
| 生产 | 16核 | 32GB | 500GB SSD | 10Gbps |

### 2.2 软件要求
- **操作系统**：Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Node.js**：v18.0+
- **PostgreSQL**：v15.0+
- **PostGIS**：v3.3+
- **Redis**：v7.0+
- **Docker**：v20.0+
- **Docker Compose**：v2.0+

## 3. 本地开发部署

### 3.1 环境准备
```bash
# 安装Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PostgreSQL和PostGIS
sudo apt-get update
sudo apt-get install -y postgresql-15 postgresql-15-postgis-3

# 安装Redis
sudo apt-get install -y redis-server

# 启动服务
sudo systemctl start postgresql
sudo systemctl start redis-server
sudo systemctl enable postgresql
sudo systemctl enable redis-server
```

### 3.2 数据库配置
```bash
# 创建数据库用户
sudo -u postgres createuser --interactive --pwprompt spatial_user

# 创建数据库
sudo -u postgres createdb -O spatial_user spatial_db

# 连接数据库并启用PostGIS
sudo -u postgres psql -d spatial_db -c "CREATE EXTENSION postgis;"
sudo -u postgres psql -d spatial_db -c "CREATE EXTENSION postgis_topology;"
```

### 3.3 项目部署
```bash
# 克隆项目
git clone <repository-url>
cd dl-engine-spatial

# 安装依赖 - 服务器端
cd server/spatial-service
npm install
cp .env.example .env
# 编辑.env文件配置数据库连接

# 启动服务器端
npm run start:dev

# 安装依赖 - 编辑器
cd ../../editor
npm install

# 启动编辑器
npm start
```

### 3.4 验证部署
```bash
# 检查服务状态
curl http://localhost:3001/api/v1/spatial-data/health

# 检查编辑器
curl http://localhost:3000

# 检查API文档
open http://localhost:3001/api/docs
```

## 4. Docker Compose部署

### 4.1 准备Docker环境
```bash
# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 4.2 配置文件
```bash
# 创建部署目录
mkdir -p /opt/spatial-system
cd /opt/spatial-system

# 复制配置文件
cp docker-compose.yml .
cp .env.example .env

# 编辑环境变量
vim .env
```

### 4.3 启动服务
```bash
# 构建和启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f spatial-service

# 停止服务
docker-compose down
```

### 4.4 数据初始化
```bash
# 等待数据库启动
docker-compose exec postgres pg_isready -U postgres

# 运行数据库迁移
docker-compose exec spatial-service npm run migration:run

# 导入初始数据（可选）
docker-compose exec spatial-service npm run seed:run
```

## 5. Kubernetes部署

### 5.1 准备Kubernetes集群
```bash
# 安装kubectl
curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
sudo install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl

# 验证集群连接
kubectl cluster-info
```

### 5.2 创建命名空间
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: spatial-system
  labels:
    name: spatial-system
```

```bash
kubectl apply -f namespace.yaml
```

### 5.3 配置存储
```yaml
# storage.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-pvc
  namespace: spatial-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: spatial-system
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
```

### 5.4 部署数据库
```yaml
# postgres.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
  namespace: spatial-system
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgis/postgis:15-3.3-alpine
        env:
        - name: POSTGRES_DB
          value: spatial_db
        - name: POSTGRES_USER
          value: postgres
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-storage
          mountPath: /var/lib/postgresql/data
      volumes:
      - name: postgres-storage
        persistentVolumeClaim:
          claimName: postgres-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgres-service
  namespace: spatial-system
spec:
  selector:
    app: postgres
  ports:
  - port: 5432
    targetPort: 5432
  type: ClusterIP
```

### 5.5 部署应用服务
```yaml
# spatial-service.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: spatial-service
  namespace: spatial-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: spatial-service
  template:
    metadata:
      labels:
        app: spatial-service
    spec:
      containers:
      - name: spatial-service
        image: spatial-service:latest
        env:
        - name: NODE_ENV
          value: production
        - name: DB_HOST
          value: postgres-service
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: postgres-secret
              key: password
        ports:
        - containerPort: 3001
        livenessProbe:
          httpGet:
            path: /api/v1/spatial-data/health
            port: 3001
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/spatial-data/health
            port: 3001
          initialDelaySeconds: 5
          periodSeconds: 5
        resources:
          requests:
            memory: "512Mi"
            cpu: "500m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
---
apiVersion: v1
kind: Service
metadata:
  name: spatial-service
  namespace: spatial-system
spec:
  selector:
    app: spatial-service
  ports:
  - port: 3001
    targetPort: 3001
  type: ClusterIP
```

### 5.6 配置Ingress
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: spatial-ingress
  namespace: spatial-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - spatial.yourdomain.com
    secretName: spatial-tls
  rules:
  - host: spatial.yourdomain.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: spatial-service
            port:
              number: 3001
      - path: /
        pathType: Prefix
        backend:
          service:
            name: spatial-frontend
            port:
              number: 80
```

## 6. 云服务部署

### 6.1 阿里云部署
```bash
# 安装阿里云CLI
wget https://aliyuncli.alicdn.com/aliyun-cli-linux-latest-amd64.tgz
tar xzvf aliyun-cli-linux-latest-amd64.tgz
sudo mv aliyun /usr/local/bin/

# 配置访问密钥
aliyun configure

# 创建ECS实例
aliyun ecs CreateInstance \
  --ImageId ubuntu_20_04_x64_20G_alibase_20210420.vhd \
  --InstanceType ecs.c6.large \
  --SecurityGroupId sg-xxxxxxxxx \
  --VSwitchId vsw-xxxxxxxxx
```

### 6.2 AWS部署
```bash
# 安装AWS CLI
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install

# 配置AWS凭证
aws configure

# 创建EC2实例
aws ec2 run-instances \
  --image-id ami-0abcdef1234567890 \
  --count 1 \
  --instance-type t3.medium \
  --key-name my-key-pair \
  --security-group-ids sg-903004f8 \
  --subnet-id subnet-6e7f829e
```

### 6.3 容器服务部署
```bash
# 阿里云容器服务ACK
aliyun cs CreateCluster \
  --name spatial-cluster \
  --cluster-type ManagedKubernetes \
  --region cn-hangzhou

# AWS EKS
aws eks create-cluster \
  --name spatial-cluster \
  --version 1.21 \
  --role-arn arn:aws:iam::123456789012:role/eks-service-role
```

## 7. 监控和日志

### 7.1 Prometheus监控
```yaml
# prometheus-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: prometheus-config
  namespace: spatial-system
data:
  prometheus.yml: |
    global:
      scrape_interval: 15s
    scrape_configs:
    - job_name: 'spatial-service'
      static_configs:
      - targets: ['spatial-service:3001']
      metrics_path: '/metrics'
```

### 7.2 Grafana仪表板
```bash
# 导入预配置的仪表板
kubectl apply -f grafana-dashboard-config.yaml

# 访问Grafana
kubectl port-forward svc/grafana 3000:3000 -n spatial-system
```

### 7.3 日志收集
```yaml
# fluentd-config.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluentd-config
  namespace: spatial-system
data:
  fluent.conf: |
    <source>
      @type tail
      path /var/log/containers/*spatial-service*.log
      pos_file /var/log/fluentd-spatial.log.pos
      tag kubernetes.spatial-service
      format json
    </source>
    
    <match kubernetes.spatial-service>
      @type elasticsearch
      host elasticsearch.logging.svc.cluster.local
      port 9200
      index_name spatial-logs
    </match>
```

## 8. 安全配置

### 8.1 网络安全
```bash
# 配置防火墙规则
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw deny 5432/tcp  # 仅内网访问数据库
sudo ufw enable
```

### 8.2 SSL证书
```bash
# 使用Let's Encrypt
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d spatial.yourdomain.com

# 自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 8.3 数据库安全
```sql
-- 创建只读用户
CREATE USER spatial_readonly WITH PASSWORD 'readonly_password';
GRANT CONNECT ON DATABASE spatial_db TO spatial_readonly;
GRANT USAGE ON SCHEMA public TO spatial_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO spatial_readonly;

-- 限制连接数
ALTER USER spatial_user CONNECTION LIMIT 50;
```

## 9. 备份和恢复

### 9.1 数据库备份
```bash
# 创建备份脚本
cat > /opt/backup/spatial_backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/opt/backup/spatial"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="spatial_db_${DATE}.sql"

mkdir -p $BACKUP_DIR
pg_dump -h localhost -U postgres spatial_db > $BACKUP_DIR/$BACKUP_FILE
gzip $BACKUP_DIR/$BACKUP_FILE

# 保留最近30天的备份
find $BACKUP_DIR -name "*.gz" -mtime +30 -delete
EOF

chmod +x /opt/backup/spatial_backup.sh

# 设置定时备份
crontab -e
# 添加：0 2 * * * /opt/backup/spatial_backup.sh
```

### 9.2 数据恢复
```bash
# 恢复数据库
gunzip spatial_db_20231201_020000.sql.gz
psql -h localhost -U postgres -d spatial_db < spatial_db_20231201_020000.sql
```

## 10. 性能优化

### 10.1 数据库优化
```sql
-- 优化PostgreSQL配置
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
SELECT pg_reload_conf();

-- 创建空间索引
CREATE INDEX CONCURRENTLY idx_spatial_features_geometry 
ON spatial_features USING GIST (geometry);

-- 分析表统计信息
ANALYZE spatial_features;
ANALYZE spatial_layers;
```

### 10.2 应用优化
```bash
# Node.js性能优化
export NODE_ENV=production
export NODE_OPTIONS="--max-old-space-size=4096"

# 启用集群模式
pm2 start ecosystem.config.js
```

### 10.3 缓存优化
```bash
# Redis配置优化
echo "maxmemory 2gb" >> /etc/redis/redis.conf
echo "maxmemory-policy allkeys-lru" >> /etc/redis/redis.conf
systemctl restart redis
```

## 11. 故障排除

### 11.1 常见问题
1. **服务无法启动**：检查端口占用、环境变量配置
2. **数据库连接失败**：检查网络连通性、用户权限
3. **内存不足**：调整JVM参数、增加服务器内存
4. **磁盘空间不足**：清理日志文件、扩展存储空间

### 11.2 日志分析
```bash
# 查看应用日志
docker-compose logs -f spatial-service

# 查看数据库日志
sudo tail -f /var/log/postgresql/postgresql-15-main.log

# 查看系统资源
htop
iostat -x 1
```

### 11.3 性能监控
```bash
# 监控数据库性能
SELECT * FROM pg_stat_activity WHERE state = 'active';
SELECT * FROM pg_stat_user_tables ORDER BY seq_tup_read DESC;

# 监控应用性能
curl http://localhost:3001/metrics
```

## 12. 维护计划

### 12.1 定期维护任务
- **每日**：检查服务状态、备份数据
- **每周**：更新系统补丁、清理日志
- **每月**：性能评估、容量规划
- **每季度**：安全审计、灾难恢复演练

### 12.2 升级策略
1. **测试环境验证**：先在测试环境部署新版本
2. **灰度发布**：逐步替换生产环境实例
3. **回滚准备**：保留上一版本的部署配置
4. **监控观察**：密切关注升级后的系统状态

---

*本部署指南涵盖了从开发到生产的完整部署流程，请根据实际环境选择合适的部署方式。*

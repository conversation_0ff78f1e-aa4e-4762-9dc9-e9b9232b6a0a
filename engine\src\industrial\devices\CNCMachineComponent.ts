import { Entity } from '../../core/Entity';
import { Vector3 } from '../../math/Vector3';
import { IndustrialDeviceComponent } from './IndustrialDeviceComponent';
import { DeviceType, DeviceStatus, DeviceConfig, DataQuality } from '../types';
import { Debug } from '../../utils/Debug';

/**
 * CNC机床组件
 * 实现数控机床的数字孪生功能
 */
export class CNCMachineComponent extends IndustrialDeviceComponent {
  public readonly deviceType = DeviceType.CNC_MACHINE;
  
  // CNC机床特有属性
  public spindleSpeed: number = 0;        // 主轴转速 (RPM)
  public feedRate: number = 0;            // 进给速度 (mm/min)
  public toolPosition: Vector3 = new Vector3(); // 刀具位置
  public currentTool: number = 1;         // 当前刀具号
  public workpieceCount: number = 0;      // 加工件数
  public currentProgram: string = '';     // 当前程序名
  public programProgress: number = 0;     // 程序执行进度 (0-100%)
  public estimatedCompletionTime: Date = new Date(); // 预计完成时间
  
  // 加工参数
  public cuttingSpeed: number = 0;        // 切削速度 (m/min)
  public cuttingDepth: number = 0;        // 切削深度 (mm)
  public coolantFlow: number = 0;         // 冷却液流量 (L/min)
  public coolantTemperature: number = 20; // 冷却液温度 (°C)
  
  // 机床状态
  public doorOpen: boolean = false;       // 机床门状态
  public emergencyStop: boolean = false;  // 急停状态
  public toolChanging: boolean = false;   // 换刀状态
  public workpieceLoaded: boolean = false; // 工件装载状态
  
  // 质量数据
  public dimensionalAccuracy: number = 0; // 尺寸精度 (μm)
  public surfaceRoughness: number = 0;    // 表面粗糙度 (Ra)
  public toolWear: number = 0;            // 刀具磨损 (%)
  
  // 能耗数据
  public powerConsumption: number = 0;    // 功耗 (kW)
  public spindleLoad: number = 0;         // 主轴负载 (%)
  public feedLoad: number = 0;            // 进给负载 (%)

  constructor(entity: Entity, config?: DeviceConfig) {
    super(entity, 'CNCMachine', config);

    // 初始化CNC机床特有的数据点
    this.initializeCNCDataPoints();

    Debug.log('CNCMachine', `CNC机床组件已创建: ${this.deviceName}`);
  }

  /**
   * 初始化CNC机床数据点
   */
  private initializeCNCDataPoints(): void {
    // 创建模拟数据点
    const dataPoints = [
      { tagId: 'spindle_speed', name: '主轴转速', unit: 'RPM' },
      { tagId: 'feed_rate', name: '进给速度', unit: 'mm/min' },
      { tagId: 'tool_position_x', name: 'X轴位置', unit: 'mm' },
      { tagId: 'tool_position_y', name: 'Y轴位置', unit: 'mm' },
      { tagId: 'tool_position_z', name: 'Z轴位置', unit: 'mm' },
      { tagId: 'current_tool', name: '当前刀具', unit: '' },
      { tagId: 'program_progress', name: '程序进度', unit: '%' },
      { tagId: 'cutting_speed', name: '切削速度', unit: 'm/min' },
      { tagId: 'coolant_temperature', name: '冷却液温度', unit: '°C' },
      { tagId: 'power_consumption', name: '功耗', unit: 'kW' },
      { tagId: 'spindle_load', name: '主轴负载', unit: '%' },
      { tagId: 'tool_wear', name: '刀具磨损', unit: '%' }
    ];
    
    dataPoints.forEach(dp => {
      this.updateDataPoint({
        tagId: dp.tagId,
        deviceId: this.deviceId,
        timestamp: new Date(),
        value: 0,
        quality: DataQuality.GOOD,
        metadata: { name: dp.name, unit: dp.unit }
      });
    });
  }

  /**
   * 启动加工程序
   * @param programName 程序名称
   * @param estimatedDuration 预计加工时间（分钟）
   */
  public startMachining(programName: string, estimatedDuration: number = 60): void {
    if (this.status !== DeviceStatus.RUNNING) {
      Debug.warn('CNCMachine', `CNC机床未处于运行状态，无法启动加工: ${this.deviceName}`);
      return;
    }
    
    this.currentProgram = programName;
    this.programProgress = 0;
    this.estimatedCompletionTime = new Date(Date.now() + estimatedDuration * 60000);
    
    Debug.log('CNCMachine', `启动加工程序: ${this.deviceName} - ${programName}`);
    
    // 模拟加工过程
    this.simulateMachiningProcess();
  }

  /**
   * 停止加工程序
   */
  public stopMachining(): void {
    this.currentProgram = '';
    this.programProgress = 0;
    this.spindleSpeed = 0;
    this.feedRate = 0;
    
    Debug.log('CNCMachine', `停止加工程序: ${this.deviceName}`);
  }

  /**
   * 换刀操作
   * @param toolNumber 刀具号
   */
  public changeTool(toolNumber: number): void {
    if (this.toolChanging) {
      Debug.warn('CNCMachine', `CNC机床正在换刀中: ${this.deviceName}`);
      return;
    }
    
    this.toolChanging = true;
    
    // 模拟换刀过程
    setTimeout(() => {
      this.currentTool = toolNumber;
      this.toolChanging = false;
      
      Debug.log('CNCMachine', `换刀完成: ${this.deviceName} - 刀具${toolNumber}`);
      
      this.updateDataPoint({
        tagId: 'current_tool',
        deviceId: this.deviceId,
        timestamp: new Date(),
        value: toolNumber,
        quality: DataQuality.GOOD
      });
    }, 5000); // 模拟5秒换刀时间
  }

  /**
   * 设置主轴转速
   * @param speed 转速 (RPM)
   */
  public setSpindleSpeed(speed: number): void {
    this.spindleSpeed = Math.max(0, Math.min(speed, 8000)); // 限制在0-8000 RPM
    
    this.updateDataPoint({
      tagId: 'spindle_speed',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.spindleSpeed,
      quality: DataQuality.GOOD
    });
    
    Debug.log('CNCMachine', `设置主轴转速: ${this.deviceName} - ${this.spindleSpeed} RPM`);
  }

  /**
   * 设置进给速度
   * @param rate 进给速度 (mm/min)
   */
  public setFeedRate(rate: number): void {
    this.feedRate = Math.max(0, Math.min(rate, 5000)); // 限制在0-5000 mm/min
    
    this.updateDataPoint({
      tagId: 'feed_rate',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.feedRate,
      quality: DataQuality.GOOD
    });
    
    Debug.log('CNCMachine', `设置进给速度: ${this.deviceName} - ${this.feedRate} mm/min`);
  }

  /**
   * 移动刀具到指定位置
   * @param position 目标位置
   */
  public moveToolTo(position: Vector3): void {
    this.toolPosition = position.clone();
    
    // 更新各轴位置数据点
    this.updateDataPoint({
      tagId: 'tool_position_x',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: position.x,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'tool_position_y',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: position.y,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'tool_position_z',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: position.z,
      quality: DataQuality.GOOD
    });
    
    Debug.log('CNCMachine', `刀具移动到: ${this.deviceName} - (${position.x}, ${position.y}, ${position.z})`);
  }

  /**
   * 模拟加工过程
   */
  private simulateMachiningProcess(): void {
    if (!this.currentProgram || this.programProgress >= 100) {
      return;
    }
    
    // 模拟程序进度增加
    this.programProgress += Math.random() * 2; // 每次增加0-2%
    this.programProgress = Math.min(this.programProgress, 100);
    
    // 模拟主轴转速变化
    this.spindleSpeed = 2000 + Math.random() * 3000;
    
    // 模拟进给速度变化
    this.feedRate = 500 + Math.random() * 1500;
    
    // 模拟功耗变化
    this.powerConsumption = 15 + Math.random() * 25;
    
    // 模拟主轴负载
    this.spindleLoad = 40 + Math.random() * 40;
    
    // 模拟刀具磨损增加
    this.toolWear += Math.random() * 0.1;
    
    // 更新数据点
    this.updateDataPoint({
      tagId: 'program_progress',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.programProgress,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'spindle_speed',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.spindleSpeed,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'power_consumption',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.powerConsumption,
      quality: DataQuality.GOOD
    });

    this.updateDataPoint({
      tagId: 'tool_wear',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.toolWear,
      quality: DataQuality.GOOD
    });
    
    // 检查加工完成
    if (this.programProgress >= 100) {
      this.workpieceCount++;
      this.currentProgram = '';
      this.programProgress = 0;
      
      Debug.log('CNCMachine', `加工完成: ${this.deviceName} - 工件数: ${this.workpieceCount}`);
    } else {
      // 继续模拟加工过程
      setTimeout(() => this.simulateMachiningProcess(), 2000);
    }
  }

  /**
   * 计算性能比率
   * @returns 性能比率 (0-100)
   */
  protected calculatePerformanceRatio(): number {
    // 基于主轴负载和程序进度计算性能
    const loadFactor = Math.max(0, 100 - this.spindleLoad);
    const progressFactor = this.currentProgram ? 100 : 80;
    
    return (loadFactor + progressFactor) / 2;
  }

  /**
   * 计算质量比率
   * @returns 质量比率 (0-100)
   */
  protected calculateQualityRatio(): number {
    // 基于刀具磨损和尺寸精度计算质量
    const wearFactor = Math.max(0, 100 - this.toolWear * 2);
    const accuracyFactor = this.dimensionalAccuracy > 0 ? Math.max(0, 100 - this.dimensionalAccuracy / 10) : 95;
    
    return (wearFactor + accuracyFactor) / 2;
  }

  /**
   * 子类更新方法
   */
  protected onUpdate(): void {
    // 更新冷却液温度（模拟环境影响）
    this.coolantTemperature = 20 + Math.sin(Date.now() / 60000) * 5 + Math.random() * 2;
    
    this.updateDataPoint({
      tagId: 'coolant_temperature',
      deviceId: this.deviceId,
      timestamp: new Date(),
      value: this.coolantTemperature,
      quality: DataQuality.GOOD
    });
    
    // 检查刀具磨损报警
    if (this.toolWear > 80) {
      this.triggerAlarm('tool_wear_high');
    }
    
    // 检查主轴负载报警
    if (this.spindleLoad > 90) {
      this.triggerAlarm('spindle_overload');
    }
  }

  /**
   * 获取CNC机床特有信息
   * @returns CNC机床信息
   */
  public getCNCInfo(): any {
    return {
      ...this.getDeviceInfo(),
      spindleSpeed: this.spindleSpeed,
      feedRate: this.feedRate,
      toolPosition: this.toolPosition,
      currentTool: this.currentTool,
      workpieceCount: this.workpieceCount,
      currentProgram: this.currentProgram,
      programProgress: this.programProgress,
      toolWear: this.toolWear,
      powerConsumption: this.powerConsumption,
      spindleLoad: this.spindleLoad
    };
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): CNCMachineComponent {
    return new CNCMachineComponent(this.entity!);
  }
}

-- 空间信息系统数据库初始化脚本

-- 创建PostGIS扩展
CREATE EXTENSION IF NOT EXISTS postgis;
CREATE EXTENSION IF NOT EXISTS postgis_topology;
CREATE EXTENSION IF NOT EXISTS fuzzystrmatch;
CREATE EXTENSION IF NOT EXISTS postgis_tiger_geocoder;

-- 创建UUID扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建btree_gist扩展（用于空间索引优化）
CREATE EXTENSION IF NOT EXISTS btree_gist;

-- 验证PostGIS安装
SELECT PostGIS_Version();

-- 创建空间参考系统（如果需要自定义）
-- 示例：创建中国2000坐标系
-- INSERT INTO spatial_ref_sys (srid, auth_name, auth_srid, proj4text, srtext) 
-- VALUES (4490, 'EPSG', 4490, '+proj=longlat +ellps=GRS80 +no_defs', 'GEOGCS["China Geodetic Coordinate System 2000",DATUM["China_2000",SPHEROID["GRS 1980",6378137,298.257222101,AUTHORITY["EPSG","7019"]],AUTHORITY["EPSG","6480"]],PRIMEM["Greenwich",0,AUTHORITY["EPSG","8901"]],UNIT["degree",0.0174532925199433,AUTHORITY["EPSG","9122"]],AUTHORITY["EPSG","4490"]]');

-- 创建空间索引函数
CREATE OR REPLACE FUNCTION create_spatial_index(table_name text, column_name text)
RETURNS void AS $$
BEGIN
    EXECUTE format('CREATE INDEX IF NOT EXISTS idx_%s_%s_gist ON %s USING GIST (%s)', 
                   table_name, column_name, table_name, column_name);
END;
$$ LANGUAGE plpgsql;

-- 创建几何验证函数
CREATE OR REPLACE FUNCTION validate_geometry(geom geometry)
RETURNS boolean AS $$
BEGIN
    RETURN ST_IsValid(geom) AND NOT ST_IsEmpty(geom);
END;
$$ LANGUAGE plpgsql;

-- 创建坐标转换函数
CREATE OR REPLACE FUNCTION transform_geometry(geom geometry, target_srid integer)
RETURNS geometry AS $$
BEGIN
    IF ST_SRID(geom) = target_srid THEN
        RETURN geom;
    ELSE
        RETURN ST_Transform(geom, target_srid);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建缓冲区分析函数
CREATE OR REPLACE FUNCTION buffer_analysis(geom geometry, distance double precision, segments integer DEFAULT 32)
RETURNS geometry AS $$
BEGIN
    RETURN ST_Buffer(geom, distance, segments);
END;
$$ LANGUAGE plpgsql;

-- 创建面积计算函数（考虑投影）
CREATE OR REPLACE FUNCTION calculate_area(geom geometry)
RETURNS double precision AS $$
DECLARE
    projected_geom geometry;
BEGIN
    -- 如果是地理坐标系，转换为适合的投影坐标系计算面积
    IF ST_SRID(geom) = 4326 THEN
        -- 使用Web Mercator投影
        projected_geom := ST_Transform(geom, 3857);
        RETURN ST_Area(projected_geom);
    ELSE
        RETURN ST_Area(geom);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建长度计算函数（考虑投影）
CREATE OR REPLACE FUNCTION calculate_length(geom geometry)
RETURNS double precision AS $$
DECLARE
    projected_geom geometry;
BEGIN
    -- 如果是地理坐标系，转换为适合的投影坐标系计算长度
    IF ST_SRID(geom) = 4326 THEN
        -- 使用Web Mercator投影
        projected_geom := ST_Transform(geom, 3857);
        RETURN ST_Length(projected_geom);
    ELSE
        RETURN ST_Length(geom);
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 创建空间关系查询函数
CREATE OR REPLACE FUNCTION spatial_relationship(geom1 geometry, geom2 geometry, relationship text)
RETURNS boolean AS $$
BEGIN
    CASE relationship
        WHEN 'intersects' THEN RETURN ST_Intersects(geom1, geom2);
        WHEN 'contains' THEN RETURN ST_Contains(geom1, geom2);
        WHEN 'within' THEN RETURN ST_Within(geom1, geom2);
        WHEN 'touches' THEN RETURN ST_Touches(geom1, geom2);
        WHEN 'crosses' THEN RETURN ST_Crosses(geom1, geom2);
        WHEN 'overlaps' THEN RETURN ST_Overlaps(geom1, geom2);
        WHEN 'disjoint' THEN RETURN ST_Disjoint(geom1, geom2);
        ELSE RETURN FALSE;
    END CASE;
END;
$$ LANGUAGE plpgsql;

-- 创建几何简化函数
CREATE OR REPLACE FUNCTION simplify_geometry(geom geometry, tolerance double precision)
RETURNS geometry AS $$
BEGIN
    RETURN ST_Simplify(geom, tolerance);
END;
$$ LANGUAGE plpgsql;

-- 创建边界框查询函数
CREATE OR REPLACE FUNCTION bbox_query(geom geometry, bbox_geom geometry)
RETURNS boolean AS $$
BEGIN
    RETURN ST_Intersects(geom, bbox_geom);
END;
$$ LANGUAGE plpgsql;

-- 创建距离查询函数
CREATE OR REPLACE FUNCTION distance_query(geom1 geometry, geom2 geometry, max_distance double precision)
RETURNS boolean AS $$
BEGIN
    RETURN ST_DWithin(geom1, geom2, max_distance);
END;
$$ LANGUAGE plpgsql;

-- 创建聚合几何函数
CREATE OR REPLACE FUNCTION aggregate_geometries(geom_array geometry[])
RETURNS geometry AS $$
DECLARE
    result_geom geometry;
    geom geometry;
BEGIN
    result_geom := NULL;
    
    FOREACH geom IN ARRAY geom_array
    LOOP
        IF result_geom IS NULL THEN
            result_geom := geom;
        ELSE
            result_geom := ST_Union(result_geom, geom);
        END IF;
    END LOOP;
    
    RETURN result_geom;
END;
$$ LANGUAGE plpgsql;

-- 创建性能优化视图
CREATE OR REPLACE VIEW spatial_features_summary AS
SELECT 
    sf.layer_id,
    sl.name as layer_name,
    sf.feature_type,
    COUNT(*) as feature_count,
    ST_Extent(sf.geometry) as extent,
    SUM(calculate_area(sf.geometry)) as total_area,
    SUM(calculate_length(sf.geometry)) as total_length
FROM spatial_features sf
JOIN spatial_layers sl ON sf.layer_id = sl.id
GROUP BY sf.layer_id, sl.name, sf.feature_type;

-- 创建空间统计函数
CREATE OR REPLACE FUNCTION get_spatial_statistics(layer_id_param uuid)
RETURNS TABLE(
    feature_count bigint,
    total_area double precision,
    total_length double precision,
    extent geometry,
    centroid geometry
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(*)::bigint,
        SUM(calculate_area(geometry)),
        SUM(calculate_length(geometry)),
        ST_Extent(geometry),
        ST_Centroid(ST_Collect(geometry))
    FROM spatial_features 
    WHERE spatial_features.layer_id = layer_id_param;
END;
$$ LANGUAGE plpgsql;

-- 创建触发器函数：自动更新几何统计信息
CREATE OR REPLACE FUNCTION update_geometry_stats()
RETURNS TRIGGER AS $$
BEGIN
    -- 更新要素的面积和长度
    IF TG_OP = 'INSERT' OR TG_OP = 'UPDATE' THEN
        NEW.metadata = COALESCE(NEW.metadata, '{}'::jsonb);
        
        -- 计算面积（仅对面要素）
        IF NEW.feature_type IN ('Polygon', 'MultiPolygon') THEN
            NEW.metadata = NEW.metadata || jsonb_build_object('area', calculate_area(NEW.geometry));
        END IF;
        
        -- 计算长度（仅对线要素）
        IF NEW.feature_type IN ('LineString', 'MultiLineString') THEN
            NEW.metadata = NEW.metadata || jsonb_build_object('length', calculate_length(NEW.geometry));
        END IF;
        
        -- 计算边界框
        NEW.metadata = NEW.metadata || jsonb_build_object('bbox', ST_Extent(NEW.geometry));
        
        RETURN NEW;
    END IF;
    
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- 设置数据库参数优化
ALTER SYSTEM SET shared_preload_libraries = 'pg_stat_statements';
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;

-- 重新加载配置
SELECT pg_reload_conf();

-- 输出初始化完成信息
DO $$
BEGIN
    RAISE NOTICE '空间信息系统数据库初始化完成！';
    RAISE NOTICE 'PostGIS版本: %', PostGIS_Version();
    RAISE NOTICE '数据库已准备就绪，可以开始使用空间功能。';
END $$;

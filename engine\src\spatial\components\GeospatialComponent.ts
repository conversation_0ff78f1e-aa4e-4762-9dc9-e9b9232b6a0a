/**
 * 地理空间组件
 * 为实体添加地理空间属性和功能
 */
import * as THREE from 'three';
import { Component } from '../../core/Component';
import { 
  GeographicCoordinate, 
  ProjectedCoordinate, 
  CoordinateSystemType, 
  CoordinateTransformer,
  CoordinateSystemManager 
} from '../coordinate/CoordinateSystem';

/**
 * 空间参考信息接口
 */
export interface SpatialReference {
  epsgCode?: string;      // EPSG代码
  proj4String?: string;   // Proj4投影字符串
  wkt?: string;          // Well-Known Text
  name?: string;         // 坐标系名称
}

/**
 * 地理空间属性接口
 */
export interface GeospatialProperties {
  [key: string]: any;
  name?: string;
  description?: string;
  category?: string;
  tags?: string[];
}

/**
 * 地理空间组件类
 */
export class GeospatialComponent extends Component {
  static readonly type = 'Geospatial';
  
  // 地理坐标
  public geographicCoordinate: GeographicCoordinate;
  
  // 投影坐标
  public projectedCoordinate: ProjectedCoordinate;
  
  // 坐标系类型
  public coordinateSystem: CoordinateSystemType;
  
  // 空间参考信息
  public spatialReference: SpatialReference;
  
  // 地理空间属性
  public properties: GeospatialProperties;
  
  // 几何类型
  public geometryType: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';
  
  // 几何数据（GeoJSON格式）
  public geometry: any;
  
  // 样式信息
  public style: {
    color?: string;
    fillColor?: string;
    weight?: number;
    opacity?: number;
    fillOpacity?: number;
    radius?: number;
  };
  
  // 是否可见
  public visible: boolean = true;
  
  // 是否可选择
  public selectable: boolean = true;
  
  // 是否可编辑
  public editable: boolean = true;
  
  // 缓存的Three.js对象
  private _threeObject: THREE.Object3D | null = null;
  
  constructor(
    geographicCoord: GeographicCoordinate,
    coordinateSystem: CoordinateSystemType = CoordinateSystemType.WGS84,
    geometryType: 'Point' | 'LineString' | 'Polygon' = 'Point'
  ) {
    super(GeospatialComponent.type);
    
    this.geographicCoordinate = { ...geographicCoord };
    this.coordinateSystem = coordinateSystem;
    this.geometryType = geometryType;
    this.properties = {};
    this.style = {};
    this.spatialReference = {};
    
    // 初始化几何数据
    this.initializeGeometry();
    
    // 更新投影坐标
    this.updateProjectedCoordinate();
  }
  
  /**
   * 初始化几何数据
   */
  private initializeGeometry(): void {
    switch (this.geometryType) {
      case 'Point':
        this.geometry = {
          type: 'Point',
          coordinates: [this.geographicCoordinate.longitude, this.geographicCoordinate.latitude]
        };
        break;
      case 'LineString':
        this.geometry = {
          type: 'LineString',
          coordinates: [[this.geographicCoordinate.longitude, this.geographicCoordinate.latitude]]
        };
        break;
      case 'Polygon':
        this.geometry = {
          type: 'Polygon',
          coordinates: [[[this.geographicCoordinate.longitude, this.geographicCoordinate.latitude]]]
        };
        break;
    }
  }
  
  /**
   * 更新投影坐标
   */
  private updateProjectedCoordinate(): void {
    // 转换为Web墨卡托投影（用于Three.js渲染）
    const wgs84Coord = CoordinateSystemManager.getInstance().transform(
      this.geographicCoordinate,
      this.coordinateSystem,
      CoordinateSystemType.WGS84
    );
    
    this.projectedCoordinate = CoordinateTransformer.geographicToWebMercator(wgs84Coord);
  }
  
  /**
   * 设置地理坐标
   */
  setGeographicCoordinate(coord: GeographicCoordinate): void {
    this.geographicCoordinate = { ...coord };
    this.updateProjectedCoordinate();
    this.updateGeometry();
    this.invalidateThreeObject();
    
    // 发出坐标变更事件
    this.emit('coordinateChanged', {
      geographic: this.geographicCoordinate,
      projected: this.projectedCoordinate
    });
  }
  
  /**
   * 获取地理坐标
   */
  getGeographicCoordinate(): GeographicCoordinate {
    return { ...this.geographicCoordinate };
  }
  
  /**
   * 设置投影坐标
   */
  setProjectedCoordinate(coord: ProjectedCoordinate): void {
    this.projectedCoordinate = { ...coord };
    
    // 反向计算地理坐标
    const wgs84Coord = CoordinateTransformer.webMercatorToGeographic(coord);
    this.geographicCoordinate = CoordinateSystemManager.getInstance().transform(
      wgs84Coord,
      CoordinateSystemType.WGS84,
      this.coordinateSystem
    );
    
    this.updateGeometry();
    this.invalidateThreeObject();
    
    this.emit('coordinateChanged', {
      geographic: this.geographicCoordinate,
      projected: this.projectedCoordinate
    });
  }
  
  /**
   * 获取投影坐标
   */
  getProjectedCoordinate(): ProjectedCoordinate {
    return { ...this.projectedCoordinate };
  }
  
  /**
   * 转换坐标系
   */
  transformTo(targetSystem: CoordinateSystemType): void {
    if (this.coordinateSystem === targetSystem) {
      return;
    }
    
    this.geographicCoordinate = CoordinateSystemManager.getInstance().transform(
      this.geographicCoordinate,
      this.coordinateSystem,
      targetSystem
    );
    
    this.coordinateSystem = targetSystem;
    this.updateProjectedCoordinate();
    this.updateGeometry();
    this.invalidateThreeObject();
    
    this.emit('coordinateSystemChanged', {
      newSystem: targetSystem,
      coordinate: this.geographicCoordinate
    });
  }
  
  /**
   * 更新几何数据
   */
  private updateGeometry(): void {
    if (this.geometry && this.geometry.coordinates) {
      switch (this.geometryType) {
        case 'Point':
          this.geometry.coordinates = [
            this.geographicCoordinate.longitude, 
            this.geographicCoordinate.latitude
          ];
          break;
        case 'LineString':
          if (this.geometry.coordinates.length === 1) {
            this.geometry.coordinates[0] = [
              this.geographicCoordinate.longitude, 
              this.geographicCoordinate.latitude
            ];
          }
          break;
        case 'Polygon':
          if (this.geometry.coordinates[0].length === 1) {
            this.geometry.coordinates[0][0] = [
              this.geographicCoordinate.longitude, 
              this.geographicCoordinate.latitude
            ];
          }
          break;
      }
    }
  }
  
  /**
   * 设置几何数据
   */
  setGeometry(geometry: any): void {
    this.geometry = { ...geometry };
    
    // 从几何数据中提取坐标
    if (geometry.coordinates) {
      let coords: number[];
      
      switch (geometry.type) {
        case 'Point':
          coords = geometry.coordinates;
          break;
        case 'LineString':
          coords = geometry.coordinates[0] || geometry.coordinates;
          break;
        case 'Polygon':
          coords = geometry.coordinates[0][0] || geometry.coordinates[0] || geometry.coordinates;
          break;
        default:
          coords = [0, 0];
      }
      
      if (coords.length >= 2) {
        this.geographicCoordinate = {
          longitude: coords[0],
          latitude: coords[1],
          altitude: coords[2]
        };
        this.updateProjectedCoordinate();
      }
    }
    
    this.invalidateThreeObject();
    this.emit('geometryChanged', geometry);
  }
  
  /**
   * 获取几何数据
   */
  getGeometry(): any {
    return { ...this.geometry };
  }
  
  /**
   * 设置属性
   */
  setProperties(properties: GeospatialProperties): void {
    this.properties = { ...properties };
    this.emit('propertiesChanged', this.properties);
  }
  
  /**
   * 获取属性
   */
  getProperties(): GeospatialProperties {
    return { ...this.properties };
  }
  
  /**
   * 设置样式
   */
  setStyle(style: any): void {
    this.style = { ...this.style, ...style };
    this.invalidateThreeObject();
    this.emit('styleChanged', this.style);
  }
  
  /**
   * 获取样式
   */
  getStyle(): any {
    return { ...this.style };
  }
  
  /**
   * 获取Three.js对象
   */
  getThreeObject(): THREE.Object3D {
    if (!this._threeObject) {
      this._threeObject = this.createThreeObject();
    }
    return this._threeObject;
  }
  
  /**
   * 创建Three.js对象
   */
  private createThreeObject(): THREE.Object3D {
    const group = new THREE.Group();
    group.name = `Geospatial_${this.entity?.id || 'unknown'}`;
    
    // 根据几何类型创建不同的Three.js对象
    switch (this.geometryType) {
      case 'Point':
        group.add(this.createPointObject());
        break;
      case 'LineString':
        group.add(this.createLineObject());
        break;
      case 'Polygon':
        group.add(this.createPolygonObject());
        break;
    }
    
    // 设置位置
    group.position.set(
      this.projectedCoordinate.x,
      this.projectedCoordinate.z || 0,
      -this.projectedCoordinate.y // Three.js使用右手坐标系
    );
    
    return group;
  }
  
  /**
   * 创建点对象
   */
  private createPointObject(): THREE.Object3D {
    const geometry = new THREE.SphereGeometry(this.style.radius || 10, 8, 6);
    const material = new THREE.MeshBasicMaterial({
      color: this.style.color || '#ff0000',
      opacity: this.style.opacity || 1,
      transparent: (this.style.opacity || 1) < 1
    });
    
    return new THREE.Mesh(geometry, material);
  }
  
  /**
   * 创建线对象
   */
  private createLineObject(): THREE.Object3D {
    const points: THREE.Vector3[] = [];
    
    if (this.geometry.coordinates) {
      this.geometry.coordinates.forEach((coord: number[]) => {
        const projCoord = CoordinateTransformer.geographicToWebMercator({
          longitude: coord[0],
          latitude: coord[1],
          altitude: coord[2] || 0
        });
        points.push(new THREE.Vector3(projCoord.x, projCoord.z || 0, -projCoord.y));
      });
    }
    
    const geometry = new THREE.BufferGeometry().setFromPoints(points);
    const material = new THREE.LineBasicMaterial({
      color: this.style.color || '#0000ff',
      linewidth: this.style.weight || 1,
      opacity: this.style.opacity || 1,
      transparent: (this.style.opacity || 1) < 1
    });
    
    return new THREE.Line(geometry, material);
  }
  
  /**
   * 创建多边形对象
   */
  private createPolygonObject(): THREE.Object3D {
    // 简化实现，创建一个平面多边形
    const shape = new THREE.Shape();
    
    if (this.geometry.coordinates && this.geometry.coordinates[0]) {
      const coords = this.geometry.coordinates[0];
      coords.forEach((coord: number[], index: number) => {
        const projCoord = CoordinateTransformer.geographicToWebMercator({
          longitude: coord[0],
          latitude: coord[1]
        });
        
        if (index === 0) {
          shape.moveTo(projCoord.x, projCoord.y);
        } else {
          shape.lineTo(projCoord.x, projCoord.y);
        }
      });
    }
    
    const geometry = new THREE.ShapeGeometry(shape);
    const material = new THREE.MeshBasicMaterial({
      color: this.style.fillColor || '#00ff00',
      opacity: this.style.fillOpacity || 0.5,
      transparent: true,
      side: THREE.DoubleSide
    });
    
    return new THREE.Mesh(geometry, material);
  }
  
  /**
   * 使Three.js对象失效
   */
  private invalidateThreeObject(): void {
    if (this._threeObject) {
      this._threeObject.clear();
      this._threeObject = null;
    }
  }
  
  /**
   * 计算到另一个地理空间组件的距离
   */
  distanceTo(other: GeospatialComponent): number {
    const coord1 = this.geographicCoordinate;
    const coord2 = other.geographicCoordinate;
    
    // 使用Haversine公式计算球面距离
    const R = 6371000; // 地球半径（米）
    const φ1 = coord1.latitude * Math.PI / 180;
    const φ2 = coord2.latitude * Math.PI / 180;
    const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;
    const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;
    
    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    
    return R * c;
  }
  
  /**
   * 序列化为GeoJSON
   */
  toGeoJSON(): any {
    return {
      type: 'Feature',
      geometry: this.geometry,
      properties: this.properties
    };
  }
  
  /**
   * 从GeoJSON反序列化
   */
  static fromGeoJSON(geoJSON: any, coordinateSystem: CoordinateSystemType = CoordinateSystemType.WGS84): GeospatialComponent {
    const geometry = geoJSON.geometry;
    const properties = geoJSON.properties || {};
    
    let coord: GeographicCoordinate;
    let geometryType: 'Point' | 'LineString' | 'Polygon';
    
    switch (geometry.type) {
      case 'Point':
        coord = {
          longitude: geometry.coordinates[0],
          latitude: geometry.coordinates[1],
          altitude: geometry.coordinates[2]
        };
        geometryType = 'Point';
        break;
      case 'LineString':
        coord = {
          longitude: geometry.coordinates[0][0],
          latitude: geometry.coordinates[0][1],
          altitude: geometry.coordinates[0][2]
        };
        geometryType = 'LineString';
        break;
      case 'Polygon':
        coord = {
          longitude: geometry.coordinates[0][0][0],
          latitude: geometry.coordinates[0][0][1],
          altitude: geometry.coordinates[0][0][2]
        };
        geometryType = 'Polygon';
        break;
      default:
        throw new Error(`不支持的几何类型: ${geometry.type}`);
    }
    
    const component = new GeospatialComponent(coord, coordinateSystem, geometryType);
    component.setGeometry(geometry);
    component.setProperties(properties);
    
    return component;
  }
}

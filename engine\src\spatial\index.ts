/**
 * 空间信息系统模块
 * 
 * 该模块为DL引擎提供完整的地理空间信息系统功能，包括：
 * - 地理坐标系统和坐标转换
 * - 地理空间组件和数据管理
 * - 瓦片地图系统和地图服务
 * - 空间分析引擎和算法
 * - 空间查询和索引
 * 
 * 主要功能：
 * - 支持多种坐标系统（WGS84、GCJ02、BD09、UTM、WebMercator）
 * - 地图瓦片加载和渲染
 * - 矢量数据管理和可视化
 * - 空间分析（缓冲区、相交、联合、差异等）
 * - 空间查询和关系判断
 * - 地理编码和反向地理编码
 * 
 * 使用示例：
 * ```typescript
 * import { 
 *   SpatialInfoSystem, 
 *   GeospatialComponent, 
 *   TileMapSystem,
 *   SpatialAnalysisEngine,
 *   CoordinateSystemType 
 * } from '../spatial';
 * 
 * // 创建空间信息系统
 * const spatialSystem = new SpatialInfoSystem(world, {
 *   defaultCoordinateSystem: CoordinateSystemType.WGS84,
 *   enableTileMap: true,
 *   enableSpatialAnalysis: true
 * });
 * 
 * // 添加地理空间组件
 * const geoComponent = new GeospatialComponent(
 *   { longitude: 116.404, latitude: 39.915 }, // 北京坐标
 *   CoordinateSystemType.WGS84,
 *   'Point'
 * );
 * entity.addComponent(geoComponent);
 * 
 * // 设置地图视图
 * spatialSystem.setMapView(
 *   { longitude: 116.404, latitude: 39.915 },
 *   10
 * );
 * 
 * // 执行空间分析
 * const bufferResult = spatialSystem.buffer(geoComponent.getGeometry(), {
 *   distance: 1000,
 *   unit: 'meters'
 * });
 * ```
 */

// 坐标系统模块
export * from './coordinate/CoordinateSystem';

// 地理空间组件
export * from './components/GeospatialComponent';

// 地图系统
export * from './map/TileMapSystem';

// 空间分析
export * from './analysis/SpatialAnalysisEngine';

// 空间信息系统主类
export { SpatialInfoSystem } from './SpatialInfoSystem';

// 空间系统
export { SpatialSystem } from './systems/SpatialSystem';

// 类型定义
export interface SpatialInfoSystemConfig {
  defaultCoordinateSystem?: import('./coordinate/CoordinateSystem').CoordinateSystemType;
  enableTileMap?: boolean;
  enableSpatialAnalysis?: boolean;
  tileProvider?: import('./map/TileMapSystem').ITileProvider;
  maxCacheSize?: number;
  enableDebug?: boolean;
}

// 常用坐标系统预设
export const COORDINATE_SYSTEMS = {
  // 世界大地坐标系
  WGS84: {
    name: 'WGS84',
    epsgCode: 'EPSG:4326',
    description: '世界大地坐标系，GPS使用的坐标系'
  },
  
  // 火星坐标系（中国偏移坐标系）
  GCJ02: {
    name: 'GCJ02',
    epsgCode: 'EPSG:4490',
    description: '火星坐标系，中国国家测绘局制定的坐标系'
  },
  
  // 百度坐标系
  BD09: {
    name: 'BD09',
    description: '百度坐标系，百度地图使用的坐标系'
  },
  
  // Web墨卡托投影
  WEB_MERCATOR: {
    name: 'WebMercator',
    epsgCode: 'EPSG:3857',
    description: 'Web墨卡托投影，网络地图常用投影'
  }
};

// 地图服务提供者预设
export const MAP_PROVIDERS = {
  OSM: 'OpenStreetMap',
  SATELLITE: 'Satellite',
  TERRAIN: 'Terrain'
};

// 空间分析类型
export const SPATIAL_ANALYSIS_TYPES = {
  BUFFER: 'buffer',
  INTERSECTION: 'intersection',
  UNION: 'union',
  DIFFERENCE: 'difference',
  DISTANCE: 'distance',
  AREA: 'area',
  LENGTH: 'length'
};

// 空间关系类型
export const SPATIAL_RELATIONS = {
  INTERSECTS: 'intersects',
  CONTAINS: 'contains',
  WITHIN: 'within',
  TOUCHES: 'touches',
  CROSSES: 'crosses',
  OVERLAPS: 'overlaps',
  DISJOINT: 'disjoint'
};

// 几何类型
export const GEOMETRY_TYPES = {
  POINT: 'Point',
  LINE_STRING: 'LineString',
  POLYGON: 'Polygon',
  MULTI_POINT: 'MultiPoint',
  MULTI_LINE_STRING: 'MultiLineString',
  MULTI_POLYGON: 'MultiPolygon',
  GEOMETRY_COLLECTION: 'GeometryCollection'
};

// 工具函数
export const SpatialUtils = {
  /**
   * 验证地理坐标是否有效
   */
  isValidGeographicCoordinate(coord: import('./coordinate/CoordinateSystem').GeographicCoordinate): boolean {
    return coord.longitude >= -180 && coord.longitude <= 180 &&
           coord.latitude >= -90 && coord.latitude <= 90;
  },
  
  /**
   * 验证GeoJSON几何是否有效
   */
  isValidGeoJSONGeometry(geometry: any): boolean {
    if (!geometry || !geometry.type || !geometry.coordinates) {
      return false;
    }
    
    const validTypes = Object.values(GEOMETRY_TYPES);
    return validTypes.includes(geometry.type);
  },
  
  /**
   * 计算边界框
   */
  calculateBounds(coordinates: number[][]): [number, number, number, number] {
    if (coordinates.length === 0) {
      return [0, 0, 0, 0];
    }
    
    let minX = coordinates[0][0];
    let minY = coordinates[0][1];
    let maxX = coordinates[0][0];
    let maxY = coordinates[0][1];
    
    for (const coord of coordinates) {
      minX = Math.min(minX, coord[0]);
      minY = Math.min(minY, coord[1]);
      maxX = Math.max(maxX, coord[0]);
      maxY = Math.max(maxY, coord[1]);
    }
    
    return [minX, minY, maxX, maxY];
  },
  
  /**
   * 格式化坐标显示
   */
  formatCoordinate(coord: import('./coordinate/CoordinateSystem').GeographicCoordinate, precision: number = 6): string {
    const lon = coord.longitude.toFixed(precision);
    const lat = coord.latitude.toFixed(precision);
    return `${lon}, ${lat}`;
  },
  
  /**
   * 解析坐标字符串
   */
  parseCoordinate(coordString: string): import('./coordinate/CoordinateSystem').GeographicCoordinate | null {
    const parts = coordString.split(',').map(s => s.trim());
    if (parts.length >= 2) {
      const longitude = parseFloat(parts[0]);
      const latitude = parseFloat(parts[1]);
      const altitude = parts.length > 2 ? parseFloat(parts[2]) : undefined;
      
      if (!isNaN(longitude) && !isNaN(latitude)) {
        return { longitude, latitude, altitude };
      }
    }
    return null;
  },
  
  /**
   * 生成随机坐标（用于测试）
   */
  generateRandomCoordinate(bounds?: [number, number, number, number]): import('./coordinate/CoordinateSystem').GeographicCoordinate {
    const [minLon, minLat, maxLon, maxLat] = bounds || [-180, -90, 180, 90];
    
    return {
      longitude: minLon + Math.random() * (maxLon - minLon),
      latitude: minLat + Math.random() * (maxLat - minLat)
    };
  }
};

// 错误类型
export class SpatialError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'SpatialError';
  }
}

export class CoordinateError extends SpatialError {
  constructor(message: string) {
    super(message, 'COORDINATE_ERROR');
    this.name = 'CoordinateError';
  }
}

export class GeometryError extends SpatialError {
  constructor(message: string) {
    super(message, 'GEOMETRY_ERROR');
    this.name = 'GeometryError';
  }
}

export class AnalysisError extends SpatialError {
  constructor(message: string) {
    super(message, 'ANALYSIS_ERROR');
    this.name = 'AnalysisError';
  }
}

// 版本信息
export const SPATIAL_VERSION = '1.0.0';

// 模块信息
export const SPATIAL_MODULE_INFO = {
  name: 'DL引擎空间信息系统',
  version: SPATIAL_VERSION,
  description: '为DL引擎提供完整的地理空间信息系统功能',
  author: 'DL引擎团队',
  license: 'MIT',
  features: [
    '多坐标系统支持',
    '地图瓦片服务',
    '矢量数据管理',
    '空间分析算法',
    '空间查询功能',
    '地理编码服务'
  ]
};

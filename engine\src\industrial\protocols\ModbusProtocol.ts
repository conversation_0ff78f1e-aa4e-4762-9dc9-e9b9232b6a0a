import { 
  IndustrialProtocol, 
  ProtocolType, 
  DeviceConfig, 
  DeviceConnection, 
  IndustrialDataPoint,
  DeviceStatus,
  DataQuality,
  DataType 
} from '../types';
import { Debug } from '../../utils/Debug';

/**
 * Modbus协议实现
 * 支持Modbus TCP和Modbus RTU协议
 */
export class ModbusProtocol implements IndustrialProtocol {
  public readonly type = ProtocolType.MODBUS_TCP;
  
  private connections: Map<string, ModbusConnection> = new Map();
  private subscriptions: Map<string, ModbusSubscription> = new Map();
  private subscriptionCounter = 0;

  /**
   * 连接设备
   * @param config 设备配置
   * @returns 设备连接信息
   */
  public async connect(config: DeviceConfig): Promise<DeviceConnection> {
    try {
      Debug.log('ModbusProtocol', `正在连接Modbus设备: ${config.name}`);
      
      // 创建Modbus连接
      const modbusConnection = await this.createModbusConnection(config);
      
      const connection: DeviceConnection = {
        deviceId: config.id,
        protocol: this.type,
        status: DeviceStatus.ONLINE,
        lastConnected: new Date(),
        errorCount: 0
      };
      
      this.connections.set(config.id, modbusConnection);
      
      Debug.log('ModbusProtocol', `Modbus设备连接成功: ${config.name}`);
      return connection;
      
    } catch (error) {
      Debug.error('ModbusProtocol', `Modbus设备连接失败: ${config.name}`, error);
      throw error;
    }
  }

  /**
   * 断开设备连接
   * @param deviceId 设备ID
   */
  public async disconnect(deviceId: string): Promise<void> {
    const connection = this.connections.get(deviceId);
    if (connection) {
      try {
        await connection.close();
        this.connections.delete(deviceId);
        Debug.log('ModbusProtocol', `Modbus设备断开连接: ${deviceId}`);
      } catch (error) {
        Debug.error('ModbusProtocol', `断开Modbus设备连接失败: ${deviceId}`, error);
      }
    }
  }

  /**
   * 读取标签数据
   * @param deviceId 设备ID
   * @param tagId 标签ID
   * @returns 工业数据点
   */
  public async readTag(deviceId: string, tagId: string): Promise<IndustrialDataPoint> {
    const connection = this.connections.get(deviceId);
    if (!connection) {
      throw new Error(`Modbus设备未连接: ${deviceId}`);
    }

    try {
      // 解析标签地址
      const tagInfo = this.parseTagAddress(tagId);
      
      // 读取数据
      const rawValue = await this.readModbusData(connection, tagInfo);
      
      // 转换数据类型
      const value = this.convertDataType(rawValue, tagInfo.dataType);
      
      return {
        tagId,
        deviceId,
        timestamp: new Date(),
        value,
        quality: DataQuality.GOOD
      };
      
    } catch (error) {
      Debug.error('ModbusProtocol', `读取Modbus标签失败: ${deviceId}:${tagId}`, error);
      
      return {
        tagId,
        deviceId,
        timestamp: new Date(),
        value: null,
        quality: DataQuality.BAD,
        metadata: { error: error.message }
      };
    }
  }

  /**
   * 写入标签数据
   * @param deviceId 设备ID
   * @param tagId 标签ID
   * @param value 写入值
   * @returns 是否写入成功
   */
  public async writeTag(deviceId: string, tagId: string, value: any): Promise<boolean> {
    const connection = this.connections.get(deviceId);
    if (!connection) {
      throw new Error(`Modbus设备未连接: ${deviceId}`);
    }

    try {
      // 解析标签地址
      const tagInfo = this.parseTagAddress(tagId);
      
      // 转换数据类型
      const rawValue = this.convertToRawValue(value, tagInfo.dataType);
      
      // 写入数据
      await this.writeModbusData(connection, tagInfo, rawValue);
      
      Debug.log('ModbusProtocol', `写入Modbus标签成功: ${deviceId}:${tagId} = ${value}`);
      return true;
      
    } catch (error) {
      Debug.error('ModbusProtocol', `写入Modbus标签失败: ${deviceId}:${tagId}`, error);
      return false;
    }
  }

  /**
   * 批量读取标签
   * @param deviceId 设备ID
   * @param tagIds 标签ID列表
   * @returns 工业数据点数组
   */
  public async readMultipleTags(deviceId: string, tagIds: string[]): Promise<IndustrialDataPoint[]> {
    const connection = this.connections.get(deviceId);
    if (!connection) {
      throw new Error(`Modbus设备未连接: ${deviceId}`);
    }

    const results: IndustrialDataPoint[] = [];
    
    // 按功能码和地址范围分组优化读取
    const groupedTags = this.groupTagsForOptimizedReading(tagIds);
    
    for (const group of groupedTags) {
      try {
        const groupResults = await this.readTagGroup(connection, deviceId, group);
        results.push(...groupResults);
      } catch (error) {
        Debug.error('ModbusProtocol', `批量读取Modbus标签组失败: ${deviceId}`, error);
        
        // 为失败的标签创建错误数据点
        group.tagIds.forEach(tagId => {
          results.push({
            tagId,
            deviceId,
            timestamp: new Date(),
            value: null,
            quality: DataQuality.BAD,
            metadata: { error: error.message }
          });
        });
      }
    }
    
    return results;
  }

  /**
   * 订阅数据
   * @param deviceId 设备ID
   * @param tagIds 标签ID列表
   * @param callback 数据回调函数
   * @returns 订阅ID
   */
  public async subscribe(
    deviceId: string, 
    tagIds: string[], 
    callback: (data: IndustrialDataPoint[]) => void
  ): Promise<string> {
    const subscriptionId = `modbus_sub_${++this.subscriptionCounter}`;
    
    const subscription: ModbusSubscription = {
      id: subscriptionId,
      deviceId,
      tagIds,
      callback,
      interval: 1000, // 默认1秒轮询
      timer: null
    };
    
    // 启动轮询
    subscription.timer = setInterval(async () => {
      try {
        const data = await this.readMultipleTags(deviceId, tagIds);
        callback(data);
      } catch (error) {
        Debug.error('ModbusProtocol', `Modbus订阅数据读取失败: ${subscriptionId}`, error);
      }
    }, subscription.interval);
    
    this.subscriptions.set(subscriptionId, subscription);
    
    Debug.log('ModbusProtocol', `Modbus数据订阅创建: ${subscriptionId}`);
    return subscriptionId;
  }

  /**
   * 取消订阅
   * @param subscriptionId 订阅ID
   */
  public async unsubscribe(subscriptionId: string): Promise<void> {
    const subscription = this.subscriptions.get(subscriptionId);
    if (subscription) {
      if (subscription.timer) {
        clearInterval(subscription.timer);
      }
      this.subscriptions.delete(subscriptionId);
      Debug.log('ModbusProtocol', `Modbus数据订阅取消: ${subscriptionId}`);
    }
  }

  /**
   * 创建Modbus连接
   * @param config 设备配置
   * @returns Modbus连接
   */
  private async createModbusConnection(config: DeviceConfig): Promise<ModbusConnection> {
    // 在浏览器环境中，我们模拟Modbus连接
    // 实际实现中，这里会使用真实的Modbus库
    
    const connection: ModbusConnection = {
      deviceId: config.id,
      host: config.address,
      port: config.port || 502,
      unitId: config.parameters.unitId || 1,
      timeout: config.parameters.timeout || 5000,
      connected: true,
      
      async close() {
        this.connected = false;
        Debug.log('ModbusProtocol', `Modbus连接已关闭: ${this.deviceId}`);
      }
    };
    
    // 模拟连接延迟
    await new Promise(resolve => setTimeout(resolve, 100));
    
    return connection;
  }

  /**
   * 解析标签地址
   * @param tagId 标签ID
   * @returns 标签信息
   */
  private parseTagAddress(tagId: string): ModbusTagInfo {
    // 标签格式: FC:ADDRESS:TYPE
    // 例如: 3:40001:INT16, 1:10001:BOOLEAN
    const parts = tagId.split(':');
    if (parts.length !== 3) {
      throw new Error(`无效的Modbus标签格式: ${tagId}`);
    }
    
    const functionCode = parseInt(parts[0]);
    const address = parseInt(parts[1]);
    const dataType = parts[2] as DataType;
    
    return {
      functionCode,
      address,
      dataType,
      length: this.getDataTypeLength(dataType)
    };
  }

  /**
   * 获取数据类型长度
   * @param dataType 数据类型
   * @returns 字节长度
   */
  private getDataTypeLength(dataType: DataType): number {
    switch (dataType) {
      case DataType.BOOLEAN:
        return 1;
      case DataType.INT16:
        return 1;
      case DataType.INT32:
      case DataType.FLOAT:
        return 2;
      case DataType.DOUBLE:
        return 4;
      default:
        return 1;
    }
  }

  /**
   * 读取Modbus数据
   * @param connection Modbus连接
   * @param tagInfo 标签信息
   * @returns 原始数据
   */
  private async readModbusData(connection: ModbusConnection, tagInfo: ModbusTagInfo): Promise<any> {
    // 模拟Modbus数据读取
    // 实际实现中，这里会调用真实的Modbus读取函数
    
    switch (tagInfo.functionCode) {
      case 1: // Read Coils
      case 2: // Read Discrete Inputs
        return Math.random() > 0.5; // 模拟布尔值
      
      case 3: // Read Holding Registers
      case 4: // Read Input Registers
        if (tagInfo.dataType === DataType.FLOAT) {
          return Math.random() * 100; // 模拟浮点数
        } else {
          return Math.floor(Math.random() * 65536); // 模拟整数
        }
      
      default:
        throw new Error(`不支持的Modbus功能码: ${tagInfo.functionCode}`);
    }
  }

  /**
   * 写入Modbus数据
   * @param connection Modbus连接
   * @param tagInfo 标签信息
   * @param value 写入值
   */
  private async writeModbusData(connection: ModbusConnection, tagInfo: ModbusTagInfo, value: any): Promise<void> {
    // 模拟Modbus数据写入
    // 实际实现中，这里会调用真实的Modbus写入函数
    
    Debug.log('ModbusProtocol', `模拟写入Modbus数据: FC${tagInfo.functionCode}, 地址${tagInfo.address}, 值${value}`);
    
    // 模拟写入延迟
    await new Promise(resolve => setTimeout(resolve, 50));
  }

  /**
   * 转换数据类型
   * @param rawValue 原始值
   * @param dataType 目标数据类型
   * @returns 转换后的值
   */
  private convertDataType(rawValue: any, dataType: DataType): any {
    switch (dataType) {
      case DataType.BOOLEAN:
        return Boolean(rawValue);
      case DataType.INT16:
        return Math.floor(Number(rawValue));
      case DataType.INT32:
        return Math.floor(Number(rawValue));
      case DataType.FLOAT:
        return Number(rawValue);
      case DataType.DOUBLE:
        return Number(rawValue);
      case DataType.STRING:
        return String(rawValue);
      default:
        return rawValue;
    }
  }

  /**
   * 转换为原始值
   * @param value 输入值
   * @param dataType 数据类型
   * @returns 原始值
   */
  private convertToRawValue(value: any, dataType: DataType): any {
    return this.convertDataType(value, dataType);
  }

  /**
   * 分组标签以优化读取
   * @param tagIds 标签ID列表
   * @returns 分组后的标签
   */
  private groupTagsForOptimizedReading(tagIds: string[]): ModbusTagGroup[] {
    const groups: ModbusTagGroup[] = [];
    
    // 简化实现：每个标签单独一组
    // 实际实现中，应该按功能码和连续地址分组
    tagIds.forEach(tagId => {
      groups.push({
        functionCode: 3, // 默认使用功能码3
        startAddress: 0,
        length: 1,
        tagIds: [tagId]
      });
    });
    
    return groups;
  }

  /**
   * 读取标签组
   * @param connection Modbus连接
   * @param deviceId 设备ID
   * @param group 标签组
   * @returns 数据点数组
   */
  private async readTagGroup(
    connection: ModbusConnection, 
    deviceId: string, 
    group: ModbusTagGroup
  ): Promise<IndustrialDataPoint[]> {
    const results: IndustrialDataPoint[] = [];
    
    // 简化实现：逐个读取标签
    for (const tagId of group.tagIds) {
      const dataPoint = await this.readTag(deviceId, tagId);
      results.push(dataPoint);
    }
    
    return results;
  }
}

/**
 * Modbus连接接口
 */
interface ModbusConnection {
  deviceId: string;
  host: string;
  port: number;
  unitId: number;
  timeout: number;
  connected: boolean;
  close(): Promise<void>;
}

/**
 * Modbus标签信息
 */
interface ModbusTagInfo {
  functionCode: number;
  address: number;
  dataType: DataType;
  length: number;
}

/**
 * Modbus标签组
 */
interface ModbusTagGroup {
  functionCode: number;
  startAddress: number;
  length: number;
  tagIds: string[];
}

/**
 * Modbus订阅
 */
interface ModbusSubscription {
  id: string;
  deviceId: string;
  tagIds: string[];
  callback: (data: IndustrialDataPoint[]) => void;
  interval: number;
  timer: NodeJS.Timeout | null;
}

/**
 * 空间系统
 * 管理具有地理空间组件的实体，处理空间相关的更新和渲染
 */
import * as THREE from 'three';
import { System } from '../../core/System';
import { World } from '../../core/World';
import { Entity } from '../../core/Entity';
import { GeospatialComponent } from '../components/GeospatialComponent';
import { Transform } from '../../scene/Transform';

/**
 * 空间系统配置接口
 */
export interface SpatialSystemConfig {
  enableAutoSync?: boolean;        // 是否自动同步Transform和地理坐标
  enableSpatialIndex?: boolean;    // 是否启用空间索引
  enableDebugVisualization?: boolean; // 是否启用调试可视化
  spatialIndexCellSize?: number;   // 空间索引单元格大小
}

/**
 * 空间索引单元格
 */
interface SpatialCell {
  entities: Set<Entity>;
  bounds: {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  };
}

/**
 * 空间系统类
 */
export class SpatialSystem extends System {
  private config: SpatialSystemConfig;
  private geospatialEntities: Map<string, Entity> = new Map();
  private spatialIndex: Map<string, SpatialCell> = new Map();
  private debugGroup?: THREE.Group;
  
  constructor(world: World, config: SpatialSystemConfig = {}) {
    super(world);
    
    this.config = {
      enableAutoSync: true,
      enableSpatialIndex: true,
      enableDebugVisualization: false,
      spatialIndexCellSize: 1000, // 1km
      ...config
    };
    
    this.initializeSystem();
  }
  
  /**
   * 初始化系统
   */
  private initializeSystem(): void {
    if (this.config.enableDebugVisualization) {
      this.debugGroup = new THREE.Group();
      this.debugGroup.name = 'SpatialSystemDebug';
      
      if (this.world.scene) {
        this.world.scene.add(this.debugGroup);
      }
    }
  }
  
  /**
   * 系统更新
   */
  update(deltaTime: number): void {
    this.updateGeospatialEntities(deltaTime);
    
    if (this.config.enableSpatialIndex) {
      this.updateSpatialIndex();
    }
    
    if (this.config.enableDebugVisualization) {
      this.updateDebugVisualization();
    }
  }
  
  /**
   * 更新地理空间实体
   */
  private updateGeospatialEntities(deltaTime: number): void {
    for (const entity of this.geospatialEntities.values()) {
      const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
      const transform = entity.getComponent('Transform') as Transform;
      
      if (!geoComponent || !transform) {
        continue;
      }
      
      // 自动同步Transform和地理坐标
      if (this.config.enableAutoSync) {
        this.syncTransformWithGeospatial(transform, geoComponent);
      }
      
      // 更新Three.js对象
      this.updateThreeObject(geoComponent);
    }
  }
  
  /**
   * 同步Transform和地理空间组件
   */
  private syncTransformWithGeospatial(transform: Transform, geoComponent: GeospatialComponent): void {
    const projectedCoord = geoComponent.getProjectedCoordinate();
    const currentPosition = transform.getPosition();
    
    // 检查是否需要更新
    const threshold = 0.1; // 0.1米的阈值
    const distance = Math.sqrt(
      Math.pow(currentPosition.x - projectedCoord.x, 2) +
      Math.pow(currentPosition.z - (-projectedCoord.y), 2)
    );
    
    if (distance > threshold) {
      // 更新Transform位置
      transform.setPosition(
        projectedCoord.x,
        projectedCoord.z || 0,
        -projectedCoord.y // Three.js使用右手坐标系
      );
    }
  }
  
  /**
   * 更新Three.js对象
   */
  private updateThreeObject(geoComponent: GeospatialComponent): void {
    const threeObject = geoComponent.getThreeObject();
    
    if (threeObject && this.world.scene) {
      // 确保对象在场景中
      if (!threeObject.parent) {
        this.world.scene.add(threeObject);
      }
      
      // 更新可见性
      threeObject.visible = geoComponent.visible;
    }
  }
  
  /**
   * 更新空间索引
   */
  private updateSpatialIndex(): void {
    // 清空现有索引
    this.spatialIndex.clear();
    
    // 重建索引
    for (const entity of this.geospatialEntities.values()) {
      const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
      if (!geoComponent) continue;
      
      const cellKey = this.getCellKey(geoComponent);
      
      if (!this.spatialIndex.has(cellKey)) {
        this.spatialIndex.set(cellKey, {
          entities: new Set(),
          bounds: this.calculateCellBounds(cellKey)
        });
      }
      
      this.spatialIndex.get(cellKey)!.entities.add(entity);
    }
  }
  
  /**
   * 获取空间索引单元格键值
   */
  private getCellKey(geoComponent: GeospatialComponent): string {
    const coord = geoComponent.getProjectedCoordinate();
    const cellSize = this.config.spatialIndexCellSize!;
    
    const cellX = Math.floor(coord.x / cellSize);
    const cellY = Math.floor(coord.y / cellSize);
    
    return `${cellX},${cellY}`;
  }
  
  /**
   * 计算单元格边界
   */
  private calculateCellBounds(cellKey: string): {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  } {
    const [cellX, cellY] = cellKey.split(',').map(Number);
    const cellSize = this.config.spatialIndexCellSize!;
    
    return {
      minX: cellX * cellSize,
      maxX: (cellX + 1) * cellSize,
      minY: cellY * cellSize,
      maxY: (cellY + 1) * cellSize
    };
  }
  
  /**
   * 更新调试可视化
   */
  private updateDebugVisualization(): void {
    if (!this.debugGroup) return;
    
    // 清空现有调试对象
    this.debugGroup.clear();
    
    // 可视化空间索引网格
    if (this.config.enableSpatialIndex) {
      this.visualizeSpatialIndex();
    }
    
    // 可视化地理空间实体
    this.visualizeGeospatialEntities();
  }
  
  /**
   * 可视化空间索引
   */
  private visualizeSpatialIndex(): void {
    if (!this.debugGroup) return;
    
    for (const [cellKey, cell] of this.spatialIndex) {
      if (cell.entities.size === 0) continue;
      
      // 创建网格线框
      const geometry = new THREE.PlaneGeometry(
        cell.bounds.maxX - cell.bounds.minX,
        cell.bounds.maxY - cell.bounds.minY
      );
      
      const edges = new THREE.EdgesGeometry(geometry);
      const material = new THREE.LineBasicMaterial({ 
        color: 0x00ff00,
        transparent: true,
        opacity: 0.3
      });
      
      const wireframe = new THREE.LineSegments(edges, material);
      wireframe.position.set(
        (cell.bounds.minX + cell.bounds.maxX) / 2,
        0,
        -((cell.bounds.minY + cell.bounds.maxY) / 2)
      );
      wireframe.rotation.x = -Math.PI / 2;
      
      this.debugGroup.add(wireframe);
      
      // 添加文本标签（实体数量）
      // 注意：这里需要文本渲染功能，暂时省略
    }
  }
  
  /**
   * 可视化地理空间实体
   */
  private visualizeGeospatialEntities(): void {
    if (!this.debugGroup) return;
    
    for (const entity of this.geospatialEntities.values()) {
      const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
      if (!geoComponent) continue;
      
      const coord = geoComponent.getProjectedCoordinate();
      
      // 创建坐标轴
      const axesHelper = new THREE.AxesHelper(50);
      axesHelper.position.set(coord.x, coord.z || 0, -coord.y);
      this.debugGroup.add(axesHelper);
      
      // 创建标签点
      const geometry = new THREE.SphereGeometry(5, 8, 6);
      const material = new THREE.MeshBasicMaterial({ 
        color: 0xff0000,
        transparent: true,
        opacity: 0.7
      });
      
      const marker = new THREE.Mesh(geometry, material);
      marker.position.set(coord.x, coord.z || 0, -coord.y);
      this.debugGroup.add(marker);
    }
  }
  
  /**
   * 添加地理空间实体
   */
  addGeospatialEntity(entity: Entity): void {
    const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
    if (!geoComponent) {
      console.warn('SpatialSystem: 实体缺少地理空间组件', entity.id);
      return;
    }
    
    this.geospatialEntities.set(entity.id, entity);
    
    // 监听组件变化
    geoComponent.on('coordinateChanged', () => {
      this.onEntityCoordinateChanged(entity);
    });
    
    geoComponent.on('geometryChanged', () => {
      this.onEntityGeometryChanged(entity);
    });
  }
  
  /**
   * 移除地理空间实体
   */
  removeGeospatialEntity(entity: Entity): void {
    const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
    
    if (geoComponent) {
      // 从场景中移除Three.js对象
      const threeObject = geoComponent.getThreeObject();
      if (threeObject && threeObject.parent) {
        threeObject.parent.remove(threeObject);
      }
      
      // 移除事件监听
      geoComponent.removeAllListeners();
    }
    
    this.geospatialEntities.delete(entity.id);
  }
  
  /**
   * 实体坐标变化处理
   */
  private onEntityCoordinateChanged(entity: Entity): void {
    // 更新空间索引（如果启用）
    if (this.config.enableSpatialIndex) {
      // 空间索引会在下次update时重建
    }
    
    // 同步Transform组件
    if (this.config.enableAutoSync) {
      const transform = entity.getComponent('Transform') as Transform;
      const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
      
      if (transform && geoComponent) {
        this.syncTransformWithGeospatial(transform, geoComponent);
      }
    }
  }
  
  /**
   * 实体几何变化处理
   */
  private onEntityGeometryChanged(entity: Entity): void {
    const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
    if (geoComponent) {
      // 重新创建Three.js对象
      this.updateThreeObject(geoComponent);
    }
  }
  
  /**
   * 空间查询：获取指定区域内的实体
   */
  queryEntitiesInBounds(
    minX: number, 
    minY: number, 
    maxX: number, 
    maxY: number
  ): Entity[] {
    const results: Entity[] = [];
    
    if (this.config.enableSpatialIndex) {
      // 使用空间索引查询
      const cellSize = this.config.spatialIndexCellSize!;
      const startCellX = Math.floor(minX / cellSize);
      const endCellX = Math.floor(maxX / cellSize);
      const startCellY = Math.floor(minY / cellSize);
      const endCellY = Math.floor(maxY / cellSize);
      
      for (let cellX = startCellX; cellX <= endCellX; cellX++) {
        for (let cellY = startCellY; cellY <= endCellY; cellY++) {
          const cellKey = `${cellX},${cellY}`;
          const cell = this.spatialIndex.get(cellKey);
          
          if (cell) {
            for (const entity of cell.entities) {
              const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
              if (geoComponent) {
                const coord = geoComponent.getProjectedCoordinate();
                if (coord.x >= minX && coord.x <= maxX && 
                    coord.y >= minY && coord.y <= maxY) {
                  results.push(entity);
                }
              }
            }
          }
        }
      }
    } else {
      // 线性查询
      for (const entity of this.geospatialEntities.values()) {
        const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
        if (geoComponent) {
          const coord = geoComponent.getProjectedCoordinate();
          if (coord.x >= minX && coord.x <= maxX && 
              coord.y >= minY && coord.y <= maxY) {
            results.push(entity);
          }
        }
      }
    }
    
    return results;
  }
  
  /**
   * 空间查询：获取指定点附近的实体
   */
  queryEntitiesNearPoint(
    x: number, 
    y: number, 
    radius: number
  ): Entity[] {
    return this.queryEntitiesInBounds(
      x - radius, 
      y - radius, 
      x + radius, 
      y + radius
    ).filter(entity => {
      const geoComponent = entity.getComponent('Geospatial') as GeospatialComponent;
      if (geoComponent) {
        const coord = geoComponent.getProjectedCoordinate();
        const distance = Math.sqrt(
          Math.pow(coord.x - x, 2) + Math.pow(coord.y - y, 2)
        );
        return distance <= radius;
      }
      return false;
    });
  }
  
  /**
   * 获取所有地理空间实体
   */
  getAllGeospatialEntities(): Entity[] {
    return Array.from(this.geospatialEntities.values());
  }
  
  /**
   * 获取系统统计信息
   */
  getSystemStats(): {
    entityCount: number;
    spatialIndexEnabled: boolean;
    spatialIndexCells: number;
    debugVisualizationEnabled: boolean;
  } {
    return {
      entityCount: this.geospatialEntities.size,
      spatialIndexEnabled: this.config.enableSpatialIndex!,
      spatialIndexCells: this.spatialIndex.size,
      debugVisualizationEnabled: this.config.enableDebugVisualization!
    };
  }
  
  /**
   * 销毁系统
   */
  destroy(): void {
    // 清理所有实体
    for (const entity of this.geospatialEntities.values()) {
      this.removeGeospatialEntity(entity);
    }
    
    this.geospatialEntities.clear();
    this.spatialIndex.clear();
    
    // 移除调试可视化
    if (this.debugGroup && this.debugGroup.parent) {
      this.debugGroup.parent.remove(this.debugGroup);
    }
    
    super.destroy();
  }
}

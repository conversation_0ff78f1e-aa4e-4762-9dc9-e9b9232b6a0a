/**
 * 审计日志记录器
 * 记录和管理系统审计日志
 */
import { EventEmitter } from '../utils/EventEmitter';
import { SecurityConfig, AuditLogEntry, AuditEventType } from './types';

export interface LogOptions {
  eventType: AuditEventType;
  userId?: string;
  username?: string;
  resource?: string;
  action?: string;
  result: 'success' | 'failure' | 'error';
  details?: Record<string, any>;
  ipAddress?: string;
  userAgent?: string;
  sessionId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

export class AuditLogger extends EventEmitter {
  private config: SecurityConfig;
  private logs: AuditLogEntry[] = [];
  private logBuffer: AuditLogEntry[] = [];
  private flushTimer?: NodeJS.Timeout;
  private isDestroyed: boolean = false;
  private logCounter: number = 0;

  constructor(config: SecurityConfig) {
    super();
    this.config = config;
    this.startPeriodicFlush();
  }

  /**
   * 记录审计日志
   * @param options 日志选项
   */
  public async log(options: LogOptions): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    if (!this.config.enableAuditLogging) {
      return;
    }

    try {
      const entry: AuditLogEntry = {
        id: this.generateLogId(),
        timestamp: new Date(),
        eventType: options.eventType,
        userId: options.userId,
        username: options.username,
        resource: options.resource,
        action: options.action,
        result: options.result,
        details: options.details,
        ipAddress: options.ipAddress,
        userAgent: options.userAgent,
        sessionId: options.sessionId,
        severity: options.severity
      };

      // 添加到缓冲区
      this.logBuffer.push(entry);

      // 如果是高严重性事件，立即刷新
      if (options.severity === 'critical' || options.severity === 'high') {
        await this.flush();
      }

      this.emit('log-created', entry);

    } catch (error) {
      this.emit('log-error', { error, options });
    }
  }

  /**
   * 查询审计日志
   * @param filters 过滤条件
   * @param limit 限制数量
   * @param offset 偏移量
   * @returns 日志条目
   */
  public query(filters: Partial<AuditLogEntry> = {}, limit: number = 100, offset: number = 0): AuditLogEntry[] {
    if (this.isDestroyed) {
      return [];
    }

    let filteredLogs = this.logs;

    // 应用过滤器
    if (Object.keys(filters).length > 0) {
      filteredLogs = this.logs.filter(log => {
        return Object.entries(filters).every(([key, value]) => {
          if (value === undefined) return true;
          
          const logValue = (log as any)[key];
          
          if (key === 'timestamp' && value instanceof Date) {
            return logValue.getTime() >= value.getTime();
          }
          
          return logValue === value;
        });
      });
    }

    // 按时间倒序排序
    filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // 应用分页
    return filteredLogs.slice(offset, offset + limit);
  }

  /**
   * 按时间范围查询
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @param eventTypes 事件类型
   * @returns 日志条目
   */
  public queryByTimeRange(
    startTime: Date, 
    endTime: Date, 
    eventTypes?: AuditEventType[]
  ): AuditLogEntry[] {
    if (this.isDestroyed) {
      return [];
    }

    return this.logs.filter(log => {
      const timeMatch = log.timestamp >= startTime && log.timestamp <= endTime;
      const typeMatch = !eventTypes || eventTypes.includes(log.eventType);
      return timeMatch && typeMatch;
    });
  }

  /**
   * 按用户查询
   * @param userId 用户ID
   * @param limit 限制数量
   * @returns 日志条目
   */
  public queryByUser(userId: string, limit: number = 100): AuditLogEntry[] {
    if (this.isDestroyed) {
      return [];
    }

    return this.logs
      .filter(log => log.userId === userId)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * 按严重性查询
   * @param severity 严重性级别
   * @param limit 限制数量
   * @returns 日志条目
   */
  public queryBySeverity(severity: 'low' | 'medium' | 'high' | 'critical', limit: number = 100): AuditLogEntry[] {
    if (this.isDestroyed) {
      return [];
    }

    return this.logs
      .filter(log => log.severity === severity)
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  /**
   * 获取统计信息
   * @param timeRange 时间范围（小时）
   * @returns 统计信息
   */
  public getStats(timeRange: number = 24): any {
    if (this.isDestroyed) {
      return null;
    }

    const cutoffTime = new Date(Date.now() - timeRange * 60 * 60 * 1000);
    const recentLogs = this.logs.filter(log => log.timestamp >= cutoffTime);

    const stats = {
      totalLogs: this.logs.length,
      recentLogs: recentLogs.length,
      timeRange: `${timeRange} hours`,
      eventTypes: {} as Record<string, number>,
      severities: {} as Record<string, number>,
      results: {} as Record<string, number>,
      topUsers: {} as Record<string, number>,
      topResources: {} as Record<string, number>
    };

    // 统计事件类型
    recentLogs.forEach(log => {
      stats.eventTypes[log.eventType] = (stats.eventTypes[log.eventType] || 0) + 1;
      stats.severities[log.severity] = (stats.severities[log.severity] || 0) + 1;
      stats.results[log.result] = (stats.results[log.result] || 0) + 1;
      
      if (log.username) {
        stats.topUsers[log.username] = (stats.topUsers[log.username] || 0) + 1;
      }
      
      if (log.resource) {
        stats.topResources[log.resource] = (stats.topResources[log.resource] || 0) + 1;
      }
    });

    return stats;
  }

  /**
   * 导出日志
   * @param format 导出格式
   * @param filters 过滤条件
   * @returns 导出数据
   */
  public export(format: 'json' | 'csv' | 'xml' = 'json', filters: Partial<AuditLogEntry> = {}): string {
    if (this.isDestroyed) {
      return '';
    }

    const logs = this.query(filters, Number.MAX_SAFE_INTEGER);

    switch (format) {
      case 'json':
        return JSON.stringify(logs, null, 2);
      case 'csv':
        return this.exportToCsv(logs);
      case 'xml':
        return this.exportToXml(logs);
      default:
        throw new Error(`Unsupported export format: ${format}`);
    }
  }

  /**
   * 清理过期日志
   */
  public async cleanup(): Promise<number> {
    if (this.isDestroyed) {
      return 0;
    }

    const cutoffTime = new Date(Date.now() - this.config.auditRetentionDays * 24 * 60 * 60 * 1000);
    const initialCount = this.logs.length;
    
    this.logs = this.logs.filter(log => log.timestamp >= cutoffTime);
    
    const removedCount = initialCount - this.logs.length;
    
    if (removedCount > 0) {
      this.emit('logs-cleaned', { removedCount, cutoffTime });
    }
    
    return removedCount;
  }

  /**
   * 刷新缓冲区
   */
  public async flush(): Promise<void> {
    if (this.isDestroyed || this.logBuffer.length === 0) {
      return;
    }

    try {
      // 将缓冲区的日志添加到主日志数组
      this.logs.push(...this.logBuffer);
      const flushedCount = this.logBuffer.length;
      this.logBuffer = [];

      // 保持日志数组大小
      const maxLogs = 100000; // 最多保留10万条日志
      if (this.logs.length > maxLogs) {
        this.logs = this.logs.slice(-maxLogs);
      }

      this.emit('logs-flushed', { count: flushedCount });

    } catch (error) {
      this.emit('flush-error', { error });
    }
  }

  /**
   * 销毁审计日志记录器
   */
  public async destroy(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    // 刷新剩余的日志
    await this.flush();

    // 清除定时器
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }

    // 清理数据
    this.logs = [];
    this.logBuffer = [];
    
    this.removeAllListeners();
    this.isDestroyed = true;
  }

  /**
   * 生成日志ID
   */
  private generateLogId(): string {
    this.logCounter++;
    return `audit_${Date.now()}_${this.logCounter}`;
  }

  /**
   * 启动定期刷新
   */
  private startPeriodicFlush(): void {
    this.flushTimer = setInterval(async () => {
      await this.flush();
    }, 5000); // 每5秒刷新一次
  }

  /**
   * 导出为CSV格式
   */
  private exportToCsv(logs: AuditLogEntry[]): string {
    if (logs.length === 0) {
      return '';
    }

    const headers = Object.keys(logs[0]).join(',');
    const rows = logs.map(log => {
      return Object.values(log).map(value => {
        if (value instanceof Date) {
          return value.toISOString();
        }
        if (typeof value === 'object') {
          return JSON.stringify(value);
        }
        return String(value);
      }).join(',');
    });

    return [headers, ...rows].join('\n');
  }

  /**
   * 导出为XML格式
   */
  private exportToXml(logs: AuditLogEntry[]): string {
    const xmlLogs = logs.map(log => {
      const fields = Object.entries(log).map(([key, value]) => {
        let xmlValue = value;
        if (value instanceof Date) {
          xmlValue = value.toISOString();
        } else if (typeof value === 'object') {
          xmlValue = JSON.stringify(value);
        }
        return `    <${key}>${xmlValue}</${key}>`;
      }).join('\n');
      
      return `  <log>\n${fields}\n  </log>`;
    }).join('\n');

    return `<?xml version="1.0" encoding="UTF-8"?>\n<auditLogs>\n${xmlLogs}\n</auditLogs>`;
  }
}

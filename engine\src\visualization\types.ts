/**
 * 可视化类型定义
 */

/**
 * 图表类型
 */
export enum ChartType {
  LINE = 'line',
  BAR = 'bar',
  PIE = 'pie',
  SCATTER = 'scatter',
  AREA = 'area',
  HISTOGRAM = 'histogram',
  HEATMAP = 'heatmap',
  GAUGE = 'gauge'
}

/**
 * 数据点
 */
export interface DataPoint {
  x: number | string | Date;
  y: number;
  label?: string;
  color?: string;
  metadata?: Record<string, any>;
}

/**
 * 数据系列
 */
export interface DataSeries {
  name: string;
  data: DataPoint[];
  color?: string;
  type?: ChartType;
  visible?: boolean;
  metadata?: Record<string, any>;
}

/**
 * 图表配置
 */
export interface ChartConfig {
  type: ChartType;
  title?: string;
  width?: number;
  height?: number;
  backgroundColor?: string;
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  animation?: boolean;
  responsive?: boolean;
  axes?: {
    x?: AxisConfig;
    y?: AxisConfig;
  };
  colors?: string[];
  theme?: 'light' | 'dark' | 'auto';
}

/**
 * 坐标轴配置
 */
export interface AxisConfig {
  title?: string;
  min?: number;
  max?: number;
  type?: 'linear' | 'logarithmic' | 'category' | 'time';
  format?: string;
  showGrid?: boolean;
  gridColor?: string;
  tickCount?: number;
  tickFormat?: string;
}

/**
 * 图表事件
 */
export interface ChartEvent {
  type: 'click' | 'hover' | 'zoom' | 'pan' | 'select';
  data?: any;
  point?: DataPoint;
  series?: DataSeries;
  position?: { x: number; y: number };
  timestamp: Date;
}

/**
 * 可视化选项
 */
export interface VisualizationOptions {
  container: HTMLElement | string;
  config: ChartConfig;
  data: DataSeries[];
  interactive?: boolean;
  realtime?: boolean;
  updateInterval?: number;
  maxDataPoints?: number;
}

/**
 * 渲染器接口
 */
export interface Renderer {
  render(options: VisualizationOptions): void;
  update(data: DataSeries[]): void;
  resize(width: number, height: number): void;
  destroy(): void;
  on(event: string, callback: (event: ChartEvent) => void): void;
  off(event: string, callback?: (event: ChartEvent) => void): void;
}

/**
 * 图表样式
 */
export interface ChartStyle {
  colors: string[];
  fonts: {
    title: string;
    axis: string;
    legend: string;
    tooltip: string;
  };
  sizes: {
    title: number;
    axis: number;
    legend: number;
    tooltip: number;
  };
  spacing: {
    margin: { top: number; right: number; bottom: number; left: number };
    padding: { top: number; right: number; bottom: number; left: number };
  };
}

/**
 * 动画配置
 */
export interface AnimationConfig {
  duration: number;
  easing: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
  loop?: boolean;
}

/**
 * 交互配置
 */
export interface InteractionConfig {
  zoom?: boolean;
  pan?: boolean;
  select?: boolean;
  hover?: boolean;
  click?: boolean;
  brush?: boolean;
  crosshair?: boolean;
}

/**
 * 导出选项
 */
export interface ExportOptions {
  format: 'png' | 'jpg' | 'svg' | 'pdf';
  width?: number;
  height?: number;
  quality?: number;
  backgroundColor?: string;
}

/**
 * 实时数据配置
 */
export interface RealtimeConfig {
  enabled: boolean;
  updateInterval: number;
  maxDataPoints: number;
  bufferSize: number;
  smoothing?: boolean;
  aggregation?: 'none' | 'average' | 'sum' | 'min' | 'max';
}

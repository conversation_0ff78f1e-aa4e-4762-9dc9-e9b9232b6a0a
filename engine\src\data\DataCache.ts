/**
 * 数据缓存
 * 提供高性能的内存缓存功能
 */
import { EventEmitter } from '../utils/EventEmitter';

export interface CacheOptions {
  maxSize?: number;
  ttl?: number; // Time to live in milliseconds
  checkInterval?: number; // Cleanup interval in milliseconds
}

export interface CacheItem<T = any> {
  key: string;
  value: T;
  timestamp: number;
  accessCount: number;
  lastAccessed: number;
}

export class DataCache<T = any> extends EventEmitter {
  private cache: Map<string, CacheItem<T>> = new Map();
  private options: Required<CacheOptions>;
  private cleanupTimer?: NodeJS.Timeout;

  constructor(options: CacheOptions = {}) {
    super();
    
    this.options = {
      maxSize: options.maxSize || 1000,
      ttl: options.ttl || 300000, // 5 minutes default
      checkInterval: options.checkInterval || 60000 // 1 minute default
    };

    // 启动清理定时器
    this.startCleanup();
  }

  /**
   * 设置缓存项
   * @param key 键
   * @param value 值
   */
  public set(key: string, value: T): void {
    const now = Date.now();
    
    // 检查大小限制
    if (this.cache.size >= this.options.maxSize && !this.cache.has(key)) {
      this.evictLRU();
    }

    const item: CacheItem<T> = {
      key,
      value,
      timestamp: now,
      accessCount: 0,
      lastAccessed: now
    };

    this.cache.set(key, item);
    this.emit('set', key, value);
  }

  /**
   * 获取缓存项
   * @param key 键
   * @returns 值或undefined
   */
  public get(key: string): T | undefined {
    const item = this.cache.get(key);
    
    if (!item) {
      this.emit('miss', key);
      return undefined;
    }

    // 检查TTL
    const now = Date.now();
    if (now - item.timestamp > this.options.ttl) {
      this.cache.delete(key);
      this.emit('expired', key);
      return undefined;
    }

    // 更新访问信息
    item.accessCount++;
    item.lastAccessed = now;
    
    this.emit('hit', key, item.value);
    return item.value;
  }

  /**
   * 检查键是否存在
   * @param key 键
   * @returns 是否存在
   */
  public has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }

    // 检查TTL
    const now = Date.now();
    if (now - item.timestamp > this.options.ttl) {
      this.cache.delete(key);
      this.emit('expired', key);
      return false;
    }

    return true;
  }

  /**
   * 删除缓存项
   * @param key 键
   * @returns 是否删除成功
   */
  public delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.emit('delete', key);
    }
    return deleted;
  }

  /**
   * 清空缓存
   */
  public clear(): void {
    const size = this.cache.size;
    this.cache.clear();
    this.emit('clear', size);
  }

  /**
   * 获取缓存大小
   * @returns 缓存项数量
   */
  public size(): number {
    return this.cache.size;
  }

  /**
   * 获取所有键
   * @returns 键数组
   */
  public keys(): string[] {
    return Array.from(this.cache.keys());
  }

  /**
   * 获取所有值
   * @returns 值数组
   */
  public values(): T[] {
    return Array.from(this.cache.values()).map(item => item.value);
  }

  /**
   * 获取缓存统计信息
   * @returns 统计信息
   */
  public getStats(): any {
    const items = Array.from(this.cache.values());
    const now = Date.now();
    
    return {
      size: this.cache.size,
      maxSize: this.options.maxSize,
      ttl: this.options.ttl,
      hitRate: this.calculateHitRate(),
      averageAccessCount: items.length > 0 ? items.reduce((sum, item) => sum + item.accessCount, 0) / items.length : 0,
      oldestItem: items.length > 0 ? Math.min(...items.map(item => item.timestamp)) : null,
      newestItem: items.length > 0 ? Math.max(...items.map(item => item.timestamp)) : null,
      expiredItems: items.filter(item => now - item.timestamp > this.options.ttl).length
    };
  }

  /**
   * 销毁缓存
   */
  public destroy(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = undefined;
    }
    
    this.clear();
    this.removeAllListeners();
  }

  /**
   * 启动清理定时器
   */
  private startCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup();
    }, this.options.checkInterval);
  }

  /**
   * 清理过期项
   */
  private cleanup(): void {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.cache.forEach((item, key) => {
      if (now - item.timestamp > this.options.ttl) {
        expiredKeys.push(key);
      }
    });

    expiredKeys.forEach(key => {
      this.cache.delete(key);
      this.emit('expired', key);
    });

    if (expiredKeys.length > 0) {
      this.emit('cleanup', expiredKeys.length);
    }
  }

  /**
   * 驱逐最少使用的项（LRU）
   */
  private evictLRU(): void {
    let lruKey: string | undefined;
    let lruTime = Date.now();

    this.cache.forEach((item, key) => {
      if (item.lastAccessed < lruTime) {
        lruTime = item.lastAccessed;
        lruKey = key;
      }
    });

    if (lruKey) {
      this.cache.delete(lruKey);
      this.emit('evict', lruKey);
    }
  }

  /**
   * 计算命中率
   */
  private calculateHitRate(): number {
    // 这里需要跟踪命中和未命中的次数
    // 为简化实现，返回一个估算值
    return 0.85; // 85% 命中率作为示例
  }
}

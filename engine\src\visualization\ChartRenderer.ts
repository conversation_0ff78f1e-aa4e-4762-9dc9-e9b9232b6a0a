/**
 * 图表渲染器
 * 提供基础的图表渲染功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { 
  ChartType, 
  DataSeries, 
  ChartConfig, 
  ChartEvent, 
  VisualizationOptions, 
  Renderer,
  DataPoint,
  ExportOptions
} from './types';

export class ChartRenderer extends EventEmitter implements Renderer {
  private container: HTMLElement;
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;
  private config: ChartConfig;
  private data: DataSeries[] = [];
  private isDestroyed: boolean = false;

  constructor() {
    super();
  }

  /**
   * 渲染图表
   * @param options 可视化选项
   */
  public render(options: VisualizationOptions): void {
    if (this.isDestroyed) {
      throw new Error('ChartRenderer has been destroyed');
    }

    this.config = options.config;
    this.data = options.data;

    // 获取容器
    if (typeof options.container === 'string') {
      const element = document.getElementById(options.container);
      if (!element) {
        throw new Error(`Container element '${options.container}' not found`);
      }
      this.container = element;
    } else {
      this.container = options.container;
    }

    // 创建画布
    this.createCanvas();

    // 渲染图表
    this.renderChart();

    // 设置交互
    if (options.interactive !== false) {
      this.setupInteractions();
    }

    // 设置实时更新
    if (options.realtime) {
      this.setupRealtimeUpdate(options.updateInterval || 1000);
    }
  }

  /**
   * 更新数据
   * @param data 新数据
   */
  public update(data: DataSeries[]): void {
    if (this.isDestroyed) {
      return;
    }

    this.data = data;
    this.renderChart();
  }

  /**
   * 调整大小
   * @param width 宽度
   * @param height 高度
   */
  public resize(width: number, height: number): void {
    if (this.isDestroyed) {
      return;
    }

    this.config.width = width;
    this.config.height = height;
    
    if (this.canvas) {
      this.canvas.width = width;
      this.canvas.height = height;
      this.renderChart();
    }
  }

  /**
   * 销毁渲染器
   */
  public destroy(): void {
    if (this.isDestroyed) {
      return;
    }

    if (this.canvas && this.container) {
      this.container.removeChild(this.canvas);
    }

    this.removeAllListeners();
    this.isDestroyed = true;
  }

  /**
   * 导出图表
   * @param options 导出选项
   * @returns 导出的数据
   */
  public export(options: ExportOptions): string | Blob {
    if (this.isDestroyed || !this.canvas) {
      throw new Error('ChartRenderer is not initialized');
    }

    switch (options.format) {
      case 'png':
      case 'jpg':
        return this.canvas.toDataURL(`image/${options.format}`, options.quality);
      case 'svg':
        return this.exportSVG();
      case 'pdf':
        return this.exportPDF();
      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  /**
   * 创建画布
   */
  private createCanvas(): void {
    this.canvas = document.createElement('canvas');
    this.canvas.width = this.config.width || 800;
    this.canvas.height = this.config.height || 600;
    
    const context = this.canvas.getContext('2d');
    if (!context) {
      throw new Error('Failed to get 2D context');
    }
    this.context = context;

    // 设置样式
    this.canvas.style.display = 'block';
    if (this.config.backgroundColor) {
      this.canvas.style.backgroundColor = this.config.backgroundColor;
    }

    // 添加到容器
    this.container.innerHTML = '';
    this.container.appendChild(this.canvas);
  }

  /**
   * 渲染图表
   */
  private renderChart(): void {
    if (!this.context) {
      return;
    }

    // 清空画布
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // 设置背景
    if (this.config.backgroundColor) {
      this.context.fillStyle = this.config.backgroundColor;
      this.context.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    // 根据图表类型渲染
    switch (this.config.type) {
      case ChartType.LINE:
        this.renderLineChart();
        break;
      case ChartType.BAR:
        this.renderBarChart();
        break;
      case ChartType.PIE:
        this.renderPieChart();
        break;
      case ChartType.SCATTER:
        this.renderScatterChart();
        break;
      case ChartType.AREA:
        this.renderAreaChart();
        break;
      default:
        this.renderPlaceholder();
    }

    // 渲染标题
    if (this.config.title) {
      this.renderTitle();
    }

    // 渲染图例
    if (this.config.showLegend !== false) {
      this.renderLegend();
    }

    // 渲染网格
    if (this.config.showGrid !== false) {
      this.renderGrid();
    }
  }

  /**
   * 渲染折线图
   */
  private renderLineChart(): void {
    const margin = { top: 50, right: 50, bottom: 50, left: 50 };
    const chartWidth = this.canvas.width - margin.left - margin.right;
    const chartHeight = this.canvas.height - margin.top - margin.bottom;

    this.data.forEach((series, index) => {
      if (series.visible === false) return;

      const color = series.color || this.getDefaultColor(index);
      this.context.strokeStyle = color;
      this.context.lineWidth = 2;
      this.context.beginPath();

      series.data.forEach((point, pointIndex) => {
        const x = margin.left + (pointIndex / (series.data.length - 1)) * chartWidth;
        const y = margin.top + chartHeight - (Number(point.y) / 100) * chartHeight;

        if (pointIndex === 0) {
          this.context.moveTo(x, y);
        } else {
          this.context.lineTo(x, y);
        }
      });

      this.context.stroke();
    });
  }

  /**
   * 渲染柱状图
   */
  private renderBarChart(): void {
    const margin = { top: 50, right: 50, bottom: 50, left: 50 };
    const chartWidth = this.canvas.width - margin.left - margin.right;
    const chartHeight = this.canvas.height - margin.top - margin.bottom;

    if (this.data.length === 0) return;

    const barWidth = chartWidth / this.data[0].data.length;

    this.data.forEach((series, seriesIndex) => {
      if (series.visible === false) return;

      const color = series.color || this.getDefaultColor(seriesIndex);
      this.context.fillStyle = color;

      series.data.forEach((point, pointIndex) => {
        const x = margin.left + pointIndex * barWidth;
        const height = (Number(point.y) / 100) * chartHeight;
        const y = margin.top + chartHeight - height;

        this.context.fillRect(x, y, barWidth * 0.8, height);
      });
    });
  }

  /**
   * 渲染饼图
   */
  private renderPieChart(): void {
    if (this.data.length === 0) return;

    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height / 2;
    const radius = Math.min(centerX, centerY) * 0.8;

    const series = this.data[0];
    const total = series.data.reduce((sum, point) => sum + Number(point.y), 0);
    let currentAngle = 0;

    series.data.forEach((point, index) => {
      const sliceAngle = (Number(point.y) / total) * 2 * Math.PI;
      const color = point.color || this.getDefaultColor(index);

      this.context.fillStyle = color;
      this.context.beginPath();
      this.context.moveTo(centerX, centerY);
      this.context.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
      this.context.closePath();
      this.context.fill();

      currentAngle += sliceAngle;
    });
  }

  /**
   * 渲染散点图
   */
  private renderScatterChart(): void {
    const margin = { top: 50, right: 50, bottom: 50, left: 50 };
    const chartWidth = this.canvas.width - margin.left - margin.right;
    const chartHeight = this.canvas.height - margin.top - margin.bottom;

    this.data.forEach((series, index) => {
      if (series.visible === false) return;

      const color = series.color || this.getDefaultColor(index);
      this.context.fillStyle = color;

      series.data.forEach(point => {
        const x = margin.left + (Number(point.x) / 100) * chartWidth;
        const y = margin.top + chartHeight - (Number(point.y) / 100) * chartHeight;

        this.context.beginPath();
        this.context.arc(x, y, 3, 0, 2 * Math.PI);
        this.context.fill();
      });
    });
  }

  /**
   * 渲染面积图
   */
  private renderAreaChart(): void {
    // 类似折线图，但填充区域
    this.renderLineChart();
    // TODO: 添加区域填充逻辑
  }

  /**
   * 渲染占位符
   */
  private renderPlaceholder(): void {
    this.context.fillStyle = '#cccccc';
    this.context.font = '24px Arial';
    this.context.textAlign = 'center';
    this.context.fillText(
      `${this.config.type} chart not implemented`,
      this.canvas.width / 2,
      this.canvas.height / 2
    );
  }

  /**
   * 渲染标题
   */
  private renderTitle(): void {
    this.context.fillStyle = '#333333';
    this.context.font = 'bold 20px Arial';
    this.context.textAlign = 'center';
    this.context.fillText(this.config.title!, this.canvas.width / 2, 30);
  }

  /**
   * 渲染图例
   */
  private renderLegend(): void {
    const legendY = this.canvas.height - 20;
    let legendX = 50;

    this.data.forEach((series, index) => {
      if (series.visible === false) return;

      const color = series.color || this.getDefaultColor(index);
      
      // 绘制颜色块
      this.context.fillStyle = color;
      this.context.fillRect(legendX, legendY - 10, 15, 15);
      
      // 绘制文本
      this.context.fillStyle = '#333333';
      this.context.font = '12px Arial';
      this.context.textAlign = 'left';
      this.context.fillText(series.name, legendX + 20, legendY);
      
      legendX += this.context.measureText(series.name).width + 50;
    });
  }

  /**
   * 渲染网格
   */
  private renderGrid(): void {
    const margin = { top: 50, right: 50, bottom: 50, left: 50 };
    const chartWidth = this.canvas.width - margin.left - margin.right;
    const chartHeight = this.canvas.height - margin.top - margin.bottom;

    this.context.strokeStyle = '#e0e0e0';
    this.context.lineWidth = 1;

    // 垂直网格线
    for (let i = 0; i <= 10; i++) {
      const x = margin.left + (i / 10) * chartWidth;
      this.context.beginPath();
      this.context.moveTo(x, margin.top);
      this.context.lineTo(x, margin.top + chartHeight);
      this.context.stroke();
    }

    // 水平网格线
    for (let i = 0; i <= 10; i++) {
      const y = margin.top + (i / 10) * chartHeight;
      this.context.beginPath();
      this.context.moveTo(margin.left, y);
      this.context.lineTo(margin.left + chartWidth, y);
      this.context.stroke();
    }
  }

  /**
   * 设置交互
   */
  private setupInteractions(): void {
    this.canvas.addEventListener('click', (event) => {
      const rect = this.canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const chartEvent: ChartEvent = {
        type: 'click',
        position: { x, y },
        timestamp: new Date()
      };

      this.emit('click', chartEvent);
    });

    this.canvas.addEventListener('mousemove', (event) => {
      const rect = this.canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const chartEvent: ChartEvent = {
        type: 'hover',
        position: { x, y },
        timestamp: new Date()
      };

      this.emit('hover', chartEvent);
    });
  }

  /**
   * 设置实时更新
   */
  private setupRealtimeUpdate(interval: number): void {
    setInterval(() => {
      if (!this.isDestroyed) {
        this.emit('update-request');
      }
    }, interval);
  }

  /**
   * 获取默认颜色
   */
  private getDefaultColor(index: number): string {
    const colors = this.config.colors || [
      '#3498db', '#e74c3c', '#2ecc71', '#f39c12', '#9b59b6',
      '#1abc9c', '#34495e', '#e67e22', '#95a5a6', '#d35400'
    ];
    return colors[index % colors.length];
  }

  /**
   * 导出SVG
   */
  private exportSVG(): string {
    // 简化实现
    return '<svg></svg>';
  }

  /**
   * 导出PDF
   */
  private exportPDF(): Blob {
    // 简化实现
    return new Blob(['PDF content'], { type: 'application/pdf' });
  }
}

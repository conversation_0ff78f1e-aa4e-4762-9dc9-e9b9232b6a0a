/**
 * 空间数据模块
 */
import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { SpatialDataController } from './spatial-data.controller';
import { SpatialDataService } from './spatial-data.service';
import { SpatialFeature } from '../entities/spatial-feature.entity';
import { SpatialLayer } from '../entities/spatial-layer.entity';
import { SpatialProject } from '../entities/spatial-project.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      SpatialFeature,
      SpatialLayer,
      SpatialProject
    ])
  ],
  controllers: [SpatialDataController],
  providers: [SpatialDataService],
  exports: [SpatialDataService]
})
export class SpatialDataModule {}

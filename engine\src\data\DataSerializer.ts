/**
 * 数据序列化器
 * 提供数据序列化和反序列化功能
 */
import { DataItem, DataType } from './types';

export interface SerializationOptions {
  compression?: boolean;
  encryption?: boolean;
  format?: 'json' | 'binary' | 'msgpack';
}

export class DataSerializer {
  /**
   * 序列化数据项
   * @param item 数据项
   * @param options 序列化选项
   * @returns 序列化后的数据
   */
  public static serialize(item: DataItem, options: SerializationOptions = {}): string | ArrayBuffer {
    const format = options.format || 'json';
    
    try {
      let serialized: string | ArrayBuffer;
      
      switch (format) {
        case 'json':
          serialized = this.serializeJSON(item);
          break;
        case 'binary':
          serialized = this.serializeBinary(item);
          break;
        case 'msgpack':
          serialized = this.serializeMsgPack(item);
          break;
        default:
          throw new Error(`Unsupported serialization format: ${format}`);
      }

      // 应用压缩
      if (options.compression) {
        serialized = this.compress(serialized);
      }

      // 应用加密
      if (options.encryption) {
        serialized = this.encrypt(serialized);
      }

      return serialized;
    } catch (error) {
      throw new Error(`Serialization failed: ${error}`);
    }
  }

  /**
   * 反序列化数据项
   * @param data 序列化的数据
   * @param options 序列化选项
   * @returns 数据项
   */
  public static deserialize(data: string | ArrayBuffer, options: SerializationOptions = {}): DataItem {
    const format = options.format || 'json';
    
    try {
      let processedData = data;

      // 应用解密
      if (options.encryption) {
        processedData = this.decrypt(processedData);
      }

      // 应用解压缩
      if (options.compression) {
        processedData = this.decompress(processedData);
      }

      let item: DataItem;
      
      switch (format) {
        case 'json':
          item = this.deserializeJSON(processedData as string);
          break;
        case 'binary':
          item = this.deserializeBinary(processedData as ArrayBuffer);
          break;
        case 'msgpack':
          item = this.deserializeMsgPack(processedData as ArrayBuffer);
          break;
        default:
          throw new Error(`Unsupported deserialization format: ${format}`);
      }

      return item;
    } catch (error) {
      throw new Error(`Deserialization failed: ${error}`);
    }
  }

  /**
   * JSON序列化
   */
  private static serializeJSON(item: DataItem): string {
    return JSON.stringify({
      ...item,
      timestamp: item.timestamp.toISOString()
    });
  }

  /**
   * JSON反序列化
   */
  private static deserializeJSON(data: string): DataItem {
    const parsed = JSON.parse(data);
    return {
      ...parsed,
      timestamp: new Date(parsed.timestamp)
    };
  }

  /**
   * 二进制序列化
   */
  private static serializeBinary(item: DataItem): ArrayBuffer {
    // 简化的二进制序列化实现
    const jsonString = this.serializeJSON(item);
    const encoder = new TextEncoder();
    return encoder.encode(jsonString).buffer;
  }

  /**
   * 二进制反序列化
   */
  private static deserializeBinary(data: ArrayBuffer): DataItem {
    const decoder = new TextDecoder();
    const jsonString = decoder.decode(data);
    return this.deserializeJSON(jsonString);
  }

  /**
   * MessagePack序列化
   */
  private static serializeMsgPack(item: DataItem): ArrayBuffer {
    // 简化实现，实际应该使用MessagePack库
    return this.serializeBinary(item);
  }

  /**
   * MessagePack反序列化
   */
  private static deserializeMsgPack(data: ArrayBuffer): DataItem {
    // 简化实现，实际应该使用MessagePack库
    return this.deserializeBinary(data);
  }

  /**
   * 压缩数据
   */
  private static compress(data: string | ArrayBuffer): string | ArrayBuffer {
    // 简化实现，实际应该使用压缩算法
    if (typeof data === 'string') {
      // 简单的字符串压缩模拟
      return data.replace(/\s+/g, ' ').trim();
    }
    return data;
  }

  /**
   * 解压缩数据
   */
  private static decompress(data: string | ArrayBuffer): string | ArrayBuffer {
    // 简化实现，实际应该使用解压缩算法
    return data;
  }

  /**
   * 加密数据
   */
  private static encrypt(data: string | ArrayBuffer): string | ArrayBuffer {
    // 简化实现，实际应该使用加密算法
    if (typeof data === 'string') {
      // 简单的字符串加密模拟（Base64编码）
      return btoa(data);
    }
    return data;
  }

  /**
   * 解密数据
   */
  private static decrypt(data: string | ArrayBuffer): string | ArrayBuffer {
    // 简化实现，实际应该使用解密算法
    if (typeof data === 'string') {
      try {
        // 简单的字符串解密模拟（Base64解码）
        return atob(data);
      } catch {
        return data;
      }
    }
    return data;
  }

  /**
   * 验证数据项
   * @param item 数据项
   * @returns 是否有效
   */
  public static validate(item: any): item is DataItem {
    return (
      typeof item === 'object' &&
      item !== null &&
      typeof item.id === 'string' &&
      typeof item.key === 'string' &&
      item.value !== undefined &&
      Object.values(DataType).includes(item.type) &&
      item.timestamp instanceof Date
    );
  }

  /**
   * 计算数据大小
   * @param item 数据项
   * @returns 大小（字节）
   */
  public static calculateSize(item: DataItem): number {
    const serialized = this.serializeJSON(item);
    return new Blob([serialized]).size;
  }

  /**
   * 克隆数据项
   * @param item 数据项
   * @returns 克隆的数据项
   */
  public static clone(item: DataItem): DataItem {
    const serialized = this.serializeJSON(item);
    return this.deserializeJSON(serialized);
  }
}

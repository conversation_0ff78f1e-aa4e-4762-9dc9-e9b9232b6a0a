/**
 * 数据可视化器
 * 提供高级的数据可视化功能
 */
import { EventEmitter } from '../utils/EventEmitter';
import { ChartRenderer } from './ChartRenderer';
import { 
  ChartType, 
  DataSeries, 
  ChartConfig, 
  VisualizationOptions,
  DataPoint,
  RealtimeConfig
} from './types';

export class DataVisualizer extends EventEmitter {
  private renderers: Map<string, ChartRenderer> = new Map();
  private realtimeConfigs: Map<string, RealtimeConfig> = new Map();
  private updateTimers: Map<string, NodeJS.Timeout> = new Map();

  /**
   * 创建图表
   * @param id 图表ID
   * @param options 可视化选项
   * @returns 图表渲染器
   */
  public createChart(id: string, options: VisualizationOptions): ChartRenderer {
    if (this.renderers.has(id)) {
      throw new Error(`Chart with id '${id}' already exists`);
    }

    const renderer = new ChartRenderer();
    renderer.render(options);

    // 监听渲染器事件
    renderer.on('click', (event) => this.emit('chart-click', id, event));
    renderer.on('hover', (event) => this.emit('chart-hover', id, event));
    renderer.on('update-request', () => this.handleUpdateRequest(id));

    this.renderers.set(id, renderer);

    // 设置实时配置
    if (options.realtime) {
      const realtimeConfig: RealtimeConfig = {
        enabled: true,
        updateInterval: options.updateInterval || 1000,
        maxDataPoints: options.maxDataPoints || 100,
        bufferSize: options.maxDataPoints || 100
      };
      this.realtimeConfigs.set(id, realtimeConfig);
    }

    return renderer;
  }

  /**
   * 获取图表
   * @param id 图表ID
   * @returns 图表渲染器
   */
  public getChart(id: string): ChartRenderer | undefined {
    return this.renderers.get(id);
  }

  /**
   * 更新图表数据
   * @param id 图表ID
   * @param data 新数据
   */
  public updateChart(id: string, data: DataSeries[]): void {
    const renderer = this.renderers.get(id);
    if (!renderer) {
      throw new Error(`Chart with id '${id}' not found`);
    }

    // 应用实时配置
    const realtimeConfig = this.realtimeConfigs.get(id);
    if (realtimeConfig) {
      data = this.applyRealtimeConfig(data, realtimeConfig);
    }

    renderer.update(data);
  }

  /**
   * 删除图表
   * @param id 图表ID
   */
  public removeChart(id: string): void {
    const renderer = this.renderers.get(id);
    if (renderer) {
      renderer.destroy();
      this.renderers.delete(id);
    }

    const timer = this.updateTimers.get(id);
    if (timer) {
      clearInterval(timer);
      this.updateTimers.delete(id);
    }

    this.realtimeConfigs.delete(id);
  }

  /**
   * 获取所有图表ID
   * @returns 图表ID数组
   */
  public getChartIds(): string[] {
    return Array.from(this.renderers.keys());
  }

  /**
   * 创建仪表盘
   * @param containerId 容器ID
   * @param charts 图表配置数组
   */
  public createDashboard(containerId: string, charts: Array<{
    id: string;
    options: VisualizationOptions;
    position: { x: number; y: number; width: number; height: number };
  }>): void {
    const container = document.getElementById(containerId);
    if (!container) {
      throw new Error(`Container '${containerId}' not found`);
    }

    // 设置容器样式
    container.style.position = 'relative';
    container.style.width = '100%';
    container.style.height = '100%';

    charts.forEach(chart => {
      // 创建图表容器
      const chartContainer = document.createElement('div');
      chartContainer.id = `${chart.id}-container`;
      chartContainer.style.position = 'absolute';
      chartContainer.style.left = `${chart.position.x}px`;
      chartContainer.style.top = `${chart.position.y}px`;
      chartContainer.style.width = `${chart.position.width}px`;
      chartContainer.style.height = `${chart.position.height}px`;
      chartContainer.style.border = '1px solid #ddd';
      chartContainer.style.borderRadius = '4px';
      chartContainer.style.padding = '10px';
      chartContainer.style.boxSizing = 'border-box';

      container.appendChild(chartContainer);

      // 创建图表
      const options = {
        ...chart.options,
        container: chartContainer,
        config: {
          ...chart.options.config,
          width: chart.position.width - 20,
          height: chart.position.height - 20
        }
      };

      this.createChart(chart.id, options);
    });
  }

  /**
   * 导出图表
   * @param id 图表ID
   * @param format 导出格式
   * @returns 导出数据
   */
  public exportChart(id: string, format: 'png' | 'jpg' | 'svg' | 'pdf'): string | Blob {
    const renderer = this.renderers.get(id);
    if (!renderer) {
      throw new Error(`Chart with id '${id}' not found`);
    }

    return renderer.export({ format });
  }

  /**
   * 设置实时数据源
   * @param id 图表ID
   * @param dataSource 数据源函数
   * @param config 实时配置
   */
  public setRealtimeDataSource(
    id: string, 
    dataSource: () => Promise<DataSeries[]>, 
    config?: Partial<RealtimeConfig>
  ): void {
    const existingConfig = this.realtimeConfigs.get(id) || {
      enabled: true,
      updateInterval: 1000,
      maxDataPoints: 100,
      bufferSize: 100
    };

    const realtimeConfig = { ...existingConfig, ...config };
    this.realtimeConfigs.set(id, realtimeConfig);

    // 清除现有定时器
    const existingTimer = this.updateTimers.get(id);
    if (existingTimer) {
      clearInterval(existingTimer);
    }

    // 设置新定时器
    const timer = setInterval(async () => {
      try {
        const data = await dataSource();
        this.updateChart(id, data);
      } catch (error) {
        this.emit('error', id, error);
      }
    }, realtimeConfig.updateInterval);

    this.updateTimers.set(id, timer);
  }

  /**
   * 停止实时更新
   * @param id 图表ID
   */
  public stopRealtimeUpdate(id: string): void {
    const timer = this.updateTimers.get(id);
    if (timer) {
      clearInterval(timer);
      this.updateTimers.delete(id);
    }

    const config = this.realtimeConfigs.get(id);
    if (config) {
      config.enabled = false;
    }
  }

  /**
   * 创建数据系列
   * @param name 系列名称
   * @param data 数据点数组
   * @param options 选项
   * @returns 数据系列
   */
  public static createDataSeries(
    name: string, 
    data: DataPoint[], 
    options: Partial<DataSeries> = {}
  ): DataSeries {
    return {
      name,
      data,
      visible: true,
      ...options
    };
  }

  /**
   * 创建数据点
   * @param x X值
   * @param y Y值
   * @param options 选项
   * @returns 数据点
   */
  public static createDataPoint(
    x: number | string | Date, 
    y: number, 
    options: Partial<DataPoint> = {}
  ): DataPoint {
    return {
      x,
      y,
      ...options
    };
  }

  /**
   * 生成随机数据
   * @param count 数据点数量
   * @param min 最小值
   * @param max 最大值
   * @returns 数据点数组
   */
  public static generateRandomData(count: number, min: number = 0, max: number = 100): DataPoint[] {
    const data: DataPoint[] = [];
    for (let i = 0; i < count; i++) {
      data.push({
        x: i,
        y: Math.random() * (max - min) + min
      });
    }
    return data;
  }

  /**
   * 销毁可视化器
   */
  public destroy(): void {
    // 清除所有定时器
    this.updateTimers.forEach(timer => clearInterval(timer));
    this.updateTimers.clear();

    // 销毁所有渲染器
    this.renderers.forEach(renderer => renderer.destroy());
    this.renderers.clear();

    // 清除配置
    this.realtimeConfigs.clear();

    // 移除所有监听器
    this.removeAllListeners();
  }

  /**
   * 处理更新请求
   */
  private handleUpdateRequest(id: string): void {
    this.emit('update-request', id);
  }

  /**
   * 应用实时配置
   */
  private applyRealtimeConfig(data: DataSeries[], config: RealtimeConfig): DataSeries[] {
    return data.map(series => {
      let processedData = [...series.data];

      // 限制数据点数量
      if (processedData.length > config.maxDataPoints) {
        processedData = processedData.slice(-config.maxDataPoints);
      }

      // 应用平滑处理
      if (config.smoothing && processedData.length > 1) {
        processedData = this.applySmoothingFilter(processedData);
      }

      // 应用聚合
      if (config.aggregation && config.aggregation !== 'none') {
        processedData = this.applyAggregation(processedData, config.aggregation);
      }

      return {
        ...series,
        data: processedData
      };
    });
  }

  /**
   * 应用平滑滤波
   */
  private applySmoothingFilter(data: DataPoint[]): DataPoint[] {
    const smoothed: DataPoint[] = [];
    const windowSize = 3;

    for (let i = 0; i < data.length; i++) {
      const start = Math.max(0, i - Math.floor(windowSize / 2));
      const end = Math.min(data.length, i + Math.ceil(windowSize / 2));
      
      const window = data.slice(start, end);
      const avgY = window.reduce((sum, point) => sum + Number(point.y), 0) / window.length;

      smoothed.push({
        ...data[i],
        y: avgY
      });
    }

    return smoothed;
  }

  /**
   * 应用聚合
   */
  private applyAggregation(data: DataPoint[], aggregation: string): DataPoint[] {
    // 简化实现，实际应该根据时间窗口进行聚合
    return data;
  }
}

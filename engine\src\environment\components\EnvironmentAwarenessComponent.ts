/**
 * 环境感知组件
 *
 * 该组件使实体能够感知周围的环境，包括环境类型、天气、光照条件、地形等。
 * 可以附加到角色或其他需要环境感知能力的实体上。
 */

import { Component } from '../../core/Component';
import { Vector3 } from 'three';

/**
 * 环境类型枚举
 */
export enum EnvironmentType {
  INDOOR = 'indoor',
  OUTDOOR = 'outdoor',
  UNDERWATER = 'underwater',
  SPACE = 'space',
  CAVE = 'cave',
  FOREST = 'forest',
  DESERT = 'desert',
  SNOW = 'snow',
  URBAN = 'urban',
  CUSTOM = 'custom'
}

/**
 * 天气类型枚举
 */
export enum WeatherType {
  CLEAR = 'clear',
  CLOUDY = 'cloudy',
  RAINY = 'rainy',
  STORMY = 'stormy',
  SNOWY = 'snowy',
  FOGGY = 'foggy',
  CUSTOM = 'custom'
}

/**
 * 地形类型枚举
 */
export enum TerrainType {
  FLAT = 'flat',
  HILLS = 'hills',
  MOUNTAINS = 'mountains',
  WATER = 'water',
  URBAN = 'urban',
  CUSTOM = 'custom'
}

/**
 * 环境感知数据接口
 */
export interface EnvironmentAwarenessData {
  // 当前环境类型
  environmentType: EnvironmentType;
  // 当前天气类型
  weatherType: WeatherType;
  // 当前地形类型
  terrainType: TerrainType;
  // 当前光照强度 (0-1)
  lightIntensity: number;
  // 当前温度 (摄氏度)
  temperature: number;
  // 当前湿度 (0-1)
  humidity: number;
  // 当前风速 (米/秒)
  windSpeed: number;
  // 当前风向
  windDirection: Vector3;
  // 当前噪音级别 (0-1)
  noiseLevel: number;
  // 当前空气质量 (0-1，1为最佳)
  airQuality: number;
  // 当前水位 (米)
  waterLevel: number;
  // 当前可见度 (米)
  visibility: number;
  // 当前时间 (24小时制)
  timeOfDay: number;
  // 自定义环境参数
  customParameters: Map<string, any>;
  // 最近检测到的环境变化时间戳
  lastEnvironmentChangeTime: number;
  // 环境感知范围 (米)
  awarenessRange: number;
}

/**
 * 环境感知组件配置接口
 */
export interface EnvironmentAwarenessComponentConfig {
  // 环境感知范围 (米)
  awarenessRange?: number;
  // 环境感知更新频率 (毫秒)
  updateFrequency?: number;
  // 是否启用调试模式
  debug?: boolean;
  // 是否自动检测环境
  autoDetect?: boolean;
  // 环境变化阈值
  changeThreshold?: number;
}

/**
 * 环境感知组件
 */
export class EnvironmentAwarenessComponent extends Component {
  // 环境感知数据
  public data: EnvironmentAwarenessData;
  // 环境感知组件专用配置
  private awarenessConfig: EnvironmentAwarenessComponentConfig;
  // 环境变化回调函数
  private onEnvironmentChangeCallbacks: Array<(data: EnvironmentAwarenessData) => void> = [];

  /**
   * 构造函数
   * @param config 配置
   */
  constructor(config: EnvironmentAwarenessComponentConfig = {}) {
    super('EnvironmentAwarenessComponent');

    // 设置默认配置
    this.awarenessConfig = {
      awarenessRange: 50,
      updateFrequency: 1000,
      debug: false,
      autoDetect: true,
      changeThreshold: 0.1,
      ...config
    };

    // 初始化环境感知数据
    this.data = {
      environmentType: EnvironmentType.OUTDOOR,
      weatherType: WeatherType.CLEAR,
      terrainType: TerrainType.FLAT,
      lightIntensity: 1.0,
      temperature: 20,
      humidity: 0.5,
      windSpeed: 0,
      windDirection: new Vector3(1, 0, 0),
      noiseLevel: 0,
      airQuality: 1.0,
      waterLevel: 0,
      visibility: 1000,
      timeOfDay: 12,
      customParameters: new Map<string, any>(),
      lastEnvironmentChangeTime: Date.now(),
      awarenessRange: this.awarenessConfig.awarenessRange || 50
    };
  }

  /**
   * 更新环境感知数据
   * @param data 新的环境感知数据
   */
  public updateEnvironmentData(data: Partial<EnvironmentAwarenessData>): void {
    const now = Date.now();
    const hasSignificantChange = this.hasSignificantChange(data);

    // 更新数据
    Object.assign(this.data, data);

    // 如果有显著变化，触发环境变化事件
    if (hasSignificantChange) {
      this.data.lastEnvironmentChangeTime = now;
      this.notifyEnvironmentChange();
    }

    if (this.awarenessConfig.debug) {
      console.log('环境感知数据已更新:', this.data);
    }
  }

  /**
   * 检查是否有显著变化
   * @param newData 新数据
   * @returns 是否有显著变化
   */
  private hasSignificantChange(newData: Partial<EnvironmentAwarenessData>): boolean {
    const threshold = this.awarenessConfig.changeThreshold || 0.1;

    // 检查枚举类型变化
    if (
      (newData.environmentType !== undefined && newData.environmentType !== this.data.environmentType) ||
      (newData.weatherType !== undefined && newData.weatherType !== this.data.weatherType) ||
      (newData.terrainType !== undefined && newData.terrainType !== this.data.terrainType)
    ) {
      return true;
    }

    // 检查数值型变化
    if (
      (newData.lightIntensity !== undefined && Math.abs(newData.lightIntensity - this.data.lightIntensity) > threshold) ||
      (newData.temperature !== undefined && Math.abs(newData.temperature - this.data.temperature) > 2) ||
      (newData.humidity !== undefined && Math.abs(newData.humidity - this.data.humidity) > threshold) ||
      (newData.windSpeed !== undefined && Math.abs(newData.windSpeed - this.data.windSpeed) > 1) ||
      (newData.noiseLevel !== undefined && Math.abs(newData.noiseLevel - this.data.noiseLevel) > threshold) ||
      (newData.airQuality !== undefined && Math.abs(newData.airQuality - this.data.airQuality) > threshold) ||
      (newData.waterLevel !== undefined && Math.abs(newData.waterLevel - this.data.waterLevel) > 0.5) ||
      (newData.visibility !== undefined && Math.abs(newData.visibility - this.data.visibility) > 100) ||
      (newData.timeOfDay !== undefined && Math.abs(newData.timeOfDay - this.data.timeOfDay) > 1)
    ) {
      return true;
    }

    return false;
  }

  /**
   * 注册环境变化回调
   * @param callback 回调函数
   */
  public onEnvironmentChange(callback: (data: EnvironmentAwarenessData) => void): void {
    this.onEnvironmentChangeCallbacks.push(callback);
  }

  /**
   * 移除环境变化回调
   * @param callback 回调函数
   */
  public removeEnvironmentChangeCallback(callback: (data: EnvironmentAwarenessData) => void): void {
    const index = this.onEnvironmentChangeCallbacks.indexOf(callback);
    if (index !== -1) {
      this.onEnvironmentChangeCallbacks.splice(index, 1);
    }
  }

  /**
   * 通知环境变化
   */
  private notifyEnvironmentChange(): void {
    for (const callback of this.onEnvironmentChangeCallbacks) {
      callback(this.data);
    }
  }

  /**
   * 获取当前环境数据
   * @returns 环境数据
   */
  public getEnvironmentData(): EnvironmentAwarenessData {
    return { ...this.data };
  }

  /**
   * 设置自定义环境参数
   * @param key 参数键
   * @param value 参数值
   */
  public setCustomParameter(key: string, value: any): void {
    this.data.customParameters.set(key, value);
  }

  /**
   * 获取自定义环境参数
   * @param key 参数键
   * @returns 参数值
   */
  public getCustomParameter(key: string): any {
    return this.data.customParameters.get(key);
  }

  /**
   * 获取环境感知组件配置
   * @returns 配置对象
   */
  public getAwarenessConfig(): EnvironmentAwarenessComponentConfig {
    return { ...this.awarenessConfig };
  }

  /**
   * 创建组件实例（实现抽象方法）
   * @returns 新的组件实例
   */
  protected createInstance(): EnvironmentAwarenessComponent {
    return new EnvironmentAwarenessComponent(this.awarenessConfig);
  }
}

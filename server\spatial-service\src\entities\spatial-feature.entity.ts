/**
 * 空间要素实体
 */
import { 
  Entity, 
  PrimaryGeneratedColumn, 
  Column, 
  ManyToOne, 
  JoinColumn, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index
} from 'typeorm';
import { SpatialLayer } from './spatial-layer.entity';

@Entity('spatial_features')
@Index(['layerId'])
@Index(['featureType'])
@Index(['createdAt'])
export class SpatialFeature {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ 
    type: 'enum',
    enum: ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'],
    name: 'feature_type'
  })
  featureType: 'Point' | 'LineString' | 'Polygon' | 'MultiPoint' | 'MultiLineString' | 'MultiPolygon';

  @Column({ 
    type: 'geometry',
    spatialFeatureType: 'Geometry',
    srid: 4326
  })
  @Index({ spatial: true })
  geometry: any;

  @Column({ type: 'jsonb', nullable: true })
  properties: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  style: {
    color?: string;
    fillColor?: string;
    weight?: number;
    opacity?: number;
    fillOpacity?: number;
    radius?: number;
    strokeWidth?: number;
    strokeColor?: string;
    strokeOpacity?: number;
    fillPattern?: string;
    iconUrl?: string;
    iconSize?: [number, number];
    iconAnchor?: [number, number];
  };

  @ManyToOne(() => SpatialLayer, layer => layer.features, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'layer_id' })
  layer: SpatialLayer;

  @Column({ name: 'layer_id' })
  layerId: string;

  @Column({ type: 'boolean', default: true })
  visible: boolean;

  @Column({ type: 'boolean', default: true })
  editable: boolean;

  @Column({ type: 'boolean', default: true })
  selectable: boolean;

  @Column({ type: 'int', default: 0 })
  zIndex: number;

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    source?: string;
    accuracy?: number;
    confidence?: number;
    tags?: string[];
    category?: string;
    subcategory?: string;
    version?: number;
    lastModifiedBy?: string;
    [key: string]: any;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by', nullable: true })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  // 计算属性
  get area(): number | null {
    // 这里应该使用PostGIS函数计算面积
    // 在实际查询中通过 ST_Area(geometry) 计算
    return null;
  }

  get length(): number | null {
    // 这里应该使用PostGIS函数计算长度
    // 在实际查询中通过 ST_Length(geometry) 计算
    return null;
  }

  get centroid(): any | null {
    // 这里应该使用PostGIS函数计算质心
    // 在实际查询中通过 ST_Centroid(geometry) 计算
    return null;
  }

  get bbox(): [number, number, number, number] | null {
    // 这里应该使用PostGIS函数计算边界框
    // 在实际查询中通过 ST_Extent(geometry) 计算
    return null;
  }

  // 辅助方法
  toGeoJSON(): any {
    return {
      type: 'Feature',
      id: this.id,
      geometry: this.geometry,
      properties: {
        ...this.properties,
        name: this.name,
        description: this.description,
        featureType: this.featureType,
        style: this.style,
        visible: this.visible,
        editable: this.editable,
        selectable: this.selectable,
        zIndex: this.zIndex,
        metadata: this.metadata,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt,
        createdBy: this.createdBy,
        updatedBy: this.updatedBy
      }
    };
  }

  // 验证几何数据
  validateGeometry(): boolean {
    if (!this.geometry || !this.geometry.type || !this.geometry.coordinates) {
      return false;
    }

    const validTypes = ['Point', 'LineString', 'Polygon', 'MultiPoint', 'MultiLineString', 'MultiPolygon'];
    if (!validTypes.includes(this.geometry.type)) {
      return false;
    }

    // 验证坐标格式
    try {
      const coords = this.geometry.coordinates;
      if (!Array.isArray(coords)) {
        return false;
      }

      switch (this.geometry.type) {
        case 'Point':
          return coords.length >= 2 && typeof coords[0] === 'number' && typeof coords[1] === 'number';
        
        case 'LineString':
          return coords.length >= 2 && coords.every((coord: any) => 
            Array.isArray(coord) && coord.length >= 2 && 
            typeof coord[0] === 'number' && typeof coord[1] === 'number'
          );
        
        case 'Polygon':
          return coords.length >= 1 && coords[0].length >= 4 && 
            coords[0].every((coord: any) => 
              Array.isArray(coord) && coord.length >= 2 && 
              typeof coord[0] === 'number' && typeof coord[1] === 'number'
            );
        
        default:
          return true; // 对于MultiPoint、MultiLineString、MultiPolygon暂时简单验证
      }
    } catch (error) {
      return false;
    }
  }

  // 获取几何类型的中文名称
  getFeatureTypeName(): string {
    const typeNames = {
      'Point': '点',
      'LineString': '线',
      'Polygon': '面',
      'MultiPoint': '多点',
      'MultiLineString': '多线',
      'MultiPolygon': '多面'
    };
    return typeNames[this.featureType] || this.featureType;
  }

  // 检查是否与另一个要素相交
  intersectsWith(other: SpatialFeature): boolean {
    // 这里应该使用PostGIS函数检查相交
    // 在实际查询中通过 ST_Intersects(this.geometry, other.geometry) 计算
    return false;
  }

  // 计算到另一个要素的距离
  distanceTo(other: SpatialFeature): number {
    // 这里应该使用PostGIS函数计算距离
    // 在实际查询中通过 ST_Distance(this.geometry, other.geometry) 计算
    return 0;
  }

  // 检查点是否在要素内部（仅对Polygon有效）
  contains(point: [number, number]): boolean {
    // 这里应该使用PostGIS函数检查包含关系
    // 在实际查询中通过 ST_Contains(this.geometry, ST_Point(point[0], point[1])) 计算
    return false;
  }

  // 获取要素的简化版本（用于显示优化）
  getSimplified(tolerance: number = 0.001): any {
    // 这里应该使用PostGIS函数简化几何
    // 在实际查询中通过 ST_Simplify(this.geometry, tolerance) 计算
    return this.geometry;
  }

  // 转换坐标系
  transform(targetSRID: number): any {
    // 这里应该使用PostGIS函数转换坐标系
    // 在实际查询中通过 ST_Transform(this.geometry, targetSRID) 计算
    return this.geometry;
  }

  // 获取要素的统计信息
  getStatistics(): {
    area?: number;
    length?: number;
    pointCount?: number;
    vertexCount?: number;
  } {
    const stats: any = {};

    if (this.featureType === 'Polygon' || this.featureType === 'MultiPolygon') {
      stats.area = this.area;
    }

    if (this.featureType === 'LineString' || this.featureType === 'MultiLineString') {
      stats.length = this.length;
    }

    if (this.featureType === 'Point' || this.featureType === 'MultiPoint') {
      stats.pointCount = this.featureType === 'Point' ? 1 : 
        (this.geometry.coordinates ? this.geometry.coordinates.length : 0);
    }

    // 计算顶点数
    try {
      switch (this.featureType) {
        case 'Point':
          stats.vertexCount = 1;
          break;
        case 'LineString':
          stats.vertexCount = this.geometry.coordinates ? this.geometry.coordinates.length : 0;
          break;
        case 'Polygon':
          stats.vertexCount = this.geometry.coordinates && this.geometry.coordinates[0] ? 
            this.geometry.coordinates[0].length : 0;
          break;
        default:
          stats.vertexCount = 0;
      }
    } catch (error) {
      stats.vertexCount = 0;
    }

    return stats;
  }
}

/**
 * 空间服务主入口文件
 */
import { NestFactory } from '@nestjs/core';
import { ValidationPipe, Logger } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { ConfigService } from '@nestjs/config';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);
  const logger = new Logger('SpatialService');

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  // CORS配置
  app.enableCors({
    origin: configService.get('CORS_ORIGIN', '*'),
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true,
  });

  // 全局前缀
  app.setGlobalPrefix('api/v1');

  // Swagger文档配置
  const config = new DocumentBuilder()
    .setTitle('空间信息系统API')
    .setDescription('DL引擎空间信息系统服务API文档')
    .setVersion('1.0.0')
    .addBearerAuth()
    .addTag('空间数据', '空间要素和图层管理')
    .addTag('空间查询', '空间查询和分析')
    .addTag('数据导入导出', '空间数据的导入导出')
    .addServer(configService.get('API_BASE_URL', 'http://localhost:3001'), '开发环境')
    .build();

  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      tagsSorter: 'alpha',
      operationsSorter: 'alpha',
    },
  });

  // 启动服务
  const port = configService.get('PORT', 3001);
  await app.listen(port);

  logger.log(`🚀 空间信息系统服务已启动`);
  logger.log(`📍 服务地址: http://localhost:${port}`);
  logger.log(`📚 API文档: http://localhost:${port}/api/docs`);
  logger.log(`🗄️  数据库: ${configService.get('DB_HOST')}:${configService.get('DB_PORT')}/${configService.get('DB_DATABASE')}`);
  logger.log(`🌍 环境: ${configService.get('NODE_ENV', 'development')}`);
}

bootstrap().catch((error) => {
  console.error('启动失败:', error);
  process.exit(1);
});

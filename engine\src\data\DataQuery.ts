/**
 * 数据查询构建器
 * 提供链式查询API
 */
import { QueryCondition, QueryOptions } from './types';

export class DataQuery {
  private conditions: QueryCondition[] = [];
  private sortOptions: { field: string; order: 'asc' | 'desc' }[] = [];
  private limitValue?: number;
  private offsetValue?: number;
  private fieldsValue?: string[];

  /**
   * 添加等于条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public where(field: string, value: any): DataQuery {
    return this.addCondition(field, 'eq', value);
  }

  /**
   * 添加不等于条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public whereNot(field: string, value: any): DataQuery {
    return this.addCondition(field, 'ne', value);
  }

  /**
   * 添加大于条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public whereGreaterThan(field: string, value: any): DataQuery {
    return this.addCondition(field, 'gt', value);
  }

  /**
   * 添加大于等于条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public whereGreaterThanOrEqual(field: string, value: any): DataQuery {
    return this.addCondition(field, 'gte', value);
  }

  /**
   * 添加小于条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public whereLessThan(field: string, value: any): DataQuery {
    return this.addCondition(field, 'lt', value);
  }

  /**
   * 添加小于等于条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public whereLessThanOrEqual(field: string, value: any): DataQuery {
    return this.addCondition(field, 'lte', value);
  }

  /**
   * 添加包含条件
   * @param field 字段名
   * @param values 值数组
   * @returns 查询构建器
   */
  public whereIn(field: string, values: any[]): DataQuery {
    return this.addCondition(field, 'in', values);
  }

  /**
   * 添加不包含条件
   * @param field 字段名
   * @param values 值数组
   * @returns 查询构建器
   */
  public whereNotIn(field: string, values: any[]): DataQuery {
    return this.addCondition(field, 'nin', values);
  }

  /**
   * 添加字符串包含条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public whereContains(field: string, value: string): DataQuery {
    return this.addCondition(field, 'contains', value);
  }

  /**
   * 添加字符串开始条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public whereStartsWith(field: string, value: string): DataQuery {
    return this.addCondition(field, 'startsWith', value);
  }

  /**
   * 添加字符串结束条件
   * @param field 字段名
   * @param value 值
   * @returns 查询构建器
   */
  public whereEndsWith(field: string, value: string): DataQuery {
    return this.addCondition(field, 'endsWith', value);
  }

  /**
   * 添加排序
   * @param field 字段名
   * @param order 排序方向
   * @returns 查询构建器
   */
  public orderBy(field: string, order: 'asc' | 'desc' = 'asc'): DataQuery {
    this.sortOptions.push({ field, order });
    return this;
  }

  /**
   * 设置限制数量
   * @param count 数量
   * @returns 查询构建器
   */
  public limit(count: number): DataQuery {
    this.limitValue = count;
    return this;
  }

  /**
   * 设置偏移量
   * @param count 偏移量
   * @returns 查询构建器
   */
  public offset(count: number): DataQuery {
    this.offsetValue = count;
    return this;
  }

  /**
   * 选择字段
   * @param fields 字段数组
   * @returns 查询构建器
   */
  public select(fields: string[]): DataQuery {
    this.fieldsValue = fields;
    return this;
  }

  /**
   * 构建查询选项
   * @returns 查询选项
   */
  public build(): QueryOptions {
    return {
      conditions: this.conditions.length > 0 ? this.conditions : undefined,
      sort: this.sortOptions.length > 0 ? this.sortOptions : undefined,
      limit: this.limitValue,
      offset: this.offsetValue,
      fields: this.fieldsValue
    };
  }

  /**
   * 重置查询构建器
   * @returns 查询构建器
   */
  public reset(): DataQuery {
    this.conditions = [];
    this.sortOptions = [];
    this.limitValue = undefined;
    this.offsetValue = undefined;
    this.fieldsValue = undefined;
    return this;
  }

  /**
   * 添加条件
   * @param field 字段名
   * @param operator 操作符
   * @param value 值
   * @returns 查询构建器
   */
  private addCondition(field: string, operator: QueryCondition['operator'], value: any): DataQuery {
    this.conditions.push({ field, operator, value });
    return this;
  }

  /**
   * 创建新的查询构建器
   * @returns 查询构建器
   */
  public static create(): DataQuery {
    return new DataQuery();
  }
}

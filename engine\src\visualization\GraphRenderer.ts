/**
 * 图形渲染器
 * 提供节点图和网络图的渲染功能
 */
import { EventEmitter } from '../utils/EventEmitter';

export interface GraphNode {
  id: string;
  label: string;
  x?: number;
  y?: number;
  size?: number;
  color?: string;
  shape?: 'circle' | 'square' | 'triangle';
  metadata?: Record<string, any>;
}

export interface GraphEdge {
  id: string;
  source: string;
  target: string;
  weight?: number;
  color?: string;
  width?: number;
  label?: string;
  metadata?: Record<string, any>;
}

export interface GraphData {
  nodes: GraphNode[];
  edges: GraphEdge[];
}

export interface GraphConfig {
  width?: number;
  height?: number;
  backgroundColor?: string;
  nodeSize?: number;
  edgeWidth?: number;
  showLabels?: boolean;
  enablePhysics?: boolean;
  enableZoom?: boolean;
  enablePan?: boolean;
  layout?: 'force' | 'circular' | 'grid' | 'hierarchical';
}

export class GraphRenderer extends EventEmitter {
  private container: HTMLElement;
  private canvas: HTMLCanvasElement;
  private context: CanvasRenderingContext2D;
  private config: GraphConfig;
  private data: GraphData = { nodes: [], edges: [] };
  private isDestroyed: boolean = false;
  private animationId?: number;

  constructor(container: HTMLElement | string, config: GraphConfig = {}) {
    super();

    // 获取容器
    if (typeof container === 'string') {
      const element = document.getElementById(container);
      if (!element) {
        throw new Error(`Container element '${container}' not found`);
      }
      this.container = element;
    } else {
      this.container = container;
    }

    this.config = {
      width: 800,
      height: 600,
      backgroundColor: '#ffffff',
      nodeSize: 10,
      edgeWidth: 1,
      showLabels: true,
      enablePhysics: true,
      enableZoom: true,
      enablePan: true,
      layout: 'force',
      ...config
    };

    this.initialize();
  }

  /**
   * 设置图数据
   * @param data 图数据
   */
  public setData(data: GraphData): void {
    if (this.isDestroyed) {
      return;
    }

    this.data = data;
    this.applyLayout();
    this.render();
  }

  /**
   * 添加节点
   * @param node 节点
   */
  public addNode(node: GraphNode): void {
    if (this.isDestroyed) {
      return;
    }

    this.data.nodes.push(node);
    this.applyLayout();
    this.render();
  }

  /**
   * 添加边
   * @param edge 边
   */
  public addEdge(edge: GraphEdge): void {
    if (this.isDestroyed) {
      return;
    }

    this.data.edges.push(edge);
    this.render();
  }

  /**
   * 移除节点
   * @param nodeId 节点ID
   */
  public removeNode(nodeId: string): void {
    if (this.isDestroyed) {
      return;
    }

    this.data.nodes = this.data.nodes.filter(node => node.id !== nodeId);
    this.data.edges = this.data.edges.filter(edge => 
      edge.source !== nodeId && edge.target !== nodeId
    );
    this.render();
  }

  /**
   * 移除边
   * @param edgeId 边ID
   */
  public removeEdge(edgeId: string): void {
    if (this.isDestroyed) {
      return;
    }

    this.data.edges = this.data.edges.filter(edge => edge.id !== edgeId);
    this.render();
  }

  /**
   * 更新配置
   * @param config 新配置
   */
  public updateConfig(config: Partial<GraphConfig>): void {
    if (this.isDestroyed) {
      return;
    }

    this.config = { ...this.config, ...config };
    
    if (config.width || config.height) {
      this.canvas.width = this.config.width!;
      this.canvas.height = this.config.height!;
    }

    this.render();
  }

  /**
   * 获取节点
   * @param nodeId 节点ID
   * @returns 节点
   */
  public getNode(nodeId: string): GraphNode | undefined {
    return this.data.nodes.find(node => node.id === nodeId);
  }

  /**
   * 获取边
   * @param edgeId 边ID
   * @returns 边
   */
  public getEdge(edgeId: string): GraphEdge | undefined {
    return this.data.edges.find(edge => edge.id === edgeId);
  }

  /**
   * 销毁渲染器
   */
  public destroy(): void {
    if (this.isDestroyed) {
      return;
    }

    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
    }

    if (this.canvas && this.container) {
      this.container.removeChild(this.canvas);
    }

    this.removeAllListeners();
    this.isDestroyed = true;
  }

  /**
   * 初始化
   */
  private initialize(): void {
    this.createCanvas();
    this.setupInteractions();
    
    if (this.config.enablePhysics) {
      this.startAnimation();
    }
  }

  /**
   * 创建画布
   */
  private createCanvas(): void {
    this.canvas = document.createElement('canvas');
    this.canvas.width = this.config.width!;
    this.canvas.height = this.config.height!;
    
    const context = this.canvas.getContext('2d');
    if (!context) {
      throw new Error('Failed to get 2D context');
    }
    this.context = context;

    // 设置样式
    this.canvas.style.display = 'block';
    this.canvas.style.backgroundColor = this.config.backgroundColor!;

    // 添加到容器
    this.container.innerHTML = '';
    this.container.appendChild(this.canvas);
  }

  /**
   * 应用布局
   */
  private applyLayout(): void {
    switch (this.config.layout) {
      case 'force':
        this.applyForceLayout();
        break;
      case 'circular':
        this.applyCircularLayout();
        break;
      case 'grid':
        this.applyGridLayout();
        break;
      case 'hierarchical':
        this.applyHierarchicalLayout();
        break;
    }
  }

  /**
   * 应用力导向布局
   */
  private applyForceLayout(): void {
    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height / 2;

    // 简化的力导向布局
    this.data.nodes.forEach((node, index) => {
      if (node.x === undefined || node.y === undefined) {
        const angle = (index / this.data.nodes.length) * 2 * Math.PI;
        const radius = Math.min(centerX, centerY) * 0.5;
        node.x = centerX + Math.cos(angle) * radius;
        node.y = centerY + Math.sin(angle) * radius;
      }
    });
  }

  /**
   * 应用圆形布局
   */
  private applyCircularLayout(): void {
    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height / 2;
    const radius = Math.min(centerX, centerY) * 0.8;

    this.data.nodes.forEach((node, index) => {
      const angle = (index / this.data.nodes.length) * 2 * Math.PI;
      node.x = centerX + Math.cos(angle) * radius;
      node.y = centerY + Math.sin(angle) * radius;
    });
  }

  /**
   * 应用网格布局
   */
  private applyGridLayout(): void {
    const cols = Math.ceil(Math.sqrt(this.data.nodes.length));
    const rows = Math.ceil(this.data.nodes.length / cols);
    const cellWidth = this.canvas.width / cols;
    const cellHeight = this.canvas.height / rows;

    this.data.nodes.forEach((node, index) => {
      const col = index % cols;
      const row = Math.floor(index / cols);
      node.x = col * cellWidth + cellWidth / 2;
      node.y = row * cellHeight + cellHeight / 2;
    });
  }

  /**
   * 应用层次布局
   */
  private applyHierarchicalLayout(): void {
    // 简化的层次布局
    this.applyGridLayout();
  }

  /**
   * 渲染图形
   */
  private render(): void {
    if (!this.context) {
      return;
    }

    // 清空画布
    this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);

    // 设置背景
    this.context.fillStyle = this.config.backgroundColor!;
    this.context.fillRect(0, 0, this.canvas.width, this.canvas.height);

    // 渲染边
    this.renderEdges();

    // 渲染节点
    this.renderNodes();

    // 渲染标签
    if (this.config.showLabels) {
      this.renderLabels();
    }
  }

  /**
   * 渲染节点
   */
  private renderNodes(): void {
    this.data.nodes.forEach(node => {
      if (node.x === undefined || node.y === undefined) return;

      const size = node.size || this.config.nodeSize!;
      const color = node.color || '#3498db';
      const shape = node.shape || 'circle';

      this.context.fillStyle = color;
      this.context.strokeStyle = '#2c3e50';
      this.context.lineWidth = 2;

      switch (shape) {
        case 'circle':
          this.context.beginPath();
          this.context.arc(node.x, node.y, size, 0, 2 * Math.PI);
          this.context.fill();
          this.context.stroke();
          break;
        case 'square':
          this.context.fillRect(node.x - size, node.y - size, size * 2, size * 2);
          this.context.strokeRect(node.x - size, node.y - size, size * 2, size * 2);
          break;
        case 'triangle':
          this.context.beginPath();
          this.context.moveTo(node.x, node.y - size);
          this.context.lineTo(node.x - size, node.y + size);
          this.context.lineTo(node.x + size, node.y + size);
          this.context.closePath();
          this.context.fill();
          this.context.stroke();
          break;
      }
    });
  }

  /**
   * 渲染边
   */
  private renderEdges(): void {
    this.data.edges.forEach(edge => {
      const sourceNode = this.data.nodes.find(n => n.id === edge.source);
      const targetNode = this.data.nodes.find(n => n.id === edge.target);

      if (!sourceNode || !targetNode || 
          sourceNode.x === undefined || sourceNode.y === undefined ||
          targetNode.x === undefined || targetNode.y === undefined) {
        return;
      }

      const color = edge.color || '#95a5a6';
      const width = edge.width || this.config.edgeWidth!;

      this.context.strokeStyle = color;
      this.context.lineWidth = width;
      this.context.beginPath();
      this.context.moveTo(sourceNode.x, sourceNode.y);
      this.context.lineTo(targetNode.x, targetNode.y);
      this.context.stroke();
    });
  }

  /**
   * 渲染标签
   */
  private renderLabels(): void {
    this.context.fillStyle = '#2c3e50';
    this.context.font = '12px Arial';
    this.context.textAlign = 'center';
    this.context.textBaseline = 'middle';

    this.data.nodes.forEach(node => {
      if (node.x === undefined || node.y === undefined) return;

      const size = node.size || this.config.nodeSize!;
      this.context.fillText(node.label, node.x, node.y + size + 15);
    });
  }

  /**
   * 设置交互
   */
  private setupInteractions(): void {
    this.canvas.addEventListener('click', (event) => {
      const rect = this.canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      // 检查点击的节点
      const clickedNode = this.getNodeAtPosition(x, y);
      if (clickedNode) {
        this.emit('node-click', clickedNode);
      }
    });

    this.canvas.addEventListener('mousemove', (event) => {
      const rect = this.canvas.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;

      const hoveredNode = this.getNodeAtPosition(x, y);
      if (hoveredNode) {
        this.canvas.style.cursor = 'pointer';
        this.emit('node-hover', hoveredNode);
      } else {
        this.canvas.style.cursor = 'default';
      }
    });
  }

  /**
   * 获取指定位置的节点
   */
  private getNodeAtPosition(x: number, y: number): GraphNode | undefined {
    return this.data.nodes.find(node => {
      if (node.x === undefined || node.y === undefined) return false;
      
      const size = node.size || this.config.nodeSize!;
      const distance = Math.sqrt((x - node.x) ** 2 + (y - node.y) ** 2);
      return distance <= size;
    });
  }

  /**
   * 开始动画
   */
  private startAnimation(): void {
    const animate = () => {
      if (this.isDestroyed) return;

      // 简化的物理模拟
      this.updatePhysics();
      this.render();

      this.animationId = requestAnimationFrame(animate);
    };

    animate();
  }

  /**
   * 更新物理模拟
   */
  private updatePhysics(): void {
    // 简化的物理模拟实现
    // 实际应该包含力的计算、碰撞检测等
  }
}

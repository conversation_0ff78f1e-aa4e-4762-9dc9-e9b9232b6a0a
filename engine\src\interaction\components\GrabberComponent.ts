/**
 * 抓取者组件
 * 用于标记可以抓取其他对象的实体
 */
import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { EventEmitter, EventCallback } from '../../utils/EventEmitter';
import { Hand } from './GrabbableComponent';

/**
 * 抓取者组件配置
 */
export interface GrabberComponentConfig {
  /** 最大抓取距离 */
  maxGrabDistance?: number;
  /** 是否启用 */
  enabled?: boolean;
  /** 抓取回调 */
  onGrab?: (grabber: Entity, grabbed: Entity, hand: Hand) => void;
  /** 释放回调 */
  onRelease?: (grabber: Entity, released: Entity, hand: Hand) => void;
}

/**
 * 抓取者组件
 */
export class GrabberComponent extends Component {
  /** 组件类型 */
  public static readonly TYPE: string = 'GrabberComponent';

  /** 最大抓取距离 */
  private _maxGrabDistance: number;

  /** 左手抓取的实体 */
  private _leftHandGrabbed?: Entity;

  /** 右手抓取的实体 */
  private _rightHandGrabbed?: Entity;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /**
   * 构造函数
   * @param entity 关联的实体（可选，为了兼容旧的API）
   * @param config 组件配置
   */
  constructor(entity?: Entity | GrabberComponentConfig, config: GrabberComponentConfig = {}) {
    // 处理参数重载
    let actualConfig: GrabberComponentConfig;
    if (entity && typeof entity === 'object' && !('id' in entity)) {
      // 第一个参数是配置对象
      actualConfig = entity as GrabberComponentConfig;
    } else {
      // 第一个参数是实体或为空
      actualConfig = config;
    }

    // 调用基类构造函数，传入组件类型名称
    super(GrabberComponent.TYPE, {
      enabled: actualConfig.enabled,
      updatePriority: 0,
      serializable: true
    });

    // 如果传入了实体，设置实体引用
    if (entity && 'id' in entity) {
      this.setEntity(entity as Entity);
    }

    // 初始化属性
    this._maxGrabDistance = actualConfig.maxGrabDistance || 1.0;

    // 注册回调
    if (actualConfig.onGrab) {
      this.on('grab', actualConfig.onGrab);
    }

    if (actualConfig.onRelease) {
      this.on('release', actualConfig.onRelease);
    }
  }

  /**
   * 获取最大抓取距离
   */
  get maxGrabDistance(): number {
    return this._maxGrabDistance;
  }

  /**
   * 设置最大抓取距离
   */
  set maxGrabDistance(value: number) {
    this._maxGrabDistance = value;
  }



  /**
   * 获取左手抓取的实体
   */
  get leftHandGrabbed(): Entity | undefined {
    return this._leftHandGrabbed;
  }

  /**
   * 获取右手抓取的实体
   */
  get rightHandGrabbed(): Entity | undefined {
    return this._rightHandGrabbed;
  }

  /**
   * 获取指定手抓取的实体
   * @param hand 手
   * @returns 抓取的实体
   */
  getGrabbedEntity(hand: Hand): Entity | undefined {
    if (hand === Hand.LEFT) {
      return this._leftHandGrabbed;
    } else if (hand === Hand.RIGHT) {
      return this._rightHandGrabbed;
    }
    return undefined;
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  on(event: string, callback: EventCallback): this {
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  off(event: string, callback?: EventCallback): this {
    this.eventEmitter.off(event, callback);
    return this;
  }

  /**
   * 抓取实体
   * @param entity 要抓取的实体
   * @param hand 使用的手
   * @returns 是否抓取成功
   */
  grab(entity: Entity, hand: Hand): boolean {
    // 如果未启用，则返回失败
    if (!this.enabled) {
      return false;
    }

    // 检查指定的手是否已经抓取了其他实体
    if ((hand === Hand.LEFT && this._leftHandGrabbed) ||
        (hand === Hand.RIGHT && this._rightHandGrabbed)) {
      return false;
    }

    // 获取可抓取组件
    const grabbableComponent = entity.getComponent('GrabbableComponent') as any as any;
    if (!grabbableComponent) {
      return false;
    }

    // 尝试抓取
    const success = grabbableComponent.grab(this.entity, hand);
    if (success) {
      // 更新抓取状态
      if (hand === Hand.LEFT) {
        this._leftHandGrabbed = entity;
      } else if (hand === Hand.RIGHT) {
        this._rightHandGrabbed = entity;
      }

      // 触发抓取事件
      this.eventEmitter.emit('grab', this.entity, entity, hand);
    }

    return success;
  }

  /**
   * 释放实体
   * @param hand 使用的手
   * @returns 是否释放成功
   */
  release(hand: Hand): boolean {
    // 获取指定手抓取的实体
    const entity = this.getGrabbedEntity(hand);
    if (!entity) {
      return false;
    }

    // 获取可抓取组件
    const grabbableComponent = entity.getComponent('GrabbableComponent') as any as any;
    if (!grabbableComponent) {
      return false;
    }

    // 尝试释放
    const success = grabbableComponent.release();
    if (success) {
      // 更新抓取状态
      if (hand === Hand.LEFT) {
        this._leftHandGrabbed = undefined;
      } else if (hand === Hand.RIGHT) {
        this._rightHandGrabbed = undefined;
      }

      // 触发释放事件
      this.eventEmitter.emit('release', this.entity, entity, hand);
    }

    return success;
  }

  /**
   * 创建组件实例
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new GrabberComponent();
  }

  /**
   * 序列化自定义数据
   * @returns 自定义数据
   */
  protected serializeData(): any {
    return {
      maxGrabDistance: this._maxGrabDistance,
      leftHandGrabbed: this._leftHandGrabbed?.id,
      rightHandGrabbed: this._rightHandGrabbed?.id
    };
  }

  /**
   * 反序列化自定义数据
   * @param data 自定义数据
   */
  protected deserializeData(data: any): void {
    if (data.maxGrabDistance !== undefined) this._maxGrabDistance = data.maxGrabDistance;
    // 注意：抓取的实体需要在反序列化时重新设置实体引用
  }
}

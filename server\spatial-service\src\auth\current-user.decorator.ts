/**
 * 当前用户装饰器
 */
import { createParamDecorator, ExecutionContext } from '@nestjs/common';

export const CurrentUser = createParamDecorator(
  (data: unknown, ctx: ExecutionContext) => {
    const request = ctx.switchToHttp().getRequest();
    return request.user;
  },
);

export const Public = () => {
  const Reflector = require('@nestjs/core').Reflector;
  return Reflector.createDecorator<boolean>({ key: 'isPublic' });
};

/**
 * 空间分析引擎
 * 提供各种空间分析算法和工具
 */
import * as THREE from 'three';
import { GeographicCoordinate } from '../coordinate/CoordinateSystem';
import { GeospatialComponent } from '../components/GeospatialComponent';

/**
 * 空间关系类型
 */
export enum SpatialRelationType {
  INTERSECTS = 'intersects',
  CONTAINS = 'contains',
  WITHIN = 'within',
  TOUCHES = 'touches',
  CROSSES = 'crosses',
  OVERLAPS = 'overlaps',
  DISJOINT = 'disjoint'
}

/**
 * 缓冲区分析参数
 */
export interface BufferAnalysisParams {
  distance: number;
  unit: 'meters' | 'kilometers' | 'degrees';
  segments?: number;
  endCapStyle?: 'round' | 'flat' | 'square';
  joinStyle?: 'round' | 'mitre' | 'bevel';
}

/**
 * 空间查询参数
 */
export interface SpatialQueryParams {
  geometry: any; // GeoJSON几何
  relation: SpatialRelationType;
  buffer?: number;
  tolerance?: number;
}

/**
 * 空间分析结果
 */
export interface SpatialAnalysisResult {
  success: boolean;
  result?: any;
  error?: string;
  metadata?: {
    processingTime: number;
    inputCount: number;
    outputCount: number;
  };
}

/**
 * 空间分析引擎
 */
export class SpatialAnalysisEngine {
  private static instance: SpatialAnalysisEngine;
  
  static getInstance(): SpatialAnalysisEngine {
    if (!this.instance) {
      this.instance = new SpatialAnalysisEngine();
    }
    return this.instance;
  }
  
  /**
   * 缓冲区分析
   * @param geometry 输入几何
   * @param params 缓冲区参数
   * @returns 缓冲区几何
   */
  buffer(geometry: any, params: BufferAnalysisParams): SpatialAnalysisResult {
    const startTime = Date.now();
    
    try {
      let bufferGeometry: any;
      
      switch (geometry.type) {
        case 'Point':
          bufferGeometry = this.bufferPoint(geometry, params);
          break;
        case 'LineString':
          bufferGeometry = this.bufferLineString(geometry, params);
          break;
        case 'Polygon':
          bufferGeometry = this.bufferPolygon(geometry, params);
          break;
        default:
          throw new Error(`不支持的几何类型: ${geometry.type}`);
      }
      
      return {
        success: true,
        result: bufferGeometry,
        metadata: {
          processingTime: Date.now() - startTime,
          inputCount: 1,
          outputCount: 1
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '缓冲区分析失败',
        metadata: {
          processingTime: Date.now() - startTime,
          inputCount: 1,
          outputCount: 0
        }
      };
    }
  }
  
  /**
   * 点缓冲区
   */
  private bufferPoint(point: any, params: BufferAnalysisParams): any {
    const [lon, lat] = point.coordinates;
    const distance = this.convertDistanceToMeters(params.distance, params.unit);
    const segments = params.segments || 32;
    
    // 将距离转换为度数（近似）
    const degreeDistance = distance / 111320; // 1度约等于111320米
    
    const coordinates: number[][] = [];
    
    for (let i = 0; i <= segments; i++) {
      const angle = (i / segments) * 2 * Math.PI;
      const x = lon + degreeDistance * Math.cos(angle);
      const y = lat + degreeDistance * Math.sin(angle);
      coordinates.push([x, y]);
    }
    
    return {
      type: 'Polygon',
      coordinates: [coordinates]
    };
  }
  
  /**
   * 线缓冲区
   */
  private bufferLineString(lineString: any, params: BufferAnalysisParams): any {
    const distance = this.convertDistanceToMeters(params.distance, params.unit);
    const degreeDistance = distance / 111320;
    
    const coordinates = lineString.coordinates;
    const leftSide: number[][] = [];
    const rightSide: number[][] = [];
    
    for (let i = 0; i < coordinates.length; i++) {
      const [lon, lat] = coordinates[i];
      
      // 计算垂直方向
      let perpAngle: number;
      if (i === 0) {
        // 第一个点，使用到下一个点的方向
        const [nextLon, nextLat] = coordinates[i + 1];
        const angle = Math.atan2(nextLat - lat, nextLon - lon);
        perpAngle = angle + Math.PI / 2;
      } else if (i === coordinates.length - 1) {
        // 最后一个点，使用从上一个点的方向
        const [prevLon, prevLat] = coordinates[i - 1];
        const angle = Math.atan2(lat - prevLat, lon - prevLon);
        perpAngle = angle + Math.PI / 2;
      } else {
        // 中间点，使用平均方向
        const [prevLon, prevLat] = coordinates[i - 1];
        const [nextLon, nextLat] = coordinates[i + 1];
        const angle1 = Math.atan2(lat - prevLat, lon - prevLon);
        const angle2 = Math.atan2(nextLat - lat, nextLon - lon);
        perpAngle = (angle1 + angle2) / 2 + Math.PI / 2;
      }
      
      const leftX = lon + degreeDistance * Math.cos(perpAngle);
      const leftY = lat + degreeDistance * Math.sin(perpAngle);
      const rightX = lon - degreeDistance * Math.cos(perpAngle);
      const rightY = lat - degreeDistance * Math.sin(perpAngle);
      
      leftSide.push([leftX, leftY]);
      rightSide.unshift([rightX, rightY]); // 反向添加右侧点
    }
    
    // 连接左右两侧形成多边形
    const bufferCoordinates = [...leftSide, ...rightSide, leftSide[0]];
    
    return {
      type: 'Polygon',
      coordinates: [bufferCoordinates]
    };
  }
  
  /**
   * 多边形缓冲区
   */
  private bufferPolygon(polygon: any, params: BufferAnalysisParams): any {
    // 简化实现：对多边形的每个顶点进行缓冲
    const distance = this.convertDistanceToMeters(params.distance, params.unit);
    const degreeDistance = distance / 111320;
    
    const originalCoords = polygon.coordinates[0];
    const bufferedCoords: number[][] = [];
    
    for (let i = 0; i < originalCoords.length - 1; i++) {
      const [lon, lat] = originalCoords[i];
      
      // 简单的外扩处理
      const expandedLon = lon + (lon > 0 ? degreeDistance : -degreeDistance);
      const expandedLat = lat + (lat > 0 ? degreeDistance : -degreeDistance);
      
      bufferedCoords.push([expandedLon, expandedLat]);
    }
    
    // 闭合多边形
    bufferedCoords.push(bufferedCoords[0]);
    
    return {
      type: 'Polygon',
      coordinates: [bufferedCoords]
    };
  }
  
  /**
   * 相交分析
   */
  intersection(geometry1: any, geometry2: any): SpatialAnalysisResult {
    const startTime = Date.now();
    
    try {
      const result = this.computeIntersection(geometry1, geometry2);
      
      return {
        success: true,
        result,
        metadata: {
          processingTime: Date.now() - startTime,
          inputCount: 2,
          outputCount: result ? 1 : 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '相交分析失败',
        metadata: {
          processingTime: Date.now() - startTime,
          inputCount: 2,
          outputCount: 0
        }
      };
    }
  }
  
  /**
   * 计算两个几何的相交
   */
  private computeIntersection(geometry1: any, geometry2: any): any {
    // 简化实现：仅处理点与多边形的相交
    if (geometry1.type === 'Point' && geometry2.type === 'Polygon') {
      const point = geometry1.coordinates;
      const polygon = geometry2.coordinates[0];
      
      if (this.pointInPolygon(point, polygon)) {
        return geometry1; // 点在多边形内，返回点
      }
    }
    
    return null; // 无相交
  }
  
  /**
   * 联合分析
   */
  union(geometries: any[]): SpatialAnalysisResult {
    const startTime = Date.now();
    
    try {
      // 简化实现：返回所有几何的集合
      const result = {
        type: 'GeometryCollection',
        geometries: geometries
      };
      
      return {
        success: true,
        result,
        metadata: {
          processingTime: Date.now() - startTime,
          inputCount: geometries.length,
          outputCount: 1
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '联合分析失败',
        metadata: {
          processingTime: Date.now() - startTime,
          inputCount: geometries.length,
          outputCount: 0
        }
      };
    }
  }
  
  /**
   * 差异分析
   */
  difference(geometry1: any, geometry2: any): SpatialAnalysisResult {
    const startTime = Date.now();
    
    try {
      // 简化实现：如果两个几何相交，返回null，否则返回第一个几何
      const intersection = this.computeIntersection(geometry1, geometry2);
      const result = intersection ? null : geometry1;
      
      return {
        success: true,
        result,
        metadata: {
          processingTime: Date.now() - startTime,
          inputCount: 2,
          outputCount: result ? 1 : 0
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '差异分析失败',
        metadata: {
          processingTime: Date.now() - startTime,
          inputCount: 2,
          outputCount: 0
        }
      };
    }
  }
  
  /**
   * 点在多边形内判断
   */
  pointInPolygon(point: number[], polygon: number[][]): boolean {
    const [x, y] = point;
    let inside = false;
    
    for (let i = 0, j = polygon.length - 1; i < polygon.length; j = i++) {
      const [xi, yi] = polygon[i];
      const [xj, yj] = polygon[j];
      
      if (((yi > y) !== (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
        inside = !inside;
      }
    }
    
    return inside;
  }
  
  /**
   * 计算两点间距离（Haversine公式）
   */
  haversineDistance(coord1: GeographicCoordinate, coord2: GeographicCoordinate): number {
    const R = 6371000; // 地球半径（米）
    const φ1 = coord1.latitude * Math.PI / 180;
    const φ2 = coord2.latitude * Math.PI / 180;
    const Δφ = (coord2.latitude - coord1.latitude) * Math.PI / 180;
    const Δλ = (coord2.longitude - coord1.longitude) * Math.PI / 180;
    
    const a = Math.sin(Δφ/2) * Math.sin(Δφ/2) +
              Math.cos(φ1) * Math.cos(φ2) *
              Math.sin(Δλ/2) * Math.sin(Δλ/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    
    return R * c;
  }
  
  /**
   * 计算多边形面积
   */
  polygonArea(coordinates: GeographicCoordinate[]): number {
    if (coordinates.length < 3) {
      return 0;
    }
    
    let area = 0;
    const n = coordinates.length;
    
    for (let i = 0; i < n; i++) {
      const j = (i + 1) % n;
      const xi = coordinates[i].longitude * Math.PI / 180;
      const yi = coordinates[i].latitude * Math.PI / 180;
      const xj = coordinates[j].longitude * Math.PI / 180;
      const yj = coordinates[j].latitude * Math.PI / 180;
      
      area += xi * yj - xj * yi;
    }
    
    area = Math.abs(area) / 2;
    
    // 转换为平方米（近似）
    const R = 6371000; // 地球半径
    return area * R * R;
  }
  
  /**
   * 线段相交判断
   */
  lineIntersection(
    line1: [number[], number[]], 
    line2: [number[], number[]]
  ): number[] | null {
    const [[x1, y1], [x2, y2]] = line1;
    const [[x3, y3], [x4, y4]] = line2;
    
    const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
    
    if (Math.abs(denom) < 1e-10) {
      return null; // 平行线
    }
    
    const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
    const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;
    
    if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
      return [
        x1 + t * (x2 - x1),
        y1 + t * (y2 - y1)
      ];
    }
    
    return null;
  }
  
  /**
   * 空间查询
   */
  spatialQuery(
    features: GeospatialComponent[], 
    params: SpatialQueryParams
  ): GeospatialComponent[] {
    const results: GeospatialComponent[] = [];
    
    for (const feature of features) {
      if (this.testSpatialRelation(feature.getGeometry(), params.geometry, params.relation)) {
        results.push(feature);
      }
    }
    
    return results;
  }
  
  /**
   * 测试空间关系
   */
  private testSpatialRelation(geometry1: any, geometry2: any, relation: SpatialRelationType): boolean {
    switch (relation) {
      case SpatialRelationType.INTERSECTS:
        return this.computeIntersection(geometry1, geometry2) !== null;
      case SpatialRelationType.CONTAINS:
        return this.testContains(geometry1, geometry2);
      case SpatialRelationType.WITHIN:
        return this.testContains(geometry2, geometry1);
      default:
        return false;
    }
  }
  
  /**
   * 测试包含关系
   */
  private testContains(container: any, contained: any): boolean {
    if (container.type === 'Polygon' && contained.type === 'Point') {
      return this.pointInPolygon(contained.coordinates, container.coordinates[0]);
    }
    return false;
  }
  
  /**
   * 转换距离单位为米
   */
  private convertDistanceToMeters(distance: number, unit: string): number {
    switch (unit) {
      case 'kilometers':
        return distance * 1000;
      case 'degrees':
        return distance * 111320; // 1度约等于111320米
      case 'meters':
      default:
        return distance;
    }
  }
}

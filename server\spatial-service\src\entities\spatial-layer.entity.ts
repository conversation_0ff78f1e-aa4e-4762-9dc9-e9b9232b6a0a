/**
 * 空间图层实体
 */
import { 
  En<PERSON>ty, 
  PrimaryGeneratedColumn, 
  Column, 
  OneToMany, 
  ManyToOne, 
  JoinColumn, 
  CreateDateColumn, 
  UpdateDateColumn,
  Index
} from 'typeorm';
import { SpatialFeature } from './spatial-feature.entity';
import { SpatialProject } from './spatial-project.entity';

@Entity('spatial_layers')
@Index(['projectId'])
@Index(['layerType'])
@Index(['visible'])
@Index(['createdAt'])
export class SpatialLayer {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ 
    type: 'enum',
    enum: ['vector', 'raster', 'tile', 'wms', 'wmts'],
    name: 'layer_type'
  })
  layerType: 'vector' | 'raster' | 'tile' | 'wms' | 'wmts';

  @Column({ type: 'jsonb', nullable: true })
  metadata: {
    crs?: string;
    extent?: [number, number, number, number]; // [minX, minY, maxX, maxY]
    fields?: Array<{
      name: string;
      type: 'string' | 'number' | 'boolean' | 'date' | 'geometry';
      description?: string;
      required?: boolean;
      defaultValue?: any;
    }>;
    source?: {
      type: 'file' | 'database' | 'service' | 'api';
      url?: string;
      connection?: string;
      query?: string;
      format?: string;
    };
    statistics?: {
      featureCount?: number;
      totalArea?: number;
      totalLength?: number;
      minZoom?: number;
      maxZoom?: number;
      lastUpdated?: string;
    };
    [key: string]: any;
  };

  @Column({ type: 'jsonb', nullable: true })
  style: {
    default?: {
      color?: string;
      fillColor?: string;
      weight?: number;
      opacity?: number;
      fillOpacity?: number;
      radius?: number;
      strokeWidth?: number;
      strokeColor?: string;
      strokeOpacity?: number;
      iconUrl?: string;
      iconSize?: [number, number];
    };
    rules?: Array<{
      condition: string; // SQL条件表达式
      style: any;
      label?: string;
      description?: string;
    }>;
    legend?: {
      title?: string;
      position?: 'topright' | 'topleft' | 'bottomright' | 'bottomleft';
      items?: Array<{
        label: string;
        color?: string;
        symbol?: string;
      }>;
    };
  };

  @OneToMany(() => SpatialFeature, feature => feature.layer, { cascade: true })
  features: SpatialFeature[];

  @ManyToOne(() => SpatialProject, project => project.layers, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'project_id' })
  project: SpatialProject;

  @Column({ name: 'project_id' })
  projectId: string;

  @Column({ type: 'boolean', default: true })
  visible: boolean;

  @Column({ type: 'boolean', default: true })
  editable: boolean;

  @Column({ type: 'boolean', default: true })
  selectable: boolean;

  @Column({ type: 'int', default: 0 })
  zIndex: number;

  @Column({ type: 'float', default: 1.0 })
  opacity: number;

  @Column({ type: 'int', nullable: true })
  minZoom: number;

  @Column({ type: 'int', nullable: true })
  maxZoom: number;

  @Column({ type: 'jsonb', nullable: true })
  permissions: {
    read?: string[]; // 用户ID或角色列表
    write?: string[];
    delete?: string[];
    admin?: string[];
  };

  @Column({ type: 'jsonb', nullable: true })
  cache: {
    enabled?: boolean;
    ttl?: number; // 缓存时间（秒）
    strategy?: 'memory' | 'redis' | 'file';
    maxSize?: number;
  };

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'created_by' })
  createdBy: string;

  @Column({ name: 'updated_by', nullable: true })
  updatedBy: string;

  // 计算属性
  get featureCount(): number {
    return this.features ? this.features.length : 0;
  }

  get extent(): [number, number, number, number] | null {
    // 这里应该使用PostGIS函数计算图层范围
    // 在实际查询中通过 ST_Extent(geometry) 计算
    return this.metadata?.extent || null;
  }

  get totalArea(): number {
    // 这里应该使用PostGIS函数计算总面积
    // 在实际查询中通过 SUM(ST_Area(geometry)) 计算
    return this.metadata?.statistics?.totalArea || 0;
  }

  get totalLength(): number {
    // 这里应该使用PostGIS函数计算总长度
    // 在实际查询中通过 SUM(ST_Length(geometry)) 计算
    return this.metadata?.statistics?.totalLength || 0;
  }

  // 辅助方法
  toGeoJSON(): any {
    return {
      type: 'FeatureCollection',
      metadata: {
        id: this.id,
        name: this.name,
        description: this.description,
        layerType: this.layerType,
        metadata: this.metadata,
        style: this.style,
        visible: this.visible,
        editable: this.editable,
        selectable: this.selectable,
        zIndex: this.zIndex,
        opacity: this.opacity,
        minZoom: this.minZoom,
        maxZoom: this.maxZoom,
        createdAt: this.createdAt,
        updatedAt: this.updatedAt,
        createdBy: this.createdBy,
        updatedBy: this.updatedBy
      },
      features: this.features ? this.features.map(feature => feature.toGeoJSON()) : []
    };
  }

  // 获取图层类型的中文名称
  getLayerTypeName(): string {
    const typeNames = {
      'vector': '矢量图层',
      'raster': '栅格图层',
      'tile': '瓦片图层',
      'wms': 'WMS服务',
      'wmts': 'WMTS服务'
    };
    return typeNames[this.layerType] || this.layerType;
  }

  // 检查用户权限
  hasPermission(userId: string, action: 'read' | 'write' | 'delete' | 'admin'): boolean {
    if (!this.permissions || !this.permissions[action]) {
      return true; // 如果没有设置权限，默认允许
    }

    return this.permissions[action]!.includes(userId);
  }

  // 添加要素
  addFeature(feature: SpatialFeature): void {
    if (!this.features) {
      this.features = [];
    }
    feature.layerId = this.id;
    this.features.push(feature);
    this.updateStatistics();
  }

  // 移除要素
  removeFeature(featureId: string): boolean {
    if (!this.features) {
      return false;
    }

    const index = this.features.findIndex(f => f.id === featureId);
    if (index >= 0) {
      this.features.splice(index, 1);
      this.updateStatistics();
      return true;
    }
    return false;
  }

  // 更新统计信息
  updateStatistics(): void {
    if (!this.metadata) {
      this.metadata = {};
    }
    if (!this.metadata.statistics) {
      this.metadata.statistics = {};
    }

    this.metadata.statistics.featureCount = this.featureCount;
    this.metadata.statistics.lastUpdated = new Date().toISOString();

    // 计算范围（这里是简化实现，实际应该使用PostGIS）
    if (this.features && this.features.length > 0) {
      let minX = Infinity, minY = Infinity, maxX = -Infinity, maxY = -Infinity;
      
      this.features.forEach(feature => {
        if (feature.geometry && feature.geometry.coordinates) {
          const coords = this.extractCoordinates(feature.geometry);
          coords.forEach(coord => {
            minX = Math.min(minX, coord[0]);
            minY = Math.min(minY, coord[1]);
            maxX = Math.max(maxX, coord[0]);
            maxY = Math.max(maxY, coord[1]);
          });
        }
      });

      if (isFinite(minX)) {
        this.metadata.extent = [minX, minY, maxX, maxY];
      }
    }
  }

  // 提取所有坐标点
  private extractCoordinates(geometry: any): number[][] {
    const coords: number[][] = [];

    const extract = (geom: any) => {
      if (!geom || !geom.coordinates) return;

      switch (geom.type) {
        case 'Point':
          coords.push(geom.coordinates);
          break;
        case 'LineString':
        case 'MultiPoint':
          geom.coordinates.forEach((coord: number[]) => coords.push(coord));
          break;
        case 'Polygon':
        case 'MultiLineString':
          geom.coordinates.forEach((ring: number[][]) => 
            ring.forEach((coord: number[]) => coords.push(coord))
          );
          break;
        case 'MultiPolygon':
          geom.coordinates.forEach((polygon: number[][][]) =>
            polygon.forEach((ring: number[][]) =>
              ring.forEach((coord: number[]) => coords.push(coord))
            )
          );
          break;
      }
    };

    extract(geometry);
    return coords;
  }

  // 获取样式规则
  getStyleForFeature(feature: SpatialFeature): any {
    if (!this.style || !this.style.rules) {
      return this.style?.default || {};
    }

    // 查找匹配的样式规则
    for (const rule of this.style.rules) {
      if (this.evaluateCondition(rule.condition, feature)) {
        return { ...this.style.default, ...rule.style };
      }
    }

    return this.style.default || {};
  }

  // 评估条件表达式（简化实现）
  private evaluateCondition(condition: string, feature: SpatialFeature): boolean {
    try {
      // 这里应该实现一个安全的表达式评估器
      // 目前只支持简单的属性比较
      const match = condition.match(/(\w+)\s*(=|!=|>|<|>=|<=)\s*(.+)/);
      if (!match) return false;

      const [, property, operator, value] = match;
      const featureValue = feature.properties?.[property];
      const compareValue = isNaN(Number(value)) ? value.replace(/['"]/g, '') : Number(value);

      switch (operator) {
        case '=': return featureValue == compareValue;
        case '!=': return featureValue != compareValue;
        case '>': return Number(featureValue) > Number(compareValue);
        case '<': return Number(featureValue) < Number(compareValue);
        case '>=': return Number(featureValue) >= Number(compareValue);
        case '<=': return Number(featureValue) <= Number(compareValue);
        default: return false;
      }
    } catch (error) {
      return false;
    }
  }

  // 验证图层配置
  validate(): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.name || this.name.trim().length === 0) {
      errors.push('图层名称不能为空');
    }

    if (!this.layerType) {
      errors.push('图层类型不能为空');
    }

    if (this.opacity < 0 || this.opacity > 1) {
      errors.push('透明度必须在0-1之间');
    }

    if (this.minZoom !== null && this.maxZoom !== null && this.minZoom > this.maxZoom) {
      errors.push('最小缩放级别不能大于最大缩放级别');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // 克隆图层
  clone(newName?: string): Partial<SpatialLayer> {
    return {
      name: newName || `${this.name}_copy`,
      description: this.description,
      layerType: this.layerType,
      metadata: JSON.parse(JSON.stringify(this.metadata)),
      style: JSON.parse(JSON.stringify(this.style)),
      visible: this.visible,
      editable: this.editable,
      selectable: this.selectable,
      zIndex: this.zIndex,
      opacity: this.opacity,
      minZoom: this.minZoom,
      maxZoom: this.maxZoom,
      permissions: JSON.parse(JSON.stringify(this.permissions)),
      cache: JSON.parse(JSON.stringify(this.cache))
    };
  }
}

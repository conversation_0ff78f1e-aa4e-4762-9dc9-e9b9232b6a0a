/**
 * 访问控制
 * 管理用户权限和资源访问
 */
import { EventEmitter } from '../utils/EventEmitter';
import { 
  AccessRule, 
  Session, 
  Permission, 
  UserRole, 
  AccessCondition 
} from './types';

export class AccessControl extends EventEmitter {
  private rules: Map<string, AccessRule> = new Map();
  private isDestroyed: boolean = false;

  constructor() {
    super();
    this.initializeDefaultRules();
  }

  /**
   * 添加访问规则
   * @param rule 访问规则
   */
  public addRule(rule: AccessRule): void {
    if (this.isDestroyed) {
      return;
    }

    this.rules.set(rule.id, rule);
    this.emit('rule-added', rule);
  }

  /**
   * 移除访问规则
   * @param ruleId 规则ID
   */
  public removeRule(ruleId: string): void {
    if (this.isDestroyed) {
      return;
    }

    const rule = this.rules.get(ruleId);
    if (rule) {
      this.rules.delete(ruleId);
      this.emit('rule-removed', rule);
    }
  }

  /**
   * 更新访问规则
   * @param ruleId 规则ID
   * @param updates 更新内容
   */
  public updateRule(ruleId: string, updates: Partial<AccessRule>): void {
    if (this.isDestroyed) {
      return;
    }

    const rule = this.rules.get(ruleId);
    if (rule) {
      Object.assign(rule, updates);
      this.emit('rule-updated', rule);
    }
  }

  /**
   * 检查权限
   * @param session 会话信息
   * @param resource 资源
   * @param action 动作
   * @returns 是否有权限
   */
  public async checkPermission(session: Session, resource: string, action: string): Promise<boolean> {
    if (this.isDestroyed) {
      return false;
    }

    try {
      // 获取适用的规则
      const applicableRules = this.getApplicableRules(resource, action as Permission);
      
      // 按优先级排序
      applicableRules.sort((a, b) => b.priority - a.priority);

      // 检查每个规则
      for (const rule of applicableRules) {
        if (!rule.isActive) {
          continue;
        }

        // 检查角色匹配
        const hasRole = this.checkRoleMatch(session.roles, rule.roles);
        if (!hasRole) {
          continue;
        }

        // 检查条件
        const conditionsMet = await this.checkConditions(session, rule.conditions || []);
        if (!conditionsMet) {
          continue;
        }

        // 权限检查通过
        this.emit('access-granted', {
          session,
          resource,
          action,
          rule
        });
        return true;
      }

      // 没有匹配的规则，拒绝访问
      this.emit('access-denied', {
        session,
        resource,
        action,
        reason: 'no_matching_rule'
      });
      return false;

    } catch (error) {
      this.emit('access-error', {
        session,
        resource,
        action,
        error
      });
      return false;
    }
  }

  /**
   * 获取用户权限
   * @param session 会话信息
   * @param resource 资源（可选）
   * @returns 权限列表
   */
  public getUserPermissions(session: Session, resource?: string): Permission[] {
    if (this.isDestroyed) {
      return [];
    }

    const permissions = new Set<Permission>();

    // 添加会话中的直接权限
    session.permissions.forEach(permission => {
      permissions.add(permission as Permission);
    });

    // 从规则中获取权限
    this.rules.forEach(rule => {
      if (!rule.isActive) {
        return;
      }

      // 检查资源匹配
      if (resource && !this.matchResource(resource, rule.resource)) {
        return;
      }

      // 检查角色匹配
      if (this.checkRoleMatch(session.roles, rule.roles)) {
        permissions.add(rule.action);
      }
    });

    return Array.from(permissions);
  }

  /**
   * 获取所有规则
   * @returns 规则列表
   */
  public getAllRules(): AccessRule[] {
    return Array.from(this.rules.values());
  }

  /**
   * 获取规则
   * @param ruleId 规则ID
   * @returns 规则
   */
  public getRule(ruleId: string): AccessRule | undefined {
    return this.rules.get(ruleId);
  }

  /**
   * 清空所有规则
   */
  public clearRules(): void {
    if (this.isDestroyed) {
      return;
    }

    this.rules.clear();
    this.emit('rules-cleared');
  }

  /**
   * 销毁访问控制
   */
  public async destroy(): Promise<void> {
    if (this.isDestroyed) {
      return;
    }

    this.rules.clear();
    this.removeAllListeners();
    this.isDestroyed = true;
  }

  /**
   * 初始化默认规则
   */
  private initializeDefaultRules(): void {
    // 管理员全权限
    this.addRule({
      id: 'admin-full-access',
      resource: '*',
      action: Permission.ADMIN,
      roles: [UserRole.ADMIN, UserRole.SUPER_ADMIN],
      priority: 1000,
      isActive: true
    });

    // 用户基本读权限
    this.addRule({
      id: 'user-read-access',
      resource: 'public/*',
      action: Permission.READ,
      roles: [UserRole.USER, UserRole.MODERATOR, UserRole.ADMIN, UserRole.SUPER_ADMIN],
      priority: 100,
      isActive: true
    });

    // 访客只读权限
    this.addRule({
      id: 'guest-read-access',
      resource: 'public/guest/*',
      action: Permission.READ,
      roles: [UserRole.GUEST],
      priority: 50,
      isActive: true
    });
  }

  /**
   * 获取适用的规则
   */
  private getApplicableRules(resource: string, action: Permission): AccessRule[] {
    return Array.from(this.rules.values()).filter(rule => {
      return this.matchResource(resource, rule.resource) && 
             (rule.action === action || rule.action === Permission.ADMIN);
    });
  }

  /**
   * 匹配资源
   */
  private matchResource(resource: string, pattern: string): boolean {
    if (pattern === '*') {
      return true;
    }

    if (pattern.endsWith('/*')) {
      const prefix = pattern.slice(0, -2);
      return resource.startsWith(prefix);
    }

    return resource === pattern;
  }

  /**
   * 检查角色匹配
   */
  private checkRoleMatch(userRoles: UserRole[], requiredRoles: UserRole[]): boolean {
    return requiredRoles.some(role => userRoles.includes(role));
  }

  /**
   * 检查条件
   */
  private async checkConditions(session: Session, conditions: AccessCondition[]): Promise<boolean> {
    for (const condition of conditions) {
      const result = await this.evaluateCondition(session, condition);
      if (!result) {
        return false;
      }
    }
    return true;
  }

  /**
   * 评估条件
   */
  private async evaluateCondition(session: Session, condition: AccessCondition): Promise<boolean> {
    try {
      let actualValue: any;

      switch (condition.type) {
        case 'time':
          actualValue = new Date();
          break;
        case 'location':
          actualValue = session.ipAddress;
          break;
        case 'device':
          actualValue = session.userAgent;
          break;
        case 'custom':
          actualValue = session.metadata?.[condition.type];
          break;
        default:
          return true;
      }

      return this.evaluateConditionOperator(actualValue, condition.operator, condition.value);
    } catch (error) {
      return false;
    }
  }

  /**
   * 评估条件操作符
   */
  private evaluateConditionOperator(actualValue: any, operator: string, expectedValue: any): boolean {
    switch (operator) {
      case 'eq':
        return actualValue === expectedValue;
      case 'ne':
        return actualValue !== expectedValue;
      case 'in':
        return Array.isArray(expectedValue) && expectedValue.includes(actualValue);
      case 'nin':
        return Array.isArray(expectedValue) && !expectedValue.includes(actualValue);
      case 'gt':
        return actualValue > expectedValue;
      case 'lt':
        return actualValue < expectedValue;
      case 'between':
        return Array.isArray(expectedValue) && 
               actualValue >= expectedValue[0] && 
               actualValue <= expectedValue[1];
      default:
        return true;
    }
  }
}

/**
 * InteractableComponent.ts
 *
 * 可交互组件，用于标记可交互的对象
 */

import { Component } from '../../core/Component';
import type { Entity } from '../../core/Entity';
import { Vector3, MeshBasicMaterial } from 'three';
import { EventEmitter, EventCallback } from '../../utils/EventEmitter';
import type { Transform } from '../../scene/Transform';

/**
 * 交互类型枚举
 */
export enum InteractionType {
  /** 点击交互 */
  CLICK = 'click',
  /** 接近交互 */
  PROXIMITY = 'proximity',
  /** 悬停交互 */
  HOVER = 'hover'
}

/**
 * 交互回调函数类型
 */
export type InteractionCallback = (entity: Entity) => void;

/**
 * 可交互组件配置
 */
export interface InteractableComponentConfig {
  /** 交互类型 */
  interactionType?: InteractionType;
  /** 是否可见 */
  visible?: boolean;
  /** 是否可交互 */
  interactive?: boolean;
  /** 交互距离 */
  interactionDistance?: number;
  /** 交互标签 */
  label?: string;
  /** 交互提示 */
  prompt?: string;
  /** 交互声音 */
  interactionSound?: string;
  /** 高亮颜色 */
  highlightColor?: string;
  /** 交互回调 */
  onInteract?: InteractionCallback;
}

/**
 * 可交互组件
 * 用于标记可交互的对象
 */
export class InteractableComponent extends Component {
  /** 交互类型 */
  private _interactionType: InteractionType;

  /** 是否可见 */
  private _visible: boolean;

  /** 是否可交互 */
  private _interactive: boolean;

  /** 交互距离 */
  private _interactionDistance: number;

  /** 交互标签 */
  private _label: string;

  /** 交互提示 */
  private _prompt: string;

  /** 交互声音 */
  private _interactionSound?: string;

  /** 高亮颜色 */
  private _highlightColor: string;

  /** 是否高亮 */
  private _highlighted: boolean = false;

  /** 事件发射器 */
  private eventEmitter: EventEmitter = new EventEmitter();

  /** 交互回调 */
  private onInteractCallback?: InteractionCallback;

  /** 世界位置缓存 */
  private _worldPosition: Vector3 = new Vector3();

  /** 上次更新时间 */
  private lastUpdateTime: number = 0;

  /**
   * 构造函数
   * @param config 组件配置
   */
  constructor(config: InteractableComponentConfig = {}) {
    // 调用基类构造函数，传入组件类型名称
    super('Interactable', {
      enabled: true,
      updatePriority: 0,
      serializable: true
    });

    // 初始化属性
    this._interactionType = config.interactionType || InteractionType.CLICK;
    this._visible = config.visible !== undefined ? config.visible : true;
    this._interactive = config.interactive !== undefined ? config.interactive : true;
    this._interactionDistance = config.interactionDistance || 2.0;
    this._label = config.label || '';
    this._prompt = config.prompt || '按E键交互';
    this._interactionSound = config.interactionSound;
    this._highlightColor = config.highlightColor || '#ffff00';

    // 设置交互回调
    if (config.onInteract) {
      this.onInteractCallback = config.onInteract;
    }
  }

  /**
   * 获取交互类型
   */
  get interactionType(): InteractionType {
    return this._interactionType;
  }

  /**
   * 设置交互类型
   */
  set interactionType(value: InteractionType) {
    this._interactionType = value;
  }

  /**
   * 获取是否可见
   */
  get visible(): boolean {
    return this._visible;
  }

  /**
   * 设置是否可见
   */
  set visible(value: boolean) {
    this._visible = value;
  }

  /**
   * 获取是否可交互
   */
  get interactive(): boolean {
    return this._interactive;
  }

  /**
   * 设置是否可交互
   */
  set interactive(value: boolean) {
    this._interactive = value;
  }

  /**
   * 获取交互距离
   */
  get interactionDistance(): number {
    return this._interactionDistance;
  }

  /**
   * 设置交互距离
   */
  set interactionDistance(value: number) {
    this._interactionDistance = value;
  }

  /**
   * 获取交互标签
   */
  get label(): string {
    return this._label;
  }

  /**
   * 设置交互标签
   */
  set label(value: string) {
    this._label = value;
  }

  /**
   * 获取交互提示
   */
  get prompt(): string {
    return this._prompt;
  }

  /**
   * 设置交互提示
   */
  set prompt(value: string) {
    this._prompt = value;
  }

  /**
   * 获取交互声音
   */
  get interactionSound(): string | undefined {
    return this._interactionSound;
  }

  /**
   * 设置交互声音
   */
  set interactionSound(value: string | undefined) {
    this._interactionSound = value;
  }

  /**
   * 获取高亮颜色
   */
  get highlightColor(): string {
    return this._highlightColor;
  }

  /**
   * 设置高亮颜色
   */
  set highlightColor(value: string) {
    this._highlightColor = value;
  }

  /**
   * 获取是否高亮
   */
  get highlighted(): boolean {
    return this._highlighted;
  }

  /**
   * 设置高亮状态
   * @param highlighted 是否高亮
   */
  setHighlighted(highlighted: boolean): void {
    if (this._highlighted === highlighted) return;

    this._highlighted = highlighted;

    // 触发高亮变化事件
    this.eventEmitter.emit('highlightChanged', { entity: this.getEntity(), highlighted });

    // 更新对象的高亮效果
    this.updateHighlight();
  }

  /**
   * 更新高亮效果
   */
  private updateHighlight(): void {
    const entity = this.getEntity();
    if (!entity) return;

    // 查找实体上的InteractionHighlightComponent组件
    const highlightComponent = entity.getComponent('InteractionHighlight') as any as any;

    // 如果存在高亮组件，则设置其高亮状态
    if (highlightComponent) {
      (highlightComponent as any).highlighted = this._highlighted;
    } else {
      // 如果没有高亮组件，但实体有mesh属性（用于示例代码兼容）
      const mesh = (entity as any).mesh;
      if (mesh && mesh.material) {
        if (this._highlighted) {
          // 保存原始材质
          if (!('_originalMaterial' in this)) {
            (this as any)._originalMaterial = mesh.material;
          }

          // 创建高亮材质
          const highlightMaterial = new MeshBasicMaterial({
            color: this._highlightColor,
            transparent: true,
            opacity: 0.7,
            wireframe: false,
            depthTest: true
          });

          // 应用高亮材质
          mesh.material = highlightMaterial;
        } else if ('_originalMaterial' in this) {
          // 恢复原始材质
          mesh.material = (this as any)._originalMaterial;
        }
      }
    }
  }

  /**
   * 获取世界位置
   * @returns 世界位置
   */
  getWorldPosition(): Vector3 {
    // 从实体的变换组件中获取世界位置
    const transform = this.getEntity()?.getComponent('Transform') as any as any as Transform;
    if (transform) {
      // 使用变换组件的getWorldPosition方法获取世界位置
      this._worldPosition.copy(transform.getWorldPosition());
    }
    return this._worldPosition.clone();
  }

  /**
   * 交互
   */
  interact(): void {
    // 如果不可交互，则返回
    if (!this._interactive) return;

    // 触发交互事件
    this.eventEmitter.emit('interact', { entity: this.getEntity() });

    // 调用交互回调
    if (this.onInteractCallback) {
      this.onInteractCallback(this.getEntity()!);
    }
  }

  /**
   * 添加事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  on(event: string, callback: EventCallback): this {
    this.eventEmitter.on(event, callback);
    return this;
  }

  /**
   * 移除事件监听器
   * @param event 事件名称
   * @param callback 回调函数
   */
  off(event: string, callback?: EventCallback): this {
    this.eventEmitter.off(event, callback);
    return this;
  }

  /**
   * 更新组件
   * @param deltaTime 时间增量（秒）
   */
  protected onUpdate(deltaTime: number): void {
    // 更新上次更新时间
    this.lastUpdateTime += deltaTime;

    // 每隔一段时间更新世界位置
    if (this.lastUpdateTime >= 0.1) {
      this.lastUpdateTime = 0;

      // 更新世界位置
      this.getWorldPosition();
    }
  }

  /**
   * 销毁组件
   */
  dispose(): void {
    // 移除所有事件监听器
    this.eventEmitter.removeAllListeners();

    // 调用基类的销毁方法
    super.dispose();
  }

  /**
   * 创建组件实例
   * @returns 新的组件实例
   */
  protected createInstance(): Component {
    return new InteractableComponent();
  }

  /**
   * 序列化自定义数据
   * @returns 自定义数据
   */
  protected serializeData(): any {
    return {
      interactionType: this._interactionType,
      visible: this._visible,
      interactive: this._interactive,
      interactionDistance: this._interactionDistance,
      label: this._label,
      prompt: this._prompt,
      interactionSound: this._interactionSound,
      highlightColor: this._highlightColor,
      highlighted: this._highlighted
    };
  }

  /**
   * 反序列化自定义数据
   * @param data 自定义数据
   */
  protected deserializeData(data: any): void {
    if (data.interactionType !== undefined) this._interactionType = data.interactionType;
    if (data.visible !== undefined) this._visible = data.visible;
    if (data.interactive !== undefined) this._interactive = data.interactive;
    if (data.interactionDistance !== undefined) this._interactionDistance = data.interactionDistance;
    if (data.label !== undefined) this._label = data.label;
    if (data.prompt !== undefined) this._prompt = data.prompt;
    if (data.interactionSound !== undefined) this._interactionSound = data.interactionSound;
    if (data.highlightColor !== undefined) this._highlightColor = data.highlightColor;
    if (data.highlighted !== undefined) this._highlighted = data.highlighted;
  }
}
